{"cells": [{"cell_type": "markdown", "source": ["#**<PERSON><PERSON><PERSON><PERSON>**"], "metadata": {"id": "SUKUXK9h8XgL"}, "id": "SUKUXK9h8XgL"}, {"cell_type": "markdown", "source": ["LangChain is a framework for developing applications powered by language models.\n", "\n", "- GitHub: https://github.com/hwchase17/langchain\n", "- Docs: https://python.langchain.com/v0.2/docs/introduction/\n", "\n", "### Overview:\n", "- Installation\n", "- LLMs\n", "- Prompt Templates\n", "- Chains\n", "- Agents and Tools\n", "- Memory\n", "- Document Loaders\n", "- Indexes"], "metadata": {"id": "qo1DQXM18bgL"}, "id": "qo1DQXM18bgL"}, {"cell_type": "markdown", "source": ["#**01: Installation**"], "metadata": {"id": "09CgA1RZkiC4"}, "id": "09CgA1RZkiC4"}, {"cell_type": "code", "source": ["!pip install langchain langchain_community"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "X4tDdLTjkkk_", "outputId": "ad6a4acc-980f-4f4a-80ce-68720f04a9e0"}, "id": "X4tDdLTjkkk_", "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain\n", "  Downloading langchain-0.3.0-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting langchain_community\n", "  Downloading langchain_community-0.3.0-py3-none-any.whl.metadata (2.8 kB)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (6.0.2)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.0.34)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (3.10.5)\n", "Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (4.0.3)\n", "Collecting langchain-core<0.4.0,>=0.3.0 (from langchain)\n", "  Downloading langchain_core-0.3.0-py3-none-any.whl.metadata (6.2 kB)\n", "Collecting langchain-text-splitters<0.4.0,>=0.3.0 (from langchain)\n", "  Downloading langchain_text_splitters-0.3.0-py3-none-any.whl.metadata (2.3 kB)\n", "Collecting langsmith<0.2.0,>=0.1.17 (from langchain)\n", "  Downloading langsmith-0.1.120-py3-none-any.whl.metadata (13 kB)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.26.4)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.9.1)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.32.3)\n", "Collecting tenacity!=8.4.0,<9.0.0,>=8.1.0 (from langchain)\n", "  Downloading tenacity-8.5.0-py3-none-any.whl.metadata (1.2 kB)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain_community)\n", "  Downloading dataclasses_json-0.6.7-py3-none-any.whl.metadata (25 kB)\n", "Collecting pydantic-settings<3.0.0,>=2.4.0 (from langchain_community)\n", "  Downloading pydantic_settings-2.5.2-py3-none-any.whl.metadata (3.5 kB)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (2.4.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (24.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (6.1.0)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.11.1)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain_community)\n", "  Downloading marshmallow-3.22.0-py3-none-any.whl.metadata (7.2 kB)\n", "Collecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain_community)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl.metadata (1.5 kB)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain-core<0.4.0,>=0.3.0->langchain)\n", "  Downloading jsonpatch-1.33-py2.py3-none-any.whl.metadata (3.0 kB)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4.0,>=0.3.0->langchain) (24.1)\n", "Requirement already satisfied: typing-extensions>=4.7 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4.0,>=0.3.0->langchain) (4.12.2)\n", "Collecting httpx<1,>=0.23.0 (from langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading httpx-0.27.2-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting <PERSON><PERSON><PERSON><4.0.0,>=3.9.14 (from langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading orjson-3.10.7-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (50 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m50.4/50.4 kB\u001b[0m \u001b[31m4.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.23.3 in /usr/local/lib/python3.10/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain) (2.23.3)\n", "Collecting python-dotenv>=0.21.0 (from pydantic-settings<3.0.0,>=2.4.0->langchain_community)\n", "  Downloading python_dotenv-1.0.1-py3-none-any.whl.metadata (23 kB)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2024.8.30)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.1.0)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (3.7.1)\n", "Collecting httpcore==1.* (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl.metadata (20 kB)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (1.3.1)\n", "Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain)\n", "  Downloading h11-0.14.0-py3-none-any.whl.metadata (8.2 kB)\n", "Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain-core<0.4.0,>=0.3.0->langchain)\n", "  Downloading jsonpointer-3.0.0-py2.py3-none-any.whl.metadata (2.3 kB)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain_community)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl.metadata (1.1 kB)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (1.2.2)\n", "Downloading langchain-0.3.0-py3-none-any.whl (1.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.0/1.0 MB\u001b[0m \u001b[31m28.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading langchain_community-0.3.0-py3-none-any.whl (2.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.3/2.3 MB\u001b[0m \u001b[31m74.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading dataclasses_json-0.6.7-py3-none-any.whl (28 kB)\n", "Downloading langchain_core-0.3.0-py3-none-any.whl (405 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m405.1/405.1 kB\u001b[0m \u001b[31m33.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading langchain_text_splitters-0.3.0-py3-none-any.whl (25 kB)\n", "Downloading langsmith-0.1.120-py3-none-any.whl (289 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m289.8/289.8 kB\u001b[0m \u001b[31m25.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pydantic_settings-2.5.2-py3-none-any.whl (26 kB)\n", "Downloading tenacity-8.5.0-py3-none-any.whl (28 kB)\n", "Downloading httpx-0.27.2-py3-none-any.whl (76 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m76.4/76.4 kB\u001b[0m \u001b[31m7.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m6.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Downloading marshmallow-3.22.0-py3-none-any.whl (49 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.3/49.3 kB\u001b[0m \u001b[31m4.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading orjson-3.10.7-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (141 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m141.9/141.9 kB\u001b[0m \u001b[31m13.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading python_dotenv-1.0.1-py3-none-any.whl (19 kB)\n", "Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Downloading jsonpointer-3.0.0-py2.py3-none-any.whl (7.6 kB)\n", "Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Downloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m5.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: tenacity, python-dotenv, orjson, mypy-extensions, marshmallow, jsonpointer, h11, typing-inspect, jsonpatch, httpcore, pydantic-settings, httpx, dataclasses-json, langsmith, langchain-core, langchain-text-splitters, langchain, langchain_community\n", "  Attempting uninstall: tenacity\n", "    Found existing installation: tenacity 9.0.0\n", "    Uninstalling tenacity-9.0.0:\n", "      Successfully uninstalled tenacity-9.0.0\n", "Successfully installed dataclasses-json-0.6.7 h11-0.14.0 httpcore-1.0.5 httpx-0.27.2 jsonpatch-1.33 jsonpointer-3.0.0 langchain-0.3.0 langchain-core-0.3.0 langchain-text-splitters-0.3.0 langchain_community-0.3.0 langsmith-0.1.120 marshmallow-3.22.0 mypy-extensions-1.0.0 orjson-3.10.7 pydantic-settings-2.5.2 python-dotenv-1.0.1 tenacity-8.5.0 typing-inspect-0.9.0\n"]}]}, {"cell_type": "markdown", "source": ["#**02: Setup the Environment**"], "metadata": {"id": "sQHZiF38-Cps"}, "id": "sQHZiF38-Cps"}, {"cell_type": "code", "source": ["import os"], "metadata": {"id": "9-mFf0Ql-KX2"}, "id": "9-mFf0Ql-KX2", "execution_count": 3, "outputs": []}, {"cell_type": "code", "execution_count": 4, "id": "f31c4cc6", "metadata": {"id": "f31c4cc6"}, "outputs": [], "source": ["os.environ['OPENAI_API_KEY'] = \"************************************************************************************************************************************\"\n", "\n", "os.environ[\"HUGGINGFACEHUB_API_TOKEN\"] = \"*************************************\""]}, {"cell_type": "markdown", "id": "9ed0dc6a", "metadata": {"id": "9ed0dc6a"}, "source": ["##**03: Large Language Models**"]}, {"cell_type": "markdown", "source": ["The basic building block of LangChain is a Large Language Model which takes text as input and generates more text"], "metadata": {"id": "516GZwvpnVpV"}, "id": "516GZwvpnVpV"}, {"cell_type": "markdown", "source": ["Suppose we want to generate a company name based on the company description, so we will first initialize an OpenAI wrapper. In this case, since we want the output to be more random, we will intialize our model with high temprature."], "metadata": {"id": "4FDyNMY3sRMc"}, "id": "4FDyNMY3sRMc"}, {"cell_type": "markdown", "source": ["The temperature parameter adjusts the randomness of the output. Higher values like 0.7 will make the output more random, while lower values like 0.2 will make it more focused and deterministic."], "metadata": {"id": "eLqFwlXaH8f4"}, "id": "eLqFwlXaH8f4"}, {"cell_type": "markdown", "source": ["temperature value--> how creative we want our model to be\n", "\n", "0 ---> temperature it means model is  very safe it is not taking any bets.\n", "\n", "1 --> it will take risk it might generate wrong output but it is very creative"], "metadata": {"id": "rMOonq5OH97v"}, "id": "rMOonq5OH97v"}, {"cell_type": "markdown", "source": ["A generic interface for all LLMs. See all LLM providers: https://python.langchain.com/en/latest/modules/models/llms/integrations.html"], "metadata": {"id": "M9Y34zmZ8xyc"}, "id": "M9Y34zmZ8xyc"}, {"cell_type": "markdown", "source": ["#**Open AI**"], "metadata": {"id": "TB5tAUbT92Z7"}, "id": "TB5tAUbT92Z7"}, {"cell_type": "markdown", "source": ["#**Example 1**"], "metadata": {"id": "BszO_ZXrs95T"}, "id": "BszO_ZXrs95T"}, {"cell_type": "code", "source": ["!pip install openai"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "w-az-0Ex9CaD", "outputId": "5e56a21b-2286-4c24-9af2-b34cecd18017"}, "id": "w-az-0Ex9CaD", "execution_count": 5, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting openai\n", "  Downloading openai-1.45.0-py3-none-any.whl.metadata (22 kB)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.10/dist-packages (from openai) (3.7.1)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai) (1.7.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.10/dist-packages (from openai) (0.27.2)\n", "Collecting jiter<1,>=0.4.0 (from openai)\n", "  Downloading jiter-0.5.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.6 kB)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /usr/local/lib/python3.10/dist-packages (from openai) (2.9.1)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from openai) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /usr/local/lib/python3.10/dist-packages (from openai) (4.66.5)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in /usr/local/lib/python3.10/dist-packages (from openai) (4.12.2)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->openai) (3.8)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->openai) (1.2.2)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->openai) (2024.8.30)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->openai) (1.0.5)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai) (0.14.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1.9.0->openai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.23.3 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1.9.0->openai) (2.23.3)\n", "Downloading openai-1.45.0-py3-none-any.whl (374 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m374.1/374.1 kB\u001b[0m \u001b[31m12.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading jiter-0.5.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (318 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m318.9/318.9 kB\u001b[0m \u001b[31m24.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: jiter, openai\n", "Successfully installed jiter-0.5.0 openai-1.45.0\n"]}]}, {"cell_type": "code", "source": ["from langchain.llms import OpenAI\n", "llm = OpenAI(temperature=0.9)"], "metadata": {"id": "lJEy652utDdM", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "7b22584d-d6ff-4960-bcee-2a978fe8a33c"}, "id": "lJEy652utDdM", "execution_count": 6, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["<ipython-input-6-6ad3029f0965>:2: LangChainDeprecationWarning: The class `OpenAI` was deprecated in LangChain 0.0.10 and will be removed in 1.0. An updated version of the class exists in the langchain-openai package and should be used instead. To use it run `pip install -U langchain-openai` and import as `from langchain_openai import OpenAI`.\n", "  llm = OpenAI(temperature=0.9)\n"]}]}, {"cell_type": "markdown", "source": ["And now we will pass in text and get  predictions"], "metadata": {"id": "n_nF4R5EtN_k"}, "id": "n_nF4R5EtN_k"}, {"cell_type": "code", "source": ["text=\"What would be a good company name for a company that makes colorful socks?\""], "metadata": {"id": "VIUqmBl3tUgj"}, "id": "VIUqmBl3tUgj", "execution_count": 8, "outputs": []}, {"cell_type": "code", "source": ["print(llm.predict(text))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "g7itCa0q9rn7", "outputId": "13cf4864-4d03-486e-ec8a-d204c4d1220b"}, "id": "g7itCa0q9rn7", "execution_count": 9, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["<ipython-input-9-652b389f66d4>:1: Lang<PERSON>hainDeprecationWarning: The method `BaseLLM.predict` was deprecated in langchain-core 0.1.7 and will be removed in 1.0. Use invoke instead.\n", "  print(llm.predict(text))\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\"Rainbow Threads\" or \"Vibrant Socks Co.\"\n"]}]}, {"cell_type": "code", "source": ["print(llm(text))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2KE0Fngs9daM", "outputId": "e8ef4852-f282-4760-f1e0-356f280ee163"}, "id": "2KE0Fngs9daM", "execution_count": 10, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["<ipython-input-10-605043303f29>:1: LangChainDeprecationWarning: The method `BaseLLM.__call__` was deprecated in langchain-core 0.1.7 and will be removed in 1.0. Use invoke instead.\n", "  print(llm(text))\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\"Rainbow Socks Co.\" or \"Vibrant Feet Co.\"\n"]}]}, {"cell_type": "code", "source": ["print(llm.invoke(text))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-s5lupvjFLVz", "outputId": "92c1cfef-c8dd-424d-84f7-98f7a3ac83c2"}, "id": "-s5lupvjFLVz", "execution_count": 11, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "Here are five potential options for company names that could work for a business that makes colorful socks: \n", "\n", "1. \"Spectrum Socks\": This name plays off of the idea of a spectrum, or a range of colors, which could be a nod to the variety of hues that your company offers in its sock designs. \n", "\n", "2. \"Rainbow Threads\": This name has a fun, whimsical feel, which could make it a good fit for a sock company. It also incorporates the idea of a rainbow, which is often associated with a wide array of colors. \n", "\n", "3. \"Vib<PERSON>\": This name is straightforward and descriptive, clearly indicating that your company specializes in colorful socks. \n", "\n", "4. \"Socktastic\": This made-up word is playful and unique, and it could make for a memorable company name. \n", "\n", "5. \"Chromatic Socks\": This name is slightly more sophisticated, using the word \"chromatic\" to describe the colorful nature of your company's products.\n"]}]}, {"cell_type": "markdown", "source": ["#**Example 2**"], "metadata": {"id": "EJIQT1FSn0Gl"}, "id": "EJIQT1FSn0Gl"}, {"cell_type": "code", "execution_count": 12, "id": "fa352d5f", "metadata": {"scrolled": true, "colab": {"base_uri": "https://localhost:8080/"}, "id": "fa352d5f", "outputId": "355c64b7-0400-4b1a-fe79-f52193a4b12d"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\"Imperial Dragon Palace\"\n"]}], "source": ["from langchain.llms import OpenAI\n", "\n", "llm = OpenAI(temperature=0.9)\n", "name = llm.predict(\"I want to open a restaurant for Chinese food. Suggest a fency name for this.\")\n", "print(name)"]}, {"cell_type": "code", "execution_count": 13, "id": "b56e8581", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "b56e8581", "outputId": "bdfae42c-1d40-4b9c-eb1a-718de7b5757c"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\"Dragonfire Dining\"\n"]}], "source": ["response=llm(\"I want to open a restaurant for Chinese food. Suggest a fency name for this.\")\n", "print(response)"]}, {"cell_type": "markdown", "source": ["#**Hugging Face**"], "metadata": {"id": "bj6wjnKZ-bgU"}, "id": "bj6wjnKZ-bgU"}, {"cell_type": "markdown", "source": ["#**Example 1**"], "metadata": {"id": "iTsUW116-th1"}, "id": "iTsUW116-th1"}, {"cell_type": "code", "source": ["!pip install huggingface_hub"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "hDMLw7Yr-nQK", "outputId": "287688c8-816d-4c9f-f2c2-14e138537538"}, "id": "hDMLw7Yr-nQK", "execution_count": 14, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: huggingface_hub in /usr/local/lib/python3.10/dist-packages (0.24.6)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (3.16.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (2024.6.1)\n", "Requirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (24.1)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (6.0.2)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (2.32.3)\n", "Requirement already satisfied: tqdm>=4.42.1 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (4.66.5)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (4.12.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface_hub) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface_hub) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface_hub) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface_hub) (2024.8.30)\n"]}]}, {"cell_type": "code", "source": ["from langchain import HuggingFaceHub"], "metadata": {"id": "B4w0ultA-icd"}, "id": "B4w0ultA-icd", "execution_count": 15, "outputs": []}, {"cell_type": "code", "source": ["# https://huggingface.co/google/flan-t5-xl\n", "\n", "llm = HuggingFaceHub(repo_id=\"google/flan-t5-large\", model_kwargs={\"temperature\":0, \"max_length\":64})\n", "\n", "llm(\"translate English to German: How old are you?\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 90}, "id": "W-pl8cXk-ie7", "outputId": "7b8a4854-2e4d-4a7f-8ce6-1b94d3b6a994"}, "id": "W-pl8cXk-ie7", "execution_count": 16, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["<ipython-input-16-4d87b8e65d57>:3: LangChainDeprecationWarning: The class `HuggingFaceHub` was deprecated in LangChain 0.0.21 and will be removed in 1.0. An updated version of the class exists in the langchain-huggingface package and should be used instead. To use it run `pip install -U langchain-huggingface` and import as `from langchain_huggingface import HuggingFaceEndpoint`.\n", "  llm = HuggingFaceHub(repo_id=\"google/flan-t5-large\", model_kwargs={\"temperature\":0, \"max_length\":64})\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'Wie alte sind Si<PERSON>?'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 16}]}, {"cell_type": "markdown", "source": ["#**Example 2**"], "metadata": {"id": "2MOh4uIm-xDQ"}, "id": "2MOh4uIm-xDQ"}, {"cell_type": "code", "source": ["from langchain import HuggingFaceHub\n", "\n", "llm = HuggingFaceHub(repo_id=\"google/flan-t5-large\", model_kwargs={\"temperature\":0, \"max_length\":64})\n", "# name = llm.predict(\"I want to open a restaurant for Chinese food. Suggest a fency name for this.\")\n", "name = llm.predict(\"I want to open a restaurant for Indian food. Suggest a fency name for this.\")\n", "print(name)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dmAwr5-d-z6F", "outputId": "f86e804c-ee6c-4868-f0ca-97f301304d6f"}, "id": "dmAwr5-d-z6F", "execution_count": 20, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Indian restaurant\n"]}]}, {"cell_type": "markdown", "id": "0782a2dd", "metadata": {"id": "0782a2dd"}, "source": ["##**04: Prompt Templates**"]}, {"cell_type": "markdown", "source": ["Currently in the above applications we are writing an entire prompt, if you are creating a user directed application then this is not an ideal case\n", "\n", "<PERSON><PERSON><PERSON><PERSON> faciliates prompt management and optimization.\n", "\n", "Normally when you use an LLM in an application, you are not sending user input directly to the LLM. Instead, you need to take the user input and construct a prompt, and only then send that to the LLM."], "metadata": {"id": "jszTHb6J_dNV"}, "id": "jszTHb6J_dNV"}, {"cell_type": "markdown", "source": ["In many Large Language Model applications we donot pass the user input directly to the Large Language Model, we add the user input to a large piece of text called prompt template"], "metadata": {"id": "unU1DcEv7TWh"}, "id": "unU1DcEv7TWh"}, {"cell_type": "markdown", "source": ["#**Example 1**"], "metadata": {"id": "IWqka6F_93QB"}, "id": "IWqka6F_93QB"}, {"cell_type": "code", "execution_count": 22, "id": "7a306b9d", "metadata": {"scrolled": true, "colab": {"base_uri": "https://localhost:8080/"}, "id": "7a306b9d", "outputId": "7c72aac5-20ad-4113-84ae-cb3a8d8c7d99"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["I want to open a restaurant for indian food. Suggest a fency name for this.\n"]}], "source": ["from langchain.prompts import PromptTemplate\n", "\n", "prompt_template_name = PromptTemplate(\n", "    input_variables =['cuisine'],\n", "    template = \"I want to open a restaurant for {cuisine} food. Suggest a fency name for this.\"\n", ")\n", "p = prompt_template_name.format(cuisine=\"indian\")\n", "print(p)"]}, {"cell_type": "markdown", "source": ["#**Example 2**"], "metadata": {"id": "qlKeWd7B95-R"}, "id": "qlKeWd7B95-R"}, {"cell_type": "code", "source": ["from langchain.prompts import PromptTemplate\n", "prompt = PromptTemplate.from_template(\"What is a good name for a company that makes {product}\")\n", "prompt.format(product=\"colorful socks\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "qqJZBS9u8534", "outputId": "82018ddc-2af8-4a7d-f3e6-3d5ab9579bc9"}, "id": "qqJZBS9u8534", "execution_count": 23, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'What is a good name for a company that makes colorful socks'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 23}]}, {"cell_type": "code", "source": [], "metadata": {"id": "O3YEt1YZKJHr"}, "id": "O3YEt1YZKJHr", "execution_count": null, "outputs": []}, {"cell_type": "markdown", "id": "af406b92", "metadata": {"id": "af406b92"}, "source": ["##**05: Chains**"]}, {"cell_type": "markdown", "source": ["Combine LLMs and Prompts in multi-step workflows"], "metadata": {"id": "vGaSSUAIBHdU"}, "id": "vGaSSUAIBHdU"}, {"cell_type": "markdown", "source": ["Now as we have the  **model**:\n", "\n", "\n", "  llm = OpenAI(temperature=0.9)\n", "\n", "\n", "and the **Prompt Template**:\n", "\n", "prompt = PromptTemplate.from_template(\"What is a good name for a company that makes {product}\")\n", "\n", "\n", "prompt.format(product=\"colorful socks\")\n", "\n", "\n", "Now using Chains we will link together model and the PromptTemplate and other Chains"], "metadata": {"id": "lcjlXP7z_-k6"}, "id": "lcjlXP7z_-k6"}, {"cell_type": "markdown", "source": ["The simplest and most common type of Chain is LLMChain, which passes the input first to Prompt Template and then to Large Language Model"], "metadata": {"id": "mIJx5zL2BbHJ"}, "id": "mIJx5zL2BbHJ"}, {"cell_type": "markdown", "source": ["LLMChain is responsible to execute the PromptTemplate, For every PromptTemplate we will specifically have an LLMChain"], "metadata": {"id": "5icZHtlDFrpI"}, "id": "5icZHtlDFrpI"}, {"cell_type": "markdown", "source": ["#**Example 1**"], "metadata": {"id": "MAUSugfLCZH-"}, "id": "MAUSugfLCZH-"}, {"cell_type": "code", "source": ["from langchain.llms import OpenAI\n", "\n", "llm = OpenAI(temperature=0.9)"], "metadata": {"id": "22NEqcvGGvHJ"}, "id": "22NEqcvGGvHJ", "execution_count": 37, "outputs": []}, {"cell_type": "code", "source": ["from langchain.prompts import PromptTemplate\n", "prompt = PromptTemplate.from_template(\"What is a good name for a company that makes {product}\")\n", "prompt.format(product=\"colorful socks\")"], "metadata": {"id": "bK-KESsGGOhY", "colab": {"base_uri": "https://localhost:8080/", "height": 35}, "outputId": "2301fc7b-8508-401f-8196-66978d6bc0f0"}, "id": "bK-KESsGGOhY", "execution_count": 41, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'What is a good name for a company that makes colorful socks'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 41}]}, {"cell_type": "markdown", "source": ["Whatever input text i am giving that will get assigned to this particular variable that is **product**"], "metadata": {"id": "8KjGw4iXGUGJ"}, "id": "8KjGw4iXGUGJ"}, {"cell_type": "code", "source": ["from langchain.chains import LLMChain\n", "\n", "chain = LLMChain(llm=llm, prompt=prompt)\n", "response= chain.run(\"colorful socks\")\n", "print(response)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1gatUl_ICZOP", "outputId": "744d4ffb-e8b3-47f8-cd70-acdc9b0756dc"}, "id": "1gatUl_ICZOP", "execution_count": 40, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\"Rainbow Threads\" or \"ChromaSocks\"\n"]}]}, {"cell_type": "markdown", "source": ["#**Example 2**"], "metadata": {"id": "O93s1iRICXNv"}, "id": "O93s1iRICXNv"}, {"cell_type": "code", "source": ["from langchain.llms import OpenAI\n", "\n", "llm = OpenAI(temperature=0.9)"], "metadata": {"id": "qV_H_EGCG-OR"}, "id": "qV_H_EGCG-OR", "execution_count": 42, "outputs": []}, {"cell_type": "code", "source": ["from langchain.prompts import PromptTemplate\n", "\n", "prompt_template_name = PromptTemplate(\n", "    input_variables =['cuisine'],\n", "    template = \"I want to open a restaurant for {cuisine} food. Suggest a fency name for this.\"\n", ")"], "metadata": {"id": "uLtIkYe6G7xK"}, "id": "uLtIkYe6G7xK", "execution_count": 43, "outputs": []}, {"cell_type": "code", "execution_count": 44, "id": "ba65c213", "metadata": {"scrolled": true, "colab": {"base_uri": "https://localhost:8080/"}, "id": "ba65c213", "outputId": "d004f5b6-5733-44b2-bb5a-3229941a99ab"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\"El Sabor de México\" (The Flavor of Mexico)\n"]}], "source": ["from langchain.chains import LLMChain\n", "\n", "chain = LLMChain(llm=llm, prompt=prompt_template_name)\n", "response=chain.run(\"Mexican\")\n", "print(response)"]}, {"cell_type": "code", "execution_count": 45, "id": "e5ccee75", "metadata": {"scrolled": true, "colab": {"base_uri": "https://localhost:8080/"}, "id": "e5ccee75", "outputId": "47d15f2d-aecf-4056-e43a-8b1e24cfd19b"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["WARNING:langchain_core.callbacks.manager:<PERSON><PERSON><PERSON> in StdOutCallbackHandler.on_chain_start callback: AttributeError(\"'NoneType' object has no attribute 'get'\")\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mI want to open a restaurant for Mexican food. Suggest a fency name for this.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "\n", "\"La Casa de Sabores\" (The House of Flavors)\n"]}], "source": ["chain = LLMChain(llm=llm, prompt=prompt_template_name, verbose=True)\n", "response=chain.run(\"Mexican\")\n", "print(response)"]}, {"cell_type": "markdown", "source": ["**Can we combine Multiple PromptTemplates, We will try to combine Multiple PromptTemplates**"], "metadata": {"id": "EMd9OQVNH7lK"}, "id": "EMd9OQVNH7lK"}, {"cell_type": "markdown", "source": ["**The output from the first PromptTemplate is passed to the next PromptTemplate as input**"], "metadata": {"id": "nv_tlKtLJLIZ"}, "id": "nv_tlKtLJLIZ"}, {"cell_type": "markdown", "source": ["#**To combine the Chain and  to set a sequence for that we use SimpleSequentialChain**"], "metadata": {"id": "a-6_6H-BJl9L"}, "id": "a-6_6H-BJl9L"}, {"cell_type": "markdown", "id": "87a98d9f", "metadata": {"id": "87a98d9f"}, "source": ["##**Simple Sequential Chain**"]}, {"cell_type": "code", "execution_count": 46, "id": "21098937", "metadata": {"id": "21098937"}, "outputs": [], "source": ["llm = OpenAI(temperature=0.6)\n", "\n", "prompt_template_name = PromptTemplate(\n", "    input_variables =['cuisine'],\n", "    template = \"I want to open a restaurant for {cuisine} food. Suggest a fency name for this.\"\n", ")\n", "\n", "name_chain =LLMChain(llm=llm, prompt=prompt_template_name)\n", "\n", "prompt_template_items = PromptTemplate(\n", "    input_variables = ['restaurant_name'],\n", "    template=\"\"\"Suggest some menu items for {restaurant_name}\"\"\"\n", ")\n", "\n", "food_items_chain = LLMChain(llm=llm, prompt=prompt_template_items)"]}, {"cell_type": "code", "execution_count": 47, "id": "d9fd9a79", "metadata": {"scrolled": false, "colab": {"base_uri": "https://localhost:8080/"}, "id": "d9fd9a79", "outputId": "fa05334c-f200-49b9-d246-3d4f73510214"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "1. <PERSON><PERSON><PERSON>\n", "2. Hot and Sour Soup\n", "3. <PERSON>\n", "4. <PERSON><PERSON>\n", "5. <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>\n", "6. General <PERSON>'s Chicken\n", "7. <PERSON>\n", "8. <PERSON>pic<PERSON> <PERSON><PERSON>\n", "9. Firec<PERSON> Shrimp\n", "10. Spicy Sichuan Green Beans\n", "11. Five Spice Pork Belly\n", "12. <PERSON><PERSON><PERSON> <PERSON>\n", "13. <PERSON><PERSON><PERSON><PERSON>\n", "14. <PERSON><PERSON><PERSON>\n", "15. Spicy Stir-Fried Cabbage\n", "16. <PERSON><PERSON><PERSON> <PERSON>\n", "17. <PERSON>picy Kung <PERSON>\n", "18. <PERSON><PERSON><PERSON><PERSON>\n", "19. <PERSON>picy S<PERSON>an Noodles\n", "20. <PERSON><PERSON> with <PERSON> and Spicy <PERSON>.\n"]}], "source": ["from langchain.chains import SimpleSequentialChain\n", "chain = SimpleSequentialChain(chains = [name_chain, food_items_chain])\n", "\n", "content = chain.run(\"indian\")\n", "print(content)"]}, {"cell_type": "markdown", "source": ["**There is a issue with SimpleSequentialChain it only shows last input information**"], "metadata": {"id": "njqmmiouJ6Uc"}, "id": "njqmmiouJ6Uc"}, {"cell_type": "markdown", "source": ["#**To show the entire information i will use Sequential<PERSON>hain**"], "metadata": {"id": "hKVVpZo8KC38"}, "id": "hKVVpZo8KC38"}, {"cell_type": "markdown", "id": "0386d05c", "metadata": {"id": "0386d05c"}, "source": ["##**Sequential Chain**"]}, {"cell_type": "code", "execution_count": 48, "id": "49dc0fae", "metadata": {"id": "49dc0fae"}, "outputs": [], "source": ["llm = OpenAI(temperature=0.7)\n", "\n", "prompt_template_name = PromptTemplate(\n", "    input_variables =['cuisine'],\n", "    template = \"I want to open a restaurant for {cuisine} food. Suggest a fency name for this.\"\n", ")\n", "\n", "name_chain =LLMChain(llm=llm, prompt=prompt_template_name, output_key=\"restaurant_name\")"]}, {"cell_type": "code", "execution_count": 49, "id": "9dea8402", "metadata": {"id": "9dea8402"}, "outputs": [], "source": ["llm = OpenAI(temperature=0.7)\n", "\n", "prompt_template_items = PromptTemplate(\n", "    input_variables = ['restaurant_name'],\n", "    template=\"Suggest some menu items for {restaurant_name}.\"\n", ")\n", "\n", "food_items_chain =LLMChain(llm=llm, prompt=prompt_template_items, output_key=\"menu_items\")"]}, {"cell_type": "code", "execution_count": 50, "id": "1ec1be10", "metadata": {"id": "1ec1be10"}, "outputs": [], "source": ["from langchain.chains import SequentialChain\n", "\n", "chain = SequentialChain(\n", "    chains = [name_chain, food_items_chain],\n", "    input_variables = ['cuisine'],\n", "    output_variables = ['restaurant_name', \"menu_items\"]\n", ")"]}, {"cell_type": "code", "execution_count": 51, "id": "4653c540", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "4653c540", "outputId": "7d69312f-1a54-4f94-a92c-9629a2423c03"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["{'cuisine': 'indian', 'restaurant_name': '\\n\\n\"Spice Palace\"', 'menu_items': '\\n\\n1. Spicy Chicken Tikka Masala\\n2. Vegetable Samosas\\n3. Lamb Vindaloo\\n4. <PERSON><PERSON> (chickpea curry)\\n5. Tandoori Grilled Shrimp\\n6. <PERSON><PERSON> (potato and cauliflower curry)\\n7. Garlic Na<PERSON> bread\\n8. Mango <PERSON> (yogurt drink)\\n9. <PERSON><PERSON> (spinach and cheese curry)\\n10. Chicken Biryani (spiced rice dish)\\n11. Ma<PERSON>a Dosa (crispy rice crepe filled with spiced potatoes)\\n12. Vegetable Korma (mixed vegetable curry)\\n13. <PERSON><PERSON><PERSON> (fried dough balls in sweet syrup)\\n14. Tandoori Chicken (marinated grilled chicken)\\n15. Butter Chicken (creamy tomato-based curry)'}\n"]}], "source": ["print(chain({\"cuisine\": \"indian\"}))"]}, {"cell_type": "markdown", "id": "4069a75e", "metadata": {"id": "4069a75e"}, "source": ["##**06. Agents and Tools**\n", "\n", "Agents involve an LLM making decisions about which Actions to take, taking that Action, seeing an Observation, and repeating that until done.\n", "\n", "\n", "When used correctly agents can be extremely powerful. In order to load agents, you should understand the following concepts:\n", "\n", "- Tool: A function that performs a specific duty. This can be things like: Google Search, Database lookup, Python REPL, other chains.\n", "- LLM: The language model powering the agent.\n", "- Agent: The agent to use.\n"]}, {"cell_type": "markdown", "source": ["Agent is a very powerful concept in LangChain"], "metadata": {"id": "Z-4QjS31LD_s"}, "id": "Z-4QjS31LD_s"}, {"cell_type": "markdown", "source": ["For example I have to travel from Dubai to Canada, I type this in ChatGPT\n", "\n", "\n", "\n", "---> Give me  two flight options from Dubai to Canada on September 1, 2024 | ChatGPT will not be able to answer because has knowledge till\n", "September 2021\n", "\n", "\n", "\n", "ChatGPT plus has Expedia Plugin, if we enable this plugin it will go to Expedia Plugin and will try to pull information about Flights & it will show the information"], "metadata": {"id": "GgNLQ6kSL4na"}, "id": "GgNLQ6kSL4na"}, {"cell_type": "markdown", "source": ["SerpApi is a real-time API to access Google search results."], "metadata": {"id": "tkzApnDnJy8p"}, "id": "tkzApnDnJy8p"}, {"cell_type": "markdown", "id": "09cd3a12", "metadata": {"id": "09cd3a12"}, "source": ["#### Wikipedia and llm-math tool"]}, {"cell_type": "code", "source": ["!pip install wikipedia"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TpJ3gA4YZKMx", "outputId": "74d92f6c-8f46-4d5a-b01d-4aafc01df22c"}, "id": "TpJ3gA4YZKMx", "execution_count": 63, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: wikipedia in /usr/local/lib/python3.10/dist-packages (1.4.0)\n", "Requirement already satisfied: beautifulsoup4 in /usr/local/lib/python3.10/dist-packages (from wikipedia) (4.12.3)\n", "Requirement already satisfied: requests<3.0.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from wikipedia) (2.32.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.0.0->wikipedia) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.0.0->wikipedia) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.0.0->wikipedia) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.0.0->wikipedia) (2024.8.30)\n", "Requirement already satisfied: soupsieve>1.2 in /usr/local/lib/python3.10/dist-packages (from beautifulsoup4->wikipedia) (2.6)\n"]}]}, {"cell_type": "code", "source": ["from langchain.agents import AgentType, initialize_agent, load_tools\n", "from langchain.llms import OpenAI"], "metadata": {"id": "kb7ZpkpcVIYO"}, "id": "kb7ZpkpcVIYO", "execution_count": 64, "outputs": []}, {"cell_type": "code", "source": ["llm = OpenAI(temperature=0)"], "metadata": {"id": "UvJNqWvTVKcd"}, "id": "UvJNqWvTVKcd", "execution_count": 65, "outputs": []}, {"cell_type": "code", "execution_count": 67, "id": "14d06ce6", "metadata": {"scrolled": false, "id": "14d06ce6", "outputId": "eee796fb-5a14-4252-8e8a-7be7207415c4", "colab": {"base_uri": "https://localhost:8080/", "height": 524}}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["WARNING:langchain_core.callbacks.manager:<PERSON><PERSON><PERSON> in StdOutCallbackHandler.on_chain_start callback: AttributeError(\"'NoneType' object has no attribute 'get'\")\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\u001b[32;1m\u001b[1;3m I should use Wikipedia to find the answer\n", "Action: wikipedia\n", "Action Input: GDP of US in 2024\u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3mPage: List of U.S. states and territories by GDP\n", "Summary: This is a list of U.S. states and territories by gross domestic product (GDP). This article presents the 50 U.S. states and the District of Columbia and their nominal GDP at current prices.\n", "The data source for the list is the Bureau of Economic Analysis (BEA) in 2024. The BEA defined GDP by state as \"the sum of value added from all industries in the state.\"\n", "Nominal GDP does not take into account differences in the cost of living in different countries, and the results can vary greatly from one year to another based on fluctuations in the exchange rates of the country's currency. Such fluctuations may change a country's ranking from one year to the next, even though they often make little or no difference in the standard of living of its population.\n", "Overall, in the calendar year 2024, the United States' Nominal GDP at Current Prices totaled at $28.269 trillion, as compared to $25.744 trillion in 2022.\n", "The three U.S. states with the highest GDPs were California ($3.987 trillion), Texas ($2.664 trillion), and New York ($2.226 trillion). The three U.S. states with the lowest GDPs were Vermont ($44.4 billion), Wyoming ($51.4 billion), and Alaska ($69.2 billion).\n", "GDP per capita also varied widely throughout the United States in 2024, with New York ($114,380), Massachusetts ($108,185), and North Dakota ($93,983) recording the three highest GDP per capita figures in the U.S., while Mississippi ($50,907), Arkansas ($58,449), and West Virginia ($57,857) recorded the three lowest GDP per capita figures in the U.S. The District of Columbia, though, recorded a GDP per capita figure far higher than any U.S. state in 2024 at $263,220.\n", "\n", "Page: List of countries by GDP (nominal) per capita\n", "Summary: The figures presented here do not take into account differences in the cost of living in different countries, and the results vary greatly from one year to another based on fluctuations in the exchange rates of the country's currency. Such fluctuations change a country's ranking from one year to the next, even though they often make little or no difference to the standard of living of its population.\n", "GDP per capita is often considered an indicator of a country's standard of living; however, this is inaccurate because GDP per capita is not a measure of personal income.\n", "Comparisons of national income are also frequently made on the basis of purchasing power parity (PPP), to adjust for differences in the cost of living in different countries (see List of countries by GDP (PPP) per capita). PPP largely removes the exchange rate problem but not others; it does not reflect the value of economic output in international trade, and it also requires more estimation than GDP per capita. On the whole, PPP per capita figures are more narrowly spread than nominal GDP per capita figures.\n", "Non-sovereign entities (the world, continents, and some dependent territories) and states with limited international recognition are included in the list in cases in which they appear in the sources. These economies are not ranked in the charts here (except Kosovo and Taiwan), but are listed in sequence by GDP for comparison. In addition, non-sovereign entities are marked in italics.\n", "Four UN members (Cuba, Liechtenstein, Monaco and North Korea) do not belong to the International Monetary Fund (IMF), hence their economies are not ranked below. Kosovo, despite not being a member of the United Nations, is a member of IMF. Taiwan is not a IMF member but it is still listed in the official IMF indices.\n", "Several leading GDP-per-capita (nominal) jurisdictions may be considered tax havens, and their GDP data subject to material distortion by tax-planning activities. Examples include Bermuda, the Cayman Islands, Ireland and Luxembourg.\n", "All data are in current United States dollars. Historical data can be found here.\n", "\n", "Page: List of countries by GDP (nominal)\n", "Summary: Gross domestic product (GDP) is the market value of all final goods an\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m I now know the final answer\n", "Final Answer: The GDP of US in 2024 was $28.269 trillion.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'The GDP of US in 2024 was $28.269 trillion.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 67}], "source": ["# install this package: pip install wikipedia\n", "\n", "# The tools we'll give the Agent access to. Note that the 'llm-math' tool uses an LLM, so we need to pass that in.\n", "tools = load_tools([\"wikipedia\", \"llm-math\"], llm=llm)\n", "\n", "# Finally, let's initialize an agent with the tools, the language model, and the type of agent we want to use.\n", "agent = initialize_agent(\n", "    tools,\n", "    llm,\n", "    agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,\n", "    verbose=True\n", ")\n", "\n", "# Let's test it out!\n", "\n", "\n", "agent.run(\"What was the GDP of US in 2024?\")"]}, {"cell_type": "markdown", "id": "b6be7ee7", "metadata": {"id": "b6be7ee7"}, "source": ["##**07: Memory**"]}, {"cell_type": "markdown", "source": ["Chatbot application like ChatGPT, you will notice that it remember past information"], "metadata": {"id": "-WkJqQzRZaXL"}, "id": "-WkJqQzRZaXL"}, {"cell_type": "code", "source": ["from langchain.llms import OpenAI\n", "\n", "llm = OpenAI(temperature=0.9)"], "metadata": {"id": "Iqunzha0Ztuz"}, "id": "Iqunzha0Ztuz", "execution_count": 86, "outputs": []}, {"cell_type": "code", "source": ["from langchain.prompts import PromptTemplate\n", "\n", "prompt_template_name = PromptTemplate(\n", "    input_variables =['cuisine'],\n", "    template = \"I want to open a restaurant for {cuisine} food. Suggest a fency name for this.\"\n", ")"], "metadata": {"id": "NE-poGM1Zxss"}, "id": "NE-poGM1Zxss", "execution_count": 87, "outputs": []}, {"cell_type": "code", "execution_count": 88, "id": "2acab5d0", "metadata": {"id": "2acab5d0", "outputId": "49615fa1-90f1-4eaa-ccd1-a46842a776df", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "1. \"La Cantina de México\"\n", "2. \"El Sabor Auténtico\"\n", "3. \"Casa de la Comida Mexicana\"\n", "4. \"Viva México Cocina\"\n", "5. \"The Mexican Kitchen\"\n", "6. \"Fiesta Mexicana\"\n", "7. \"Sabores de México\"\n", "8. \"El Rincón Mexicano\"\n", "9. \"La Cocina de Abuela\"\n", "10. \"La Hacienda Mexicana\"\n"]}], "source": ["from langchain.chains import LLMChain\n", "\n", "chain = LLMChain(llm=llm,prompt=prompt_template_name)\n", "name = chain.run(\"Mexican\")\n", "print(name)"]}, {"cell_type": "code", "execution_count": 89, "id": "5bc200f9", "metadata": {"id": "5bc200f9", "outputId": "d077dc9a-733f-464a-aad8-62b7e5ef7cf9", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "1. \"Spice Kingdom\"\n", "2. \"Tandoori House\"\n", "3. \"Curry Palace\"\n", "4. \"<PERSON>an <PERSON>\"\n", "5. \"Masala Magic\"\n", "6. \"<PERSON><PERSON><PERSON> Ba<PERSON>\"\n", "7. \"Mughlai Delight\"\n", "8. \"<PERSON><PERSON> & Chaat\"\n", "9. \"Punjabi Grill\"\n", "10. \"The Bollywood Bistro\"\n"]}], "source": ["name = chain.run(\"Indian\")\n", "print(name)"]}, {"cell_type": "code", "execution_count": 90, "id": "229a6888", "metadata": {"id": "229a6888"}, "outputs": [], "source": ["chain.memory"]}, {"cell_type": "code", "execution_count": 91, "id": "f492fb5a", "metadata": {"scrolled": true, "id": "f492fb5a", "outputId": "f54b8c42-21ae-4b7a-d748-99a8e0db5c1e", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["NoneType"]}, "metadata": {}, "execution_count": 91}], "source": ["type(chain.memory)"]}, {"cell_type": "markdown", "id": "871492be", "metadata": {"id": "871492be"}, "source": ["##**ConversationBufferMemory**"]}, {"cell_type": "markdown", "source": ["We can attach memory to remember all previous conversation"], "metadata": {"id": "coQKpk8jZ8zz"}, "id": "coQKpk8jZ8zz"}, {"cell_type": "code", "execution_count": 92, "id": "53eea298", "metadata": {"id": "53eea298", "outputId": "d5c0226a-a886-44be-8fa9-b464a7ed18b9", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\"Cantina de Oro\" (meaning \"Gold Cantina\")\n"]}], "source": ["from langchain.memory import ConversationBufferMemory\n", "\n", "memory = ConversationBufferMemory()\n", "\n", "chain = LLMChain(llm=llm, prompt=prompt_template_name, memory=memory)\n", "name = chain.run(\"Mexican\")\n", "print(name)"]}, {"cell_type": "code", "execution_count": 93, "id": "0de5d50b", "metadata": {"id": "0de5d50b", "outputId": "cb1d1234-5bdf-4bf8-ed4d-77307ff3254e", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\"Al-Fawwarah\" (The Oasis)\n"]}], "source": ["name = chain.run(\"Arabic\")\n", "print(name)"]}, {"cell_type": "code", "execution_count": 94, "id": "5cc88888", "metadata": {"scrolled": true, "id": "5cc88888", "outputId": "f01949dc-618b-47bc-93a1-a50734773cd7", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Human: Mexican\n", "AI: \n", "\n", "\"Cantina de Oro\" (meaning \"Gold Cantina\")\n", "Human: Arabic\n", "AI: \n", "\n", "\"Al-Fawwarah\" (The Oasis)\n"]}], "source": ["print(chain.memory.buffer)"]}, {"cell_type": "markdown", "id": "a0a88b5b", "metadata": {"id": "a0a88b5b"}, "source": ["##**ConversationChain**"]}, {"cell_type": "markdown", "source": ["Conversation buffer memory goes growing endlessly\n", "\n", "Just remember last 5 Conversation Chain\n", "\n", "Just remember last 10-20 Conversation Chain"], "metadata": {"id": "FyFmOOemaVxb"}, "id": "FyFmOOemaVxb"}, {"cell_type": "code", "execution_count": 95, "id": "687ddd2f", "metadata": {"id": "687ddd2f", "outputId": "57a8866c-7bee-4138-db77-a97d4d9b91ce", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "{history}\n", "Human: {input}\n", "AI:\n"]}], "source": ["from langchain.chains import ConversationChain\n", "\n", "convo = ConversationChain(llm=OpenAI(temperature=0.7))\n", "print(convo.prompt.template)"]}, {"cell_type": "code", "execution_count": 96, "id": "47ad5062", "metadata": {"id": "47ad5062", "outputId": "460ed904-f145-4a27-8b5e-51c3b567ada5", "colab": {"base_uri": "https://localhost:8080/", "height": 104}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["\" The first cricket world cup was won by India in 1983. The final match took place at Lord's Cricket Ground in London, and India beat the West Indies by 43 runs. The Indian cricket team was captained by <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> was awarded the Man of the Match for his all-round performance. This victory was a major upset as the West Indies had won the previous two world cups and were considered unbeatable at the time. Do you have any other questions about the cricket world cup?\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 96}], "source": ["convo.run(\"Who won the first cricket world cup?\")"]}, {"cell_type": "code", "execution_count": 97, "id": "03c80b54", "metadata": {"id": "03c80b54", "outputId": "b3d62890-1d0e-481a-cefd-18a2dc2db867", "colab": {"base_uri": "https://localhost:8080/", "height": 35}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'  5+5 is equal to 10. Do you have any other questions?'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 97}], "source": ["convo.run(\"How much is 5+5?\")"]}, {"cell_type": "code", "execution_count": 98, "id": "07342f88", "metadata": {"id": "07342f88", "outputId": "0ab4f1ab-b98b-45b3-ac60-dab12d4fd2fd", "colab": {"base_uri": "https://localhost:8080/", "height": 35}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["' The captain of the winning team, India, was <PERSON><PERSON><PERSON>. Do you have any other questions?'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 98}], "source": ["convo.run(\"Who was the captain of the winning team?\")"]}, {"cell_type": "code", "execution_count": 99, "id": "4e459d07", "metadata": {"id": "4e459d07", "outputId": "24a349d0-c018-4a3d-face-3847168134af", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Human: Who won the first cricket world cup?\n", "AI:  The first cricket world cup was won by India in 1983. The final match took place at Lord's Cricket Ground in London, and India beat the West Indies by 43 runs. The Indian cricket team was captained by <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> was awarded the Man of the Match for his all-round performance. This victory was a major upset as the West Indies had won the previous two world cups and were considered unbeatable at the time. Do you have any other questions about the cricket world cup?\n", "Human: How much is 5+5?\n", "AI:   5+5 is equal to 10. Do you have any other questions?\n", "Human: Who was the captain of the winning team?\n", "AI:  The captain of the winning team, India, was <PERSON><PERSON><PERSON>. Do you have any other questions?\n"]}], "source": ["print(convo.memory.buffer)"]}, {"cell_type": "markdown", "id": "feaa3abd", "metadata": {"id": "feaa3abd"}, "source": ["##**ConversationBufferWindowMemory**"]}, {"cell_type": "code", "execution_count": 103, "id": "460eb33c", "metadata": {"id": "460eb33c", "outputId": "83ac07a2-254a-4f95-9c15-8799f2aef5d1", "colab": {"base_uri": "https://localhost:8080/", "height": 52}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["\" The first cricket world cup was won by the West Indies in 1975. The final match was played between West Indies and Australia at Lord's Cricket Ground in London, England. West Indies won by 17 runs.\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 103}], "source": ["from langchain.memory import ConversationBufferWindowMemory\n", "\n", "memory = ConversationBufferWindowMemory(k=3)\n", "\n", "convo = ConversationChain(\n", "    llm=OpenAI(temperature=0.7),\n", "    memory=memory\n", ")\n", "convo.run(\"Who won the first cricket world cup?\")"]}, {"cell_type": "code", "execution_count": 104, "id": "d395beaf", "metadata": {"id": "d395beaf", "outputId": "ce645a79-c300-4222-bf33-7a260e56a22b", "colab": {"base_uri": "https://localhost:8080/", "height": 35}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'  5+5 is equal to 10.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 104}], "source": ["convo.run(\"How much is 5+5?\")"]}, {"cell_type": "code", "execution_count": 106, "id": "93b24745", "metadata": {"id": "93b24745", "outputId": "acf0c9f3-c6f1-4376-92dd-54f4a8927a5e", "colab": {"base_uri": "https://localhost:8080/", "height": 52}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["' The captain of the West Indies team during the 1975 cricket world cup was <PERSON>. He was known for his aggressive batting and leadership skills, and he led the team to victory in the first two cricket world cups in 1975 and 1979.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 106}], "source": ["convo.run(\"Who was the captain of the winning team?\")"]}, {"cell_type": "code", "source": ["print(convo.memory.buffer)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "K63Ie5FTvjzo", "outputId": "de320f7f-ff80-49cc-b5cc-0ee89d442a00"}, "id": "K63Ie5FTvjzo", "execution_count": 107, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Human: How much is 5+5?\n", "AI:   5+5 is equal to 10.\n", "Human: Who was the captain of the winning team?\n", "AI:  The captain of the West Indies team during the 1975 cricket world cup was <PERSON>. He was known for his aggressive batting and leadership skills, and he led the team to victory in the first two cricket world cups in 1975 and 1979.\n", "Human: Who was the captain of the winning team?\n", "AI:  The captain of the West Indies team during the 1975 cricket world cup was <PERSON>. He was known for his aggressive batting and leadership skills, and he led the team to victory in the first two cricket world cups in 1975 and 1979.\n"]}]}, {"cell_type": "markdown", "source": ["#**08: Document Loaders**\n"], "metadata": {"id": "mkFhYXKUmnDO"}, "id": "mkFhYXKUmnDO"}, {"cell_type": "code", "source": ["!pip install pypdf"], "metadata": {"id": "-9WXxhHCZtTn", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "f54f01e1-b31a-4e4c-8d56-86917e6d1822"}, "id": "-9WXxhHCZtTn", "execution_count": 108, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting pypdf\n", "  Downloading pypdf-4.3.1-py3-none-any.whl.metadata (7.4 kB)\n", "Requirement already satisfied: typing_extensions>=4.0 in /usr/local/lib/python3.10/dist-packages (from pypdf) (4.12.2)\n", "Downloading pypdf-4.3.1-py3-none-any.whl (295 kB)\n", "\u001b[?25l   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/295.8 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K   \u001b[91m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[91m╸\u001b[0m\u001b[90m━\u001b[0m \u001b[32m286.7/295.8 kB\u001b[0m \u001b[31m9.8 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m295.8/295.8 kB\u001b[0m \u001b[31m6.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: pypdf\n", "Successfully installed pypdf-4.3.1\n"]}]}, {"cell_type": "code", "source": ["from langchain.document_loaders import PyPDFLoader\n", "\n", "loader = PyPDFLoader(\"/content/my_paper.pdf\")\n", "pages = loader.load()"], "metadata": {"id": "wqlRJc7DmtwA"}, "id": "wqlRJc7DmtwA", "execution_count": 109, "outputs": []}, {"cell_type": "code", "source": ["pages"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XGTtdS26mt0_", "outputId": "c2a6adbe-7308-4838-f0d6-ecb712bf8802"}, "id": "XGTtdS26mt0_", "execution_count": 110, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(metadata={'source': '/content/my_paper.pdf', 'page': 0}, page_content='See discussions, st ats, and author pr ofiles f or this public ation at : https://www .researchgate.ne t/public ation/357213035\\nDevelopment of Multiple Combined Regression Methods for Rainfall\\nMeasu rement Development of Multiple Combined Regression Methods for\\nRainfall Measu rement\\nArticle  · Dec ember 2021\\nCITATIONS\\n0READS\\n711\\n6 author s, including:\\nNusr at Jahan Pr ottasha\\nDaff odil Int ernational Univ ersity\\n26 PUBLICA TIONS \\xa0\\xa0\\xa0299 CITATIONS \\xa0\\xa0\\xa0\\nSEE PROFILE\\nMd K owsher\\nStevens Instit ute of T echnolog y\\n73 PUBLICA TIONS \\xa0\\xa0\\xa0561 CITATIONS \\xa0\\xa0\\xa0\\nSEE PROFILE\\nRokeya Khat un Shorna\\nJahangirnag ar Univ ersity\\n6 PUBLICA TIONS \\xa0\\xa0\\xa05 CITATIONS \\xa0\\xa0\\xa0\\nSEE PROFILE\\nNiaz Mur shed\\nJahangirnag ar Univ ersity\\n3 PUBLICA TIONS \\xa0\\xa0\\xa00 CITATIONS \\xa0\\xa0\\xa0\\nSEE PROFILE\\nAll c ontent f ollo wing this p age was uplo aded b y Niaz Mur shed  on 21 Dec ember 2021.\\nThe user has r equest ed enhanc ement of the do wnlo aded file.'),\n", " Document(metadata={'source': '/content/my_paper.pdf', 'page': 1}, page_content='See discussions, st ats, and author pr ofiles f or this public ation at : https://www .researchgate.ne t/public ation/354831740\\nDevelopment of Multiple Combined Regression Methods for Rainfall\\nMeasu rement\\nChapt er · Januar y 2021\\nDOI: 10.52458/978-93-91842-08-6-7\\nCITATIONS\\n0READS\\n47\\n6 author s, including:\\nSome o f the author s of this public ation ar e also w orking on these r elat ed pr ojects:\\nBangla NLP  View pr oject\\nImp act le arning Implement ation  View pr oject\\nNusr at Jahan Pr ottasha\\nDaff odil Int ernational Univ ersity\\n15 PUBLICA TIONS \\xa0\\xa0\\xa012 CITATIONS \\xa0\\xa0\\xa0\\nSEE PROFILE\\nMd. K owsher\\nStevens Instit ute of T echnolog y\\n57 PUBLICA TIONS \\xa0\\xa0\\xa094 CITATIONS \\xa0\\xa0\\xa0\\nSEE PROFILE\\nAll c ontent f ollo wing this p age was uplo aded b y Md. K owsher  on 06 Dec ember 2021.\\nThe user has r equest ed enhanc ement of the do wnlo aded file.'),\n", " Document(metadata={'source': '/content/my_paper.pdf', 'page': 2}, page_content='Development of Multiple Combined Regression\\nMethods for Rainfall Measurement.\\nN<PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>\\n<PERSON>horna4, <PERSON><PERSON>5, and <PERSON><PERSON><PERSON>6\\n1Daﬀodil International University Dhaka 1207, Bangladesh,\\<EMAIL>\\n2Noakhali Science and Technology University, 3814, Dhaka,\\<EMAIL>\\n3Stevens Institute of Technology, Hoboken, NJ 07030 USA,\\<EMAIL>\\n4Daﬀodil International University, 1207, Dhaka,\\<EMAIL>\\n5Jahangirnagar University, 1342, Dhaka,\\<EMAIL>\\n6Jhenaidah polytechnic institute, 7300, Dhaka,\\<EMAIL>\\nAbstract. Rainfall forecast is imperative as overwhelming precipitation\\ncan lead to numerous catastrophes. The prediction makes a diﬀerence for\\nindividuals to require preventive measures. In addition, the expectation\\nought to be precise. Most of the nations in the world is an agricultural\\nnation and most of the economy of any nation depends upon agriculture.\\nRain plays an imperative part in agribusiness so the early expectation of\\nrainfall plays a vital part within the economy of any agricultural. Over-\\nwhelming precipitation may well be a major disadvantage. It’s a cause\\nfor natural disasters like ﬂoods and drought that unit of measurement\\nexperienced by people over the world each year. Rainfall forecast has\\nbeen one of the foremost challenging issues around the world in the ﬁnal\\nyear. There are so many techniques that have been invented for predict-\\ning rainfall but most of them are classiﬁcation, clustering techniques.\\nPredicting the quantity of rain prediction is crucial for countries’ people.\\nIn our paperwork, we have proposed some regression analysis techniques\\nwhich can be utilized for predicting the quantity of rainfall (The amount\\nof rainfall recorded for the day in mm) based on some historical weather\\nconditions dataset. we have applied 10 supervised regressors (Machine\\nLearning Model) and some preprocessing methodology to the dataset.\\nWe have also analyzed the result and compared them using various sta-\\ntistical parameters among these trained models to ﬁnd the bestperformed\\nmodel. Using this model for predicting the quantity of rainfall in some\\ndiﬀerent places. Finally, the Random Forest regressor has predicted the\\nbest r2 score of 0.869904217, and the mean absolute error is 0.194459262,\\nmean squared error is 0.126358647 and the root mean squared error is\\n0.355469615. . .'),\n", " Document(metadata={'source': '/content/my_paper.pdf', 'page': 3}, page_content='2 <PERSON><PERSON><PERSON> et al.\\nKeywords: Rainfall, Supervised Learning, Regression, Random Forest\\nTree, AdaBoost Regressor, Gradient Boosting Regressor, XGBoost.\\n1 Introduction\\nThis research paper proposed a scientiﬁc method to predict rainfall quantity\\nbased on some diﬀerent weather conditions considering preceding weather records\\nand present weather situations using some regression analysis techniques .[1]\\nRainfall determining is exceptionally vital since overwhelming and irregular rain-\\nfall can have numerous impacts on many other things like annihilation of river-\\nbank, crops, agriculture, and farms. One of the very deleterious departures is\\nﬂooding due to the over rain.[2] According to Wikipedia in late summer 2002,\\nenormous storm downpours driven to gigantic ﬂooding in eastern India, Nepal,\\nand Bangladesh, killing over 500 individuals and clearing out millions of houses.\\nEach year in Bangladesh approximately 26,000 square kilometers (10,000 sq mi)\\n(around 18% of the country) is ﬂooded, killing over 5,000 individuals and wreck-\\ning more than 7 million homes. On the other hand, Western Sydney is now\\nthe ”greatest concern” from the worst ﬂoods in decades to have ravaged east-\\nern Australia.[3] <PERSON><PERSON>, <PERSON> et al. presented a very rational method of the\\nrainfall measurement problem. The application of science and innovation that\\npredicts the state of the environment at any given speciﬁc period is known as\\nclimate determining or weather forecasting. There are many distinctive strate-\\ngies for climate estimate and weather forecasting. But rainfall prediction is rare.\\nSome of the research has shown some classiﬁcation method to predict whether\\nit would be rain tomorrow or not. But instead of a classiﬁcation method for pre-\\ndicting rain, we need to the quantity of the rainfall in a particular place. There\\nis numerous equipment implement for foreseeing rainfall by utilizing the climate\\nconditions like temperature, humidity, weight. These conventional strategies can-\\nnot work productively so by utilizing machine learning procedures. we can create\\nan exact comes about rain forecast. Ready to fair do it by having the histori-\\ncal information investigation of rainfall and can anticipate the precipitation for\\nfuture seasons. In our paper, we presented some predictive regression analysis\\ntechniques to quantify rainfall quantity at a place. Here we used more than 10\\nyears of historical data to train our model. The dataset contains various weather\\nconditions of diﬀerent places. This method can be utilized to predict the rainfall\\n(The amount of rainfall recorded for the day in mm) and avoid the annihilation\\ncaused by it to life, agriculture, farm, and property. If we can quantify the rain-\\nfall most people can make some decisions before overwhelmed rain-aﬀected. The\\ncontributions of this work are summarised as:\\n–We have assessed a pipeline of making choices for evaluating the ﬁnest rea-\\nsonable rain prediction.\\n–We have utilized 10 supervised regressors (Machine Learning Model). Be-\\ncause diﬀerent regressors give us diﬀerent results. So, it’s essential to ﬁnd\\nout the right model according to the requirements.'),\n", " Document(metadata={'source': '/content/my_paper.pdf', 'page': 4}, page_content='Rainfall Prediction 3\\n–We have discussed a big comparison among all trained models to ﬁgure out\\nthe best performer.\\nThe paper is organized as takes after: Section II clariﬁes the related work of\\ndiﬀerent classiﬁcation strategies for the forecast of rain classiﬁcation. Section-III\\ndepicts the technique and materials utilized. Section-IV depicts the experimental\\nanalysis including performance and result. Section V talks about the conclusion\\nof this research work where section VI described about the plan of future.\\n2 Related Works\\nIn this paper, through a systematic investigation <PERSON><PERSON> et al. have presented the\\nrainfall measurement problem, they claim there’s an orderly mistake in the esti-\\nmation of precipitation made in an ordinary way, a mistake which may inﬂuence\\nany gauges utilizing these estimations.[3] Besides <PERSON><PERSON><PERSON><PERSON> et al. proposed a\\nmethod that speaks to a numerical strategy called Linear Regression to antici-\\npate the rainfall in diﬀerent areas in southern states of India.[4] To improvement\\nWang et al. showed a case study they proposed an application of generalized\\nregression neural network (GRNN) model to anticipate yearly precipitation in\\nZhengzhou .[5] On the other hand, <PERSON><PERSON> et al. presented an exploiting data min-\\ning technique for the early prediction of rainfall called multiple linear regression\\n(MLR) .[6] <PERSON><PERSON><PERSON> et al. presented a divide and conquer approach to\\npredict the rainfall based on the locational information only .[7] Also, Bagirov,\\nM Adil et al. developed the Clusterwise Linear Regression (CLR) technique for\\nthe prediction of monthly rainfall .[8] In addition, Mohammed Moulana et al.\\nrepresented machine learning techniques to precipitation prediction the purpose\\nof this project is to oﬀer non-experts simple get to the methods, approaches\\nutilized within the division of precipitation forecast and give a comparative\\nthink about among the diﬀerent machine learning methods.[9] P, Asha, et al\\nproposed a mutual neural classiﬁcation model for predicting rainfall. [10] S, Sak-\\nthivel, et al described neural networks and the rapid miner-based rain prediction\\nsystem.[11] Diwakar, Naidu, et al presented the changes in rainfall patterns in\\nnumerous agro-climatic zones using machine learning approaches.[12] Besides,\\nTuan Vu, Dinh, et al utilized an LSHADE-PWI-SVM method for the integra-\\ntion of machine learning classiﬁers conjointly metaheuristic optimization .[13]\\nOn the other hand, Malathi, R, et al showed a Information Gain based Feature\\nSelection Method for Weather Dataset for the prediction of rainfall.[14] Also,\\nNor, SamsiahSani, et al. evaluated many machine learning classiﬁers based on\\nMalaysian data for rainfall prediction.[15] David, Ahijevych, et al presented a\\nrandom forest (RF) that is utilized to produce 2-h ﬁgures of the probability\\nfor the start of mesoscale convective frameworks (MCS-I).[16] John T, Allen,\\net al performed property and agribusiness, as well as handfuls of fatalities and\\nWonders related to extreme electrical storms.[15][17] Harold E, Brooks, et al, dis-\\nplayed the current dissemination of serious rainstorms as a work of large-scale\\nnatural conditions.[18] Pierre, Gentine, et al. Representing uncertain sodden con-\\nvection in coarse-scale climate models remains one of the most bottlenecks of'),\n", " Document(metadata={'source': '/content/my_paper.pdf', 'page': 5}, page_content='4 <PERSON><PERSON><PERSON> et al.\\ncurrent climate recreations.[19] <PERSON><PERSON><PERSON><PERSON><PERSON>, et al. described the participation of\\nthe pivotal for agriculture-dependent.[20] <PERSON><PERSON> et al, Represented to reduce the\\nrisk of life and also maintain the agriculture farms in a better way [21] Then,\\n<PERSON><PERSON><PERSON> et al. elucidates farmers to take early measurements of ﬂoods, and\\nmanage the water resources properly.[22] <PERSON> et al, discussed to related this\\ntask to predict rain.[23]\\n3 Methodology\\nTo perform the complete technique, we assume the four signiﬁcant steps such as\\ndata collection, data pre-processing, training model using 10 supervised regres-\\nsors, and execution examination. Within the information collection step, we have\\nused a dataset7from the Kaggle platform which has been split into two parts\\nsuch as the training part and validation part. Here we have utilized one of the\\nvalidation parts as the testing data to evaluate the models’ performance. Each\\nrow has various weights for decision making to suggest the sensible best rain\\nprediction. Afterward, gathering all raw data, ﬁrstly we would be made ready\\nfor the training model with the help of data pre-processing techniques and this\\nhas been used for outliers free and more rigid. It also assists to increase the per-\\nformance of the models. As a result, we have applied six pre-processing methods\\nsuch as cleaning data, missing value check, handling the categorical data, han-\\ndling outliers, handling outliers, feature selection. Next, to establish supervised\\nregressors models, we utilized the regressors such as Linear Regression, Ridge\\nRegression, Polynomial Regression, and Lasso Regression. From all the training\\nmethods we have used a total of 10 regressors so that we can compare the per-\\nformance and ﬁgure out the best model. Most of the regressors come up with a\\ngood performance. We have described the whole methodology in the ﬁgure 1:\\nData Collection\\nData Cleaning\\nData Analysis\\nPreprocessingHandling missing value \\nHandling categorical data\\nHandling outliersFeature scalingFeature SelectionSpliting Data\\nTrain Data\\nModel Teﬆ\\nData\\nTrained\\nModel \\nPrediction\\nFig. 1. The whole methodology of rainfall prediction including all important steps\\nsuch as data collection, necessary preprocessing, and training model with performance\\nprediction\\n7https://www.kaggle.com/jsphyg/weather-dataset-rattle-package'),\n", " Document(metadata={'source': '/content/my_paper.pdf', 'page': 6}, page_content='Rainfall Prediction 5\\nTable 1. Considering feature’s description of dataset\\nFeature name Description\\nLocation The common title of the area of the climate\\nstation.\\nMinTemp The least temperature in degrees centigrade.\\nMaxTemp The most extreme temperature in degrees\\ncentigrade.\\nRainfall The sum of precipitation recorded for the day\\nin millimeters.\\nWindGustDir The heading of the most grounded wind blast\\nwithin 24 h to midnight.\\nWindGustSpeed The speed (in kilometers per hour) of the\\nstrongest wind blast within 24 h to midnight.\\nWindDir9am The course of the wind blast at 9 a.m.\\nWindSpeed9am Wind speed (km/hr) found the middle value\\nof over 10 minutes sometime recently 9 am.\\nWindSpeed3pm Wind speed (in kilometers per hour) found\\nthe middle value of over 10 min sometime re-\\ncently 3 p.m.\\nHumidity9am Relative humidity at 9 am.\\nHumidity3pm Relative humidity at 3 pm.\\nPressure 9am Climatic weight (hPa) was decreased to cruel\\nocean level at 9 a.m.\\nTemp3pm Temperature (degrees C) at 3 p.m.\\nRain Today Numbers 1 on the oﬀ chance that precipita-\\ntion (in millimeters) within the 24 h to 9 a.m.\\nsurpasses 1 mm, something else 0.\\n3.1 Introduction dataset\\nKindly This dataset contains about 10 years of daily weather observations from\\nmany locations. We have collected this dataset from Kaggle. It is having 23\\ndiverse observation features of weather condition like ’Location’, ’Min Temp’,\\n’MaxTemp’, ’Rainfall’, ’Evaporation’, ’Sunshine’, ’Wind Gust Dir’, ’Wind Gust\\nSpeed’, ’Wind Dir 9am’, ’Wind Dir 3pm’, ’Wind Speed 9am’, ’Wind Speed 3pm’,\\n’Humidity 9am’, ’Humidity 3pm’, ’Pressure 9 am’, ’Pressure 3pm’, ’Cloud 9am’,\\n’Cloud 3pm’, ’Temp 9am’, ’Temp 3pm’, ’Rain Today’. Here,in the table 1 the\\ndescription of the data-set has been illustrated.'),\n", " Document(metadata={'source': '/content/my_paper.pdf', 'page': 7}, page_content='6 <PERSON><PERSON><PERSON> et al.\\n3.2 Pre-Processing\\nIn machine learning, the data preprocessing is within the framework of exchang-\\ning or encoding the crude information in a stage where calculations can be\\neﬀectively implemented to prepare. We ought to preprocess the information con-\\ncurring to create it ﬁt for the machine learning model. Well-processed data gives\\nhigh accuracy and makes the model more solid. Here, we have utilized a few\\nstages of preprocessing strategies, which have been outlined in Figure-2:\\nInput DataData Cleaning\\nRemoved unused\\nfeature\\nRemoved DuplicatesHandling missing values\\nMean & MedianHandling categorical data\\nEDA\\nOne Hot Encoding\\nHandling outliersIQR\\nMethod\\nFeature ScalingStandard Scaler \\nFeature SelectionNumerical \\nCategorical Model Output\\nFig. 2. Data Pre-Processing\\nIn our dataset, there are parcels of unused, null, and duplicate values. For\\nthis reason, we took some steps to handle these issues. such as,\\n–Erased duplicate row and column: we discover that numerous information\\npoints are repeated in row and column sections. Therefore, we expelled all\\nthe duplicate information.\\n–Erased the row and column, which shows up more than 50% of the null value.\\nCleaning data occurs when 50% of information comes to the null value. At\\nthat point, we have chosen to evacuate the whole rows and columns. For\\nthe most part, missing value is characterized as the value which was not put\\naway within the sample. The missing value may be a common occasion in\\ninformation. On the other hand, most prescient modeling strategies can’t\\nhandle any missing value. Thus, this issue must be unraveled before model-\\ning. In some cases, median, mean, mode strategies are utilized to overhaul\\na missing value. In any case, the foremost direct method for managing the\\nmissing value is the mean, median, mode strategy. Here we have utilized this\\nmean, median & mode strategy for managing missing data\\n–Handled categorical features: Categorical data could be a subjective include\\nwhose values are taken on the value of labels. So, we ought to encode this\\nsort of information into numbers so that the machine learning model can'),\n", " Document(metadata={'source': '/content/my_paper.pdf', 'page': 8}, page_content='Rainfall Prediction 7\\nexecute scientiﬁc operations on it. In our dataset, there exist a few categorical\\nfeatures. We have utilized one-hot encoding, one of the foremost prevalent\\nencoding algorithms, to encode the categorical values into numbers. It is the\\nforemost common approach, and it works well unless any categorical variable\\ntakes a large number of diverse values. After this encoding, a double matrix\\nis shaped where 1 indicates the presence of any value and 0 indicates the\\nabsence of the value.\\n–Inside our dataset, there were a lot of outliers presented: an outlier is a\\nperception point that’s removed from other perceptions. An outlier may be\\ndue to variations within the estimation or it may appear exploratory mistake\\nthe latter are some of the time excluded from the set of information. An issue\\nof outliers can cause, they tend to be unaﬀected by littler UI changes that\\ndo inﬂuence a more whimsical standard population. Bulk orders will thrust\\nthrough littler convenience changes in a way that your average visitor may\\nnot. So to handle the outliers we have used the IQR (interquartile range)\\nmethod, which is an eﬃcient technique.\\n–Include scaling is one of the signiﬁcant strategies that are mandatory to\\nstandardize the working data’s independent features. All things considered,\\nthere are diﬀerent strategies like Min-Max Scaling, Variance Scaling, Stan-\\ndardization, Mean Normalization, and Unit vectors for include scaling. In\\nour work, we have applied standard scaling as a feature scaling procedure.\\nHere, the exchanged every data point in the range of between -1 and 1.\\n3.3 Training selective Models\\nThe linear model[24]performs well in machine learning linearly. We utilized the\\nfour regressors as Linear Regression, Ridge Regression, Polynomial Regression,\\nand Lasso Regression. Tree-model [25]algorithms are considered to be one of the\\nleading and most utilized supervised learning methods. In this work, we utilize\\na decision tree regressor. We utilized ”gini” for the Gini impurity, and the split-\\nter is chosen as ’best’ to select the part at each node. Ensemble methods[26]\\nare procedures that make multiple models and combine them to create moves\\nforward. Here, we utilized four ensemble-based regressors. These are Random\\nForest, Gradient Boosting, Adaboost, and XGboost. Afterward, we have utilized\\nthree neighbors regressors of statistical pattern recognition. This is K- nearest\\nneighbors[27],ﬁve nearest is chosen for every iteration. Besides, the Manhat-\\ntan distance is chosen for all neighbor classiﬁers. The support vector machine\\nSVM[28] is used mainly for exploring a hyperplane in ddimensional space that\\nnotably ﬁts a hyperplane in data points. In the linear SVM, we used hinge as\\nloss function with l2 penalty.\\n4 Experiment\\nIn the advancement of our test from the proposed work, we have to begin with\\namassed the demonstrate and prepared it. 10 diﬀerent regressors from super-\\nvised learning based on distinctive learning techniques have been executed to'),\n", " Document(metadata={'source': '/content/my_paper.pdf', 'page': 9}, page_content='8 <PERSON><PERSON><PERSON> et al.\\nModel \\nLinear Model \\n> Linear Regression\\n> Polynomial Regression\\n> Ridge \\n> LassoTree Model \\n> Decision tree     \\n Regressor Neareﬆ Neighbor \\n> KNN Regressor Ensemble Model \\n> Random Foreﬆ\\nRegressor \\n> Adabooﬆ\\n> Gradient Booﬆing\\n> XGBooﬆ\\nSVM\\n> Support V ector \\nMachine\\nFig. 3. Training Algorithms\\nanticipate precipitation’s most pertinent mode. This area depicted distinctive\\ntest errands for the execution investigation and assessment and compared all\\ncalculations. Then, we have outlined the test setup utilized to execute the entire\\nerrand and utilized 11 statistical assessment measurements for investigation ex-\\necution. At long last, we have moreover compared with other works related to\\nthis issue concerning the ﬁnest form of our work.\\n4.1 Experiment Setup\\nwe have completed the complete computation in8google collab, a python reen-\\nactment environment given by Google. This environment comes with parallel\\ncomputation facilities for quick execution. We have utilized the foremost well-\\nknown libraries to create simple and expressive information structures that work\\nwell and instinctively quickly. At long last, sklearn library contains specialized\\nmachine learning and statistical modeling instruments, counting classiﬁcation,\\nregression, and clustering calculations for modeling. We have utilized a machine\\nlearning system named9sci-kit learn to implement the regression algorithm. At\\nlong last, we utilized10matplotlib and11seaborn for information visualization,\\ngraphical representation, additionally for information investigation.\\n8https://colab.research.google.com/\\n9https://scikit-learn.org\\n10https://matplotlib.org\\n11https://seaborn.pydata.org'),\n", " Document(metadata={'source': '/content/my_paper.pdf', 'page': 10}, page_content='Rainfall Prediction 9\\n4.2 Statistical measurement\\nR2 score : The R2 score could be a very critical metric that’s utilized to assess the\\nperformance of a regression based machine learning model. It is articulated as\\nR squared and is additionally known as the coeﬃcient of assurance. It works by\\nmeasuring the sum of variance within the expectations clariﬁed by the dataset.\\nBasically put, it is the contrast between the tests within the dataset and the\\nexpectations made by the demonstrate. As we can see from all models Random\\nForest regressor achieves the best r2 score which is 0.869904217. The second\\nand third positions are achieved by GradientBoostingRegressor and XGBoost\\nwhich are 0.863496747 and 0.863215393. The condition is shown underneath in\\ncondition 1:\\nR2= 1−∑n\\ni=1(ˆyi−yi)2\\n∑n\\ni=1(yi−¯yi)2(1)\\nMean absolute error: If we consider with respect to error rate then ﬁrst comes\\nto mean absolute error. In measurements, mean absolute error may be a degree of\\nblunders between combined perceptions communicating the same wonder. Mean\\nAbsolute Error (MAE) is another loss function utilized for relapse models. MAE\\nis the entirety of outright contrasts between our target and anticipated factors.\\nSo it measures the normal greatness of errors in a set of forecasts, without\\nconsidering their bearings. Random Forest regressor gets the least mean absolute\\nerror rate which is 0.194459262 compare to others. The declaration of the F1\\nscore is displayed in equation 2 :\\nMAE =1\\nnn∑\\ni=1⏐⏐⏐Yi−ˆYi⏐⏐⏐ (2)\\nMean squared error: If we consider with respect to mean squared error, The\\nmean squared error (MSE) tells how near a relapse line is to a set of focuses.\\nIt does this by taking the separations from the focuses to the relapse line these\\nseparations are the errors and squaring them, we call It mean squared error.\\nFrom all the models Random forest achieves a minimum mean squared error\\n0.126358647. The articulation is shown beneath in 3 :\\nMSE =1\\nnn∑\\ni=1(\\nYi−ˆYi)2\\n(3)\\nRoot mean squared error: Now if we consider the root mean squared error,\\nRoot Mean Square Error (RMSE) means the standard deviation of the residuals\\nwhich is prediction error. Residuals are a degree of how distant from the relapse\\nline information focuses are RMSE could be a degree of how to spread out these\\nresiduals are. Here root mean squared error of Random Forest is 0.355469615\\nwhich is less compare to others. The verbalization is shown in 4:\\nRMSE =\\ued6a\\ued6b\\ued6b√n∑\\ni=1(ˆyi−yi)2\\nn(4)'),\n", " Document(metadata={'source': '/content/my_paper.pdf', 'page': 11}, page_content='10 <PERSON><PERSON><PERSON> et al.\\nBy all the statistical performance analysis we can see Random forest is the\\neﬃcient regressor model and performing well in this use case.\\n4.3 Result & Performance Analysis\\nTable 2. Performance Metrics of diﬀerent regressors\\nModel Name r2 score MAE MSE RMSE\\nRandom Forest 0.869904217 0.19445926 0.126358647 0.355469615\\nDecision Tree 0.742284572 0.21508858 0.250312287 0.500312189\\nLinear Regression 0.837495137 0.22694578 0.157836744 0.397286728\\nKNN Regressor 0.401557082 0.48855924 0.581252029 0.762398865\\nAdaBoost Regressor 0.786451397 0.37659111 0.207414199 0.455427491\\nGradient Boosting Regressor 0.863496747 0.20372662 0.132582057 0.364118191\\nXGBoost 0.863215393 0.20367076 0.132855329 0.364493249\\nRidge Regression 0.837495234 0.157836649 0.132855329 0.397286608\\nLasso Regression -5.91E-05 0.83158029 0.971331339 0.985561434\\nSVM 0.841801 0.203451 0.130951 0.345151\\nFrom table 2, we showed statistical results and comparisons among all ma-\\nchine learning regressors.For better analysis, we choose some statistical pro-\\ncedures for numerical result computing such as r2 score, mean absolute error\\n(MAE), mean square error (MSE), root mean square error (RMSE). After de-\\nveloping the models and testing all regressors, We can see that the Random\\nForest has predicted the best accuracy of 0.869904217 among all others, and the\\nmean absolute error is 0.194459262 which is the lowest, mean squared error is\\n0.126358647 and the root mean squared error is 0.355469615. Considering all\\nerrors and accuracy, it took the best place. Secondly, the gradient boosting re-\\ngressor has gained better accuracy with the second place which is 0.863496747\\nwith the mean absolute error is 0.203726623, mean squared error is 0.132582057\\nand the root mean squared error is 0.364118191. Thirdly, the XGBoost regres-\\nsor has acquired better accuracy, which is 0.863215393, along with the mean\\nabsolute error is 0.203670766, mean squared error is 0.132855329 and the root\\nmean squared error is 0.364493249. Also, from the section on linear algorithms,\\nwe can ﬁgure out that Linear Regression and Ridge Regression showed the al-\\nmost same accuracy and so on. So in this analysis, we can although Random'),\n", " Document(metadata={'source': '/content/my_paper.pdf', 'page': 12}, page_content='Rainfall Prediction 11\\nforest and Gradient Boosting Regressor have acquired almost the same Accuracy\\nbut if we consider the evaluation metrics of then so, Random forest has a low\\nerror rate compare to Gradient Boosting. So, here we have considered the Ran-\\ndom forest approach. Overall all of regressors showed a standard and acceptable\\nperformance.\\nThe bar chart is a graph for representing all regressors algorithms with Sta-\\ntistical measurement. The bar can be vertically or horizontally. Here is the bar\\ngraph of our selective algorithms, down below.\\nFig. 4. Selective algorithms\\n5 Conclusion\\nIn this work, we have presented an initial attempt to determine how much rain\\nwill come when it’s raining time. In the data collection phase, we adopted real\\ndata from Australia from the Kaggle platform. The primary purpose of this\\ntask is to ﬁnd out the best regression technique for the prediction of rain. For\\nthis reason, we have used a variety of regression analysis techniques that can\\nbe utilized for predicting the quantity of rainfall so that anyone can use the\\nbest predictive model in real-life applications. To perform this task, we selected\\nﬁve signiﬁcant steps, these are data collection, data prepossessing, training model\\nusing regression analysis techniques, and performance analysis. In pre-processing\\npart, we have described cleaning data, Missing value check, EDA, Handling\\noutliers, Feature selection, Feature scaling respectively. Besides, we used ten\\nsupervised regressors (machine learning models) for predicting rainfall. Among\\nall models the are gives good accuracy in our predicting regression. Here, in'),\n", " Document(metadata={'source': '/content/my_paper.pdf', 'page': 13}, page_content='12 <PERSON><PERSON><PERSON> et al.\\nthe ﬁgure 4 the graphical performance including compassion among all trained\\nmodels has been depicted\\n6 Future Work\\nIn future work, we will focus on the real-life application of rainfall prediction,\\nso that anyone especially farmer can use it easily and forecast the weather of\\nrain. Also, we have plan to use the neural network based deep hybrid approaches\\nto improve the performance. Undoubtedly, we have plans to evaluate the other\\ncountry’s data for forecasting the rain.\\nReferences\\n1. E<PERSON>, S Salcedo-Sanz, and C Casanova-Mateo. Accurate precipita-\\ntion prediction with support vector classiﬁers: A study including novel predictive\\nvariables and observational data. Atmospheric research , 139:128–136, 2014.\\n2. <PERSON>. River Dreams: The people and landscape of the Cooks River . NewSouth,\\n2018.\\n3. <PERSON>. The rainfall measurement problem. IAHS Publication No , 78:215–\\n231, 1967.\\n4. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Machine learning mod-\\nels applied for rainfall prediction. REVISTA GEINTEC-GESTAO INOVACAO E\\nTECNOLOGIAS , 11(3):179–187, 2021.\\n5. Zhi-liang Wang and Hui-hua Sheng. Rainfall prediction using generalized regres-\\nsion neural network: case study zhengzhou. In 2010 International conference on\\ncomputational and information sciences , pages 1265–1268. IEEE, 2010.\\n6. Nikhil Sethi and Kanwal Garg. Exploiting data mining technique for rainfall pre-\\ndiction. International Journal of Computer Science and Information Technologies ,\\n5(3):3982–3984, 2014.\\n7. Sunyoung Lee, Sungzoon Cho, and Patrick M Wong. Rainfall prediction using\\nartiﬁcial neural networks. journal of geographic information and Decision Analysis ,\\n2(2):233–242, 1998.\\n8. Adil M Bagirov, Arshad Mahmood, and Andrew Barton. Prediction of monthly\\nrainfall in victoria, australia: Clusterwise linear regression approach. Atmospheric\\nresearch , 188:20–29, 2017.\\n9. Mohammed Moulana, Kolapalli Roshitha, Golla Niharika, and Maturi Siva Sai.\\nPrediction of rainfall using machine learning techniques. International Journal of\\nScientiﬁc & Technology Research , 9:3236–3240, 2020.\\n10. P Asha, A Jesudoss, S Prince Mary, KV Sai Sandeep, and K Harsha Vardhan.\\nAn eﬃcient hybrid machine learning classiﬁer for rainfall prediction. In Journal of\\nPhysics: Conference Series , volume 1770, page 012012. IOP Publishing, 2021.\\n11. S Sakthivel et al. Eﬀective procedure to predict rainfall conditions using hybrid\\nmachine learning strategies. Turkish Journal of Computer and Mathematics Edu-\\ncation (TURCOMAT) , 12(6):209–216, 2021.\\n12. Diwakar Naidu, Babita Majhi, and Surendra Kumar Chandniha. Development of\\nrainfall prediction models using machine learning approaches for diﬀerent agro-\\nclimatic zones. In Handbook of Research on Automated Feature Engineering and\\nAdvanced Applications in Data Science , pages 72–94. IGI Global, 2021.'),\n", " Document(metadata={'source': '/content/my_paper.pdf', 'page': 14}, page_content='Rainfall Prediction 13\\n13. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Predict-\\ning rainfall-induced soil erosion based on a hybridization of adaptive diﬀerential\\nevolution and support vector machine classiﬁcation. Mathematical Problems in\\nEngineering , 2021, 2021.\\n14. <PERSON> and <PERSON>. Ant colony–information gain based feature selec-\\ntion method for weather dataset. Annals of the Romanian Society for Cell Biology ,\\npages 3838–3850, 2021.\\n15. <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> Aliﬀ.\\nEnhancing malaysia rainfall prediction using classiﬁcation techniques. J. <PERSON>l.\\nEnviron. Biol. Sci , 7(2S):20–29, 2017.\\n16. <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Proba-\\nbilistic forecasts of mesoscale convective system initiation using the random forest\\ndata mining technique. Weather and Forecasting , 31(2):581–599, 2016.\\n17. <PERSON>. Climate change and severe thunderstorms. In Oxford research\\nencyclopedia of climate science . 2018.\\n18. <PERSON>. Severe thunderstorms and climate change. Atmospheric Re-\\nsearch , 123:129–138, 2013.\\n19. <PERSON>, <PERSON>, <PERSON>, <PERSON>ael Reinaudi, and Galen Yacalis.\\nCould machine learning break the convection parameterization deadlock? Geo-\\nphysical Research Letters , 45(11):5742–5751, 2018.\\n20. <PERSON> J Mcphaden, Gary Meyers, K Ando, Y Masumoto, VSN Murty,\\nM Ravichandran, F Syamsudin, J´ erˆ ome Vialard, Lianbo Yu, and W Yu. Rama:\\nthe research moored array for african–asian–australian monsoon analysis and pre-\\ndiction. Bulletin of the American Meteorological Society , 90(4):459–480, 2009.\\n21. Peter BR Hazell. The appropriate role of agricultural insurance in developing\\ncountries. Journal of International Development , 4(6):567–581, 1992.\\n22. Peter P Mollinga, Ruth S Meinzen-Dick, and Douglas J Merrey. Politics, plurality\\nand problemsheds: A strategic approach for reform of agricultural water resources\\nmanagement. Development Policy Review , 25(6):699–719, 2007.\\n23. Chirag Shah, Chathra Hendahewa, and Roberto Gonz´ alez-Ib´ a˜ nez. Rain or shine?\\nforecasting search process performance in exploratory search tasks. Journal of the\\nAssociation for Information Science and Technology , 67(7):1607–1623, 2016.\\n24. Gareth James, Daniela Witten, Trevor Hastie, and Robert Tibshirani. Linear\\nmodel selection and regularization. In An introduction to statistical learning , pages\\n225–288. Springer, 2021.\\n25. Raksha Agarwal and Niladri Chatterjee. Langresearchlab nc at cmcl2021 shared\\ntask: Predicting gaze behaviour using linguistic features and tree regressors. In\\nProceedings of the Workshop on Cognitive Modeling and Computational Linguis-\\ntics, pages 79–84, 2021.\\n26. S Ben´ ıtez-Pe˜ na, E Carrizosa, V Guerrero, MD Jim´ enez-Gamero, B Mart´ ın-\\nBarrag´ an, and C Molero-R´ ıo. On sparse ensemble methods. 2021.\\n27. Kim de Bie, Ana Lucic, and Hinda Haned. To trust or not to trust a regressor:\\nEstimating and explaining trustworthiness of regression predictions. arXiv preprint\\narXiv:2104.06982 , 2021.\\n28. Mauricio Gonz´ alez-Palacio, Lina Sep´ ulveda-Cano, and Ronal Montoya. Simpli-\\nﬁed path loss lognormal shadow fading model versus a support vector machine-\\nbased regressor comparison for determining reception powers in wlan networks. In\\nInternational Conference on Information Technology & Systems , pages 431–441.\\nSpringer, 2021.\\nView publication statsView publication statsView publication stats')]"]}, "metadata": {}, "execution_count": 110}]}, {"cell_type": "code", "source": [], "metadata": {"id": "77KUe0qSmt5h"}, "id": "77KUe0qSmt5h", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "bduwbig1mt9c"}, "id": "bduwbig1mt9c", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "FSnv3gV3muBm"}, "id": "FSnv3gV3muBm", "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "uO4dx_pNmuFt"}, "id": "uO4dx_pNmuFt", "execution_count": null, "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}, "colab": {"provenance": [], "gpuType": "T4"}, "accelerator": "GPU"}, "nbformat": 4, "nbformat_minor": 5}