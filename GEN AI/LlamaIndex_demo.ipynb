{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU", "widgets": {"application/vnd.jupyter.widget-state+json": {"903d15f299df449da52b6b8233850857": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9837aa7584904ef7bdc8e4bbb128ba40", "IPY_MODEL_881063785b8b43e7a86c1de04d42e5c1", "IPY_MODEL_25df0a34e4b349d29ac206eaa36c241d"], "layout": "IPY_MODEL_f7211751c1384a36b7466ebd8f0e288d"}}, "9837aa7584904ef7bdc8e4bbb128ba40": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e4d31c45c3124a0586586eb993f7bc9d", "placeholder": "​", "style": "IPY_MODEL_4c7abe27dc404dea90a0cd6a196c70c4", "value": "Downloading (…)9a243/.gitattributes: 100%"}}, "881063785b8b43e7a86c1de04d42e5c1": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2b1ea9a27b73477abfb5804d7f45f805", "max": 1519, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_8516a83f732f40e3afa2399a90d13eb9", "value": 1519}}, "25df0a34e4b349d29ac206eaa36c241d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ceb06c7e5e0a46fab78224e734ca6c17", "placeholder": "​", "style": "IPY_MODEL_bb1ef1c7a49c42ae885070a52ede2035", "value": " 1.52k/1.52k [00:00&lt;00:00, 68.3kB/s]"}}, "f7211751c1384a36b7466ebd8f0e288d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e4d31c45c3124a0586586eb993f7bc9d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4c7abe27dc404dea90a0cd6a196c70c4": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2b1ea9a27b73477abfb5804d7f45f805": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8516a83f732f40e3afa2399a90d13eb9": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ceb06c7e5e0a46fab78224e734ca6c17": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bb1ef1c7a49c42ae885070a52ede2035": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c8aa6e8d98924f328db627b73d147be5": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_cf8be049f5514207b790fc42458b9fbe", "IPY_MODEL_36e521f3005f4e098eb2d523587a0d7a", "IPY_MODEL_d98d233a558d4f7389fd530acda855fe"], "layout": "IPY_MODEL_c751bde3426747b498a11f16b70bdc41"}}, "cf8be049f5514207b790fc42458b9fbe": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f118255250004e2cba2295993c5aa082", "placeholder": "​", "style": "IPY_MODEL_57a8e3bfb8f24ecdb9a28237f6c5fd95", "value": "Downloading (…)_Pooling/config.json: 100%"}}, "36e521f3005f4e098eb2d523587a0d7a": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4e03dad574a64e8c844c2b6c2e60811b", "max": 190, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_ca788245ecd540fb9d99deff1d1059c2", "value": 190}}, "d98d233a558d4f7389fd530acda855fe": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8558789a3fc34d64b3a8bfff3e66132f", "placeholder": "​", "style": "IPY_MODEL_2aaa96d1868a489c874f2d8a3b10b7fa", "value": " 190/190 [00:00&lt;00:00, 4.25kB/s]"}}, "c751bde3426747b498a11f16b70bdc41": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f118255250004e2cba2295993c5aa082": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "57a8e3bfb8f24ecdb9a28237f6c5fd95": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4e03dad574a64e8c844c2b6c2e60811b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ca788245ecd540fb9d99deff1d1059c2": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8558789a3fc34d64b3a8bfff3e66132f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2aaa96d1868a489c874f2d8a3b10b7fa": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a93ed370645c4634af0d189870228a31": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_176003174a224871845a98f3899066ba", "IPY_MODEL_984d8d44d8124161b3c7834dd454c66b", "IPY_MODEL_b628979a52264881a07659aaee86ea95"], "layout": "IPY_MODEL_2579fcd61d7c46a180bfc41ed7bfd77e"}}, "176003174a224871845a98f3899066ba": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_776dee3627564f5da71bce7113a7e740", "placeholder": "​", "style": "IPY_MODEL_051a6e6a5eee499db760abbbba228f19", "value": "Downloading (…)1e3c49a243/README.md: 100%"}}, "984d8d44d8124161b3c7834dd454c66b": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_97e817c44d734531bb4a21f9a70708dd", "max": 90069, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_cac05914e6dd4febaec54f6649eed839", "value": 90069}}, "b628979a52264881a07659aaee86ea95": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7e828b0bd0904f099e6185276615509d", "placeholder": "​", "style": "IPY_MODEL_192ffcad51d944adae634aeb7063b5f2", "value": " 90.1k/90.1k [00:00&lt;00:00, 1.67MB/s]"}}, "2579fcd61d7c46a180bfc41ed7bfd77e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "776dee3627564f5da71bce7113a7e740": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "051a6e6a5eee499db760abbbba228f19": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "97e817c44d734531bb4a21f9a70708dd": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cac05914e6dd4febaec54f6649eed839": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7e828b0bd0904f099e6185276615509d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "192ffcad51d944adae634aeb7063b5f2": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9c895f9f349a4708b42db32ce83a02af": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_4ce909dfe1f645bdb92234bd578be953", "IPY_MODEL_294e951c5c7046a389427f9240ef6529", "IPY_MODEL_12cbf197afa54c5794ed57901e07a535"], "layout": "IPY_MODEL_f0a3883c3c81498d850286bebbc272c7"}}, "4ce909dfe1f645bdb92234bd578be953": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_293d3434556e4fa88ab86dc4426504e3", "placeholder": "​", "style": "IPY_MODEL_074b2b434ff5420e96cb5071c33442cc", "value": "Downloading (…)3c49a243/config.json: 100%"}}, "294e951c5c7046a389427f9240ef6529": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6936e212d1e54ac9935ee9dc0effab32", "max": 719, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_50a7c7334b18453fa8a177a6968f7f24", "value": 719}}, "12cbf197afa54c5794ed57901e07a535": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6aeaf8ff8b4243ddaf08660405d0de9f", "placeholder": "​", "style": "IPY_MODEL_b83ea89f96e24d33a09c397d73de1fdd", "value": " 719/719 [00:00&lt;00:00, 12.4kB/s]"}}, "f0a3883c3c81498d850286bebbc272c7": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "293d3434556e4fa88ab86dc4426504e3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "074b2b434ff5420e96cb5071c33442cc": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6936e212d1e54ac9935ee9dc0effab32": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "50a7c7334b18453fa8a177a6968f7f24": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "6aeaf8ff8b4243ddaf08660405d0de9f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b83ea89f96e24d33a09c397d73de1fdd": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ab3c09044f6d43abbd5b6370587b4d93": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0023bcc4aaf3481f95e4d0a709bb9ac7", "IPY_MODEL_9abd1849142d4633b37dd20717ab5f21", "IPY_MODEL_8e0ee13db9c24fdf9b32fc8aeaea40d8"], "layout": "IPY_MODEL_ad724eec0ccd448cb6adac798042d0f3"}}, "0023bcc4aaf3481f95e4d0a709bb9ac7": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ac681ef384a948b1996fed5e23431111", "placeholder": "​", "style": "IPY_MODEL_9a1a6523166d463a8429dee17e4626a1", "value": "Downloading (…)ce_transformers.json: 100%"}}, "9abd1849142d4633b37dd20717ab5f21": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5345eb4c78244e0492902bd28046f88d", "max": 124, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_50292be14b254a55b6626232aac3dae2", "value": 124}}, "8e0ee13db9c24fdf9b32fc8aeaea40d8": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bafeac4919a9435a8ae925ef2d0629e0", "placeholder": "​", "style": "IPY_MODEL_95f2c40b12c24e6c94777bd2506ee051", "value": " 124/124 [00:00&lt;00:00, 4.13kB/s]"}}, "ad724eec0ccd448cb6adac798042d0f3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ac681ef384a948b1996fed5e23431111": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9a1a6523166d463a8429dee17e4626a1": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5345eb4c78244e0492902bd28046f88d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "50292be14b254a55b6626232aac3dae2": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "bafeac4919a9435a8ae925ef2d0629e0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "95f2c40b12c24e6c94777bd2506ee051": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "45f02c3ddaf949cea984cd43275cf4f1": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f862711bb01a40f5a9afca453902ab13", "IPY_MODEL_7c9b0a38175f41f986cc0db4f636e4ad", "IPY_MODEL_5c83269e53fa4cd586abf8377ad55491"], "layout": "IPY_MODEL_7eced5ed7b154d24b4f0eb0e2107ded8"}}, "f862711bb01a40f5a9afca453902ab13": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_021647ecd5a84f719b6eca7cbf4ce4dd", "placeholder": "​", "style": "IPY_MODEL_0260afb1a3114d258557d8abb8b648e2", "value": "Downloading model.safetensors: 100%"}}, "7c9b0a38175f41f986cc0db4f636e4ad": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6b694071e135468fadc31f7a09530e34", "max": 437955512, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_c6ec655ebced41b9b9199b3c95ffb5e6", "value": 437955512}}, "5c83269e53fa4cd586abf8377ad55491": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0d3ec1aa51b84692a5a80e9a0f4655fc", "placeholder": "​", "style": "IPY_MODEL_6b6e6a4123af4be88a33708a4d6bbe30", "value": " 438M/438M [00:04&lt;00:00, 96.8MB/s]"}}, "7eced5ed7b154d24b4f0eb0e2107ded8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "021647ecd5a84f719b6eca7cbf4ce4dd": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0260afb1a3114d258557d8abb8b648e2": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6b694071e135468fadc31f7a09530e34": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c6ec655ebced41b9b9199b3c95ffb5e6": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "0d3ec1aa51b84692a5a80e9a0f4655fc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6b6e6a4123af4be88a33708a4d6bbe30": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c708bd05142341e4af744ce01a2e1bf7": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f4cf5cd8699a4a0182167576ed89c25a", "IPY_MODEL_f6a51cf9d91845e29e1c1f2a864ae097", "IPY_MODEL_8c5b4e62eb054978b3b23461c2cbf4e7"], "layout": "IPY_MODEL_f2827bd2e94b43d8b09302ab5a8b3506"}}, "f4cf5cd8699a4a0182167576ed89c25a": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_15b870ef0cb8472b913c49539d8536fa", "placeholder": "​", "style": "IPY_MODEL_3a55dbc9e85b4d868f59a2bcc6a7c554", "value": "Downloading pytorch_model.bin: 100%"}}, "f6a51cf9d91845e29e1c1f2a864ae097": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9a997581ecd24c3faa3ba96d06ba0e35", "max": 437997357, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_28cc265ac3ef46c69ab811fea991f364", "value": 437997357}}, "8c5b4e62eb054978b3b23461c2cbf4e7": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b6c0ceecfb524a918d1c904c0687f58e", "placeholder": "​", "style": "IPY_MODEL_df404bde13224be2813f8a31585eb774", "value": " 438M/438M [00:01&lt;00:00, 261MB/s]"}}, "f2827bd2e94b43d8b09302ab5a8b3506": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "15b870ef0cb8472b913c49539d8536fa": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3a55dbc9e85b4d868f59a2bcc6a7c554": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9a997581ecd24c3faa3ba96d06ba0e35": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "28cc265ac3ef46c69ab811fea991f364": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b6c0ceecfb524a918d1c904c0687f58e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "df404bde13224be2813f8a31585eb774": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5e309dc0a13a4742a08bb8eb5a1be968": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9aa030848cd34f6c8cfa17e614e9faae", "IPY_MODEL_482f63e1b3ff42dfa2ce8491e02c386d", "IPY_MODEL_ee55b32cd81e4413bd5f694d190ccaae"], "layout": "IPY_MODEL_b1bd09849a094f32bcb07c8eab3c2028"}}, "9aa030848cd34f6c8cfa17e614e9faae": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a20b4a36f18846ed93db124fe571f023", "placeholder": "​", "style": "IPY_MODEL_987ffb7983c04071a46d35494a3cf989", "value": "Downloading (…)nce_bert_config.json: 100%"}}, "482f63e1b3ff42dfa2ce8491e02c386d": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1470721076cf41d984e231d9b052f0b3", "max": 52, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_ec251b4f5e5e45ac8b39337b883343d6", "value": 52}}, "ee55b32cd81e4413bd5f694d190ccaae": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e86b0d3b2abe40019b84d6b9e804b080", "placeholder": "​", "style": "IPY_MODEL_a8f250319fa842998a8bb8d9a26deaf8", "value": " 52.0/52.0 [00:00&lt;00:00, 2.79kB/s]"}}, "b1bd09849a094f32bcb07c8eab3c2028": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a20b4a36f18846ed93db124fe571f023": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "987ffb7983c04071a46d35494a3cf989": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1470721076cf41d984e231d9b052f0b3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ec251b4f5e5e45ac8b39337b883343d6": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e86b0d3b2abe40019b84d6b9e804b080": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a8f250319fa842998a8bb8d9a26deaf8": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2a1aba2615c047fb9a9e3590800c0215": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_df6981f3b26941618a5be9391fbb4dd0", "IPY_MODEL_6d712fffdde643f689c4b0b6fad3fc89", "IPY_MODEL_3be8373f147b4bdba20b054302074948"], "layout": "IPY_MODEL_2b67722d589842248b956d66dcf09322"}}, "df6981f3b26941618a5be9391fbb4dd0": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d8c86ccf604547ac9ef9cc91d55fe927", "placeholder": "​", "style": "IPY_MODEL_4fb6a908acca43caaa5ddabbaa8f2bf5", "value": "Downloading (…)cial_tokens_map.json: 100%"}}, "6d712fffdde643f689c4b0b6fad3fc89": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6e793ab45d834b05ac7924b628baa356", "max": 125, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_1b442f31e13b4b18a08669c1a150c615", "value": 125}}, "3be8373f147b4bdba20b054302074948": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_19fbc7421db64995995da9ca8b54a99d", "placeholder": "​", "style": "IPY_MODEL_92d8f19db5224c619e864cfd2b2f9b5b", "value": " 125/125 [00:00&lt;00:00, 9.17kB/s]"}}, "2b67722d589842248b956d66dcf09322": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d8c86ccf604547ac9ef9cc91d55fe927": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4fb6a908acca43caaa5ddabbaa8f2bf5": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6e793ab45d834b05ac7924b628baa356": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1b442f31e13b4b18a08669c1a150c615": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "19fbc7421db64995995da9ca8b54a99d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "92d8f19db5224c619e864cfd2b2f9b5b": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5858a6ab6aa847b7a38d6916655ce5b8": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1ecf62b41229495f8efa4981ca8177e8", "IPY_MODEL_3b5a94e0428342da8ab16ed0ec906ccd", "IPY_MODEL_341c06f3bf6e4b29a4010babe07eeebf"], "layout": "IPY_MODEL_233cb3581524453ca31b51d2dca916f3"}}, "1ecf62b41229495f8efa4981ca8177e8": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0061f308bdc2484fb81b884cfdc4d7d5", "placeholder": "​", "style": "IPY_MODEL_f0b213f0db1e4450b8fed947932cd0e9", "value": "Downloading (…)9a243/tokenizer.json: 100%"}}, "3b5a94e0428342da8ab16ed0ec906ccd": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0e2bcaff68fd4fb98788fac9e5c6596b", "max": 711396, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_bd7cf408e0414400914e2643094a262a", "value": 711396}}, "341c06f3bf6e4b29a4010babe07eeebf": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0361566722974545a43de179dab68ecc", "placeholder": "​", "style": "IPY_MODEL_cdc430ed8e594fe1905cbe3d3882a622", "value": " 711k/711k [00:00&lt;00:00, 33.8MB/s]"}}, "233cb3581524453ca31b51d2dca916f3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0061f308bdc2484fb81b884cfdc4d7d5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f0b213f0db1e4450b8fed947932cd0e9": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0e2bcaff68fd4fb98788fac9e5c6596b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bd7cf408e0414400914e2643094a262a": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "0361566722974545a43de179dab68ecc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cdc430ed8e594fe1905cbe3d3882a622": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3e9a8333200a445982edadb13efcf5d8": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_eaf3a213ac594cf8818b19e75e7e0057", "IPY_MODEL_1fb62612a1f845a890d77a38b6b5223e", "IPY_MODEL_c82ec150d5a5486d9712f9f87363e8da"], "layout": "IPY_MODEL_d108b3be110642898a6896b62e369c2f"}}, "eaf3a213ac594cf8818b19e75e7e0057": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6a5cbc388ca04f17930fa51e3b479d33", "placeholder": "​", "style": "IPY_MODEL_dce6f13b0bf447839d3d71e4d174ad41", "value": "Downloading (…)okenizer_config.json: 100%"}}, "1fb62612a1f845a890d77a38b6b5223e": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_282e99c67b944ee1ae76357e86c648bf", "max": 366, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_4738ccdd38af4d3798a92f67dad16e04", "value": 366}}, "c82ec150d5a5486d9712f9f87363e8da": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_82899aa739f54d98aa9c7955f18dc2c8", "placeholder": "​", "style": "IPY_MODEL_b223dd4676424856b9264e2c0e7c6eab", "value": " 366/366 [00:00&lt;00:00, 27.1kB/s]"}}, "d108b3be110642898a6896b62e369c2f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6a5cbc388ca04f17930fa51e3b479d33": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dce6f13b0bf447839d3d71e4d174ad41": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "282e99c67b944ee1ae76357e86c648bf": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4738ccdd38af4d3798a92f67dad16e04": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "82899aa739f54d98aa9c7955f18dc2c8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b223dd4676424856b9264e2c0e7c6eab": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fce4280420754dd694d79d83d568dda4": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6a5e97f44eae45f9898cae48910f6d72", "IPY_MODEL_c070911b61d349b39559a25b699b240a", "IPY_MODEL_952cdd03d79d40b2aa93f25c990860b1"], "layout": "IPY_MODEL_78fade87f26f4cf5b54270e30813c4ba"}}, "6a5e97f44eae45f9898cae48910f6d72": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_19ea69e25bd74105ba657f9dd88e137c", "placeholder": "​", "style": "IPY_MODEL_13ea918b604c46e0aafa2114549c0b0e", "value": "Downloading (…)1e3c49a243/vocab.txt: 100%"}}, "c070911b61d349b39559a25b699b240a": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5591e8c671a24baa97ac2ba9dd87d681", "max": 231508, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_ae7d5d105ea044598e76be5bc0cc0ccd", "value": 231508}}, "952cdd03d79d40b2aa93f25c990860b1": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_65bb4bfa2d834c4c8bfbeae48808b3a4", "placeholder": "​", "style": "IPY_MODEL_0309575b12f3437697263a2a07f33f77", "value": " 232k/232k [00:00&lt;00:00, 16.0MB/s]"}}, "78fade87f26f4cf5b54270e30813c4ba": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "19ea69e25bd74105ba657f9dd88e137c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "13ea918b604c46e0aafa2114549c0b0e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5591e8c671a24baa97ac2ba9dd87d681": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ae7d5d105ea044598e76be5bc0cc0ccd": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "65bb4bfa2d834c4c8bfbeae48808b3a4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0309575b12f3437697263a2a07f33f77": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c97068aba0034a11bf8aaeca27d07829": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7b57fda9ecc849b2a9cfa8eb413c8550", "IPY_MODEL_697d818fd9934b68b0e8f876532677d1", "IPY_MODEL_4a9e38877ca545268258fb17ae9445bc"], "layout": "IPY_MODEL_6cc21d0d49dd4ecfab03953533924a25"}}, "7b57fda9ecc849b2a9cfa8eb413c8550": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_686f8cf3b0d04a3b8dffad47ed8e1404", "placeholder": "​", "style": "IPY_MODEL_0580e93ad0bf4a62ab9684b8a517cc2c", "value": "Downloading (…)c49a243/modules.json: 100%"}}, "697d818fd9934b68b0e8f876532677d1": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_92fe0f8643bb465a9f9935b00b36d558", "max": 349, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_bf3228423bd64b95af2503c3e4a007e8", "value": 349}}, "4a9e38877ca545268258fb17ae9445bc": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e65894c129cb4d1b9d23067e16c018c8", "placeholder": "​", "style": "IPY_MODEL_7ffc411962e74dff8be3735428911b8e", "value": " 349/349 [00:00&lt;00:00, 18.3kB/s]"}}, "6cc21d0d49dd4ecfab03953533924a25": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "686f8cf3b0d04a3b8dffad47ed8e1404": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0580e93ad0bf4a62ab9684b8a517cc2c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "92fe0f8643bb465a9f9935b00b36d558": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bf3228423bd64b95af2503c3e4a007e8": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e65894c129cb4d1b9d23067e16c018c8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7ffc411962e74dff8be3735428911b8e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "cells": [{"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "EaTpeCbl5D4c", "outputId": "866fc7d8-db6b-4969-bb2d-6a68c890fd5e"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: pypdf in /usr/local/lib/python3.10/dist-packages (3.17.0)\n", "Requirement already satisfied: docx2txt in /usr/local/lib/python3.10/dist-packages (0.8)\n", "Requirement already satisfied: transformers in /usr/local/lib/python3.10/dist-packages (4.35.0)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from transformers) (3.13.1)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.16.4 in /usr/local/lib/python3.10/dist-packages (from transformers) (0.17.3)\n", "Requirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.10/dist-packages (from transformers) (1.23.5)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.10/dist-packages (from transformers) (23.2)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from transformers) (6.0.1)\n", "Requirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.10/dist-packages (from transformers) (2023.6.3)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from transformers) (2.31.0)\n", "Requirement already satisfied: tokenizers<0.15,>=0.14 in /usr/local/lib/python3.10/dist-packages (from transformers) (0.14.1)\n", "Requirement already satisfied: safetensors>=0.3.1 in /usr/local/lib/python3.10/dist-packages (from transformers) (0.4.0)\n", "Requirement already satisfied: tqdm>=4.27 in /usr/local/lib/python3.10/dist-packages (from transformers) (4.66.1)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.16.4->transformers) (2023.6.0)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.16.4->transformers) (4.5.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->transformers) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->transformers) (3.4)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->transformers) (1.26.18)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->transformers) (2023.7.22)\n"]}], "source": ["!pip install -q llama-index\n", "!pip install pypdf\n", "!pip install docx2txt\n", "!pip install transformers"]}, {"cell_type": "code", "source": ["!pip install google-generativeai"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 550}, "id": "a4CFaWyz9mgx", "outputId": "82769d4a-5d45-4f26-c3f5-61077c36773a"}, "execution_count": 9, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting google-generativeai\n", "  Using cached google_generativeai-0.2.2-py3-none-any.whl (133 kB)\n", "Collecting google-ai-generativelanguage==0.3.3 (from google-generativeai)\n", "  Using cached google_ai_generativelanguage-0.3.3-py3-none-any.whl (267 kB)\n", "Requirement already satisfied: google-auth in /usr/local/lib/python3.10/dist-packages (from google-generativeai) (2.17.3)\n", "Requirement already satisfied: google-api-core in /usr/local/lib/python3.10/dist-packages (from google-generativeai) (2.11.1)\n", "Requirement already satisfied: protobuf in /usr/local/lib/python3.10/dist-packages (from google-generativeai) (3.20.3)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from google-generativeai) (4.66.1)\n", "Requirement already satisfied: proto-plus<2.0.0dev,>=1.22.0 in /usr/local/lib/python3.10/dist-packages (from google-ai-generativelanguage==0.3.3->google-generativeai) (1.22.3)\n", "Requirement already satisfied: googleapis-common-protos<2.0.dev0,>=1.56.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core->google-generativeai) (1.61.0)\n", "Requirement already satisfied: requests<3.0.0.dev0,>=2.18.0 in /usr/local/lib/python3.10/dist-packages (from google-api-core->google-generativeai) (2.31.0)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from google-auth->google-generativeai) (5.3.2)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.10/dist-packages (from google-auth->google-generativeai) (0.3.0)\n", "Requirement already satisfied: six>=1.9.0 in /usr/local/lib/python3.10/dist-packages (from google-auth->google-generativeai) (1.16.0)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.10/dist-packages (from google-auth->google-generativeai) (4.9)\n", "Requirement already satisfied: grpcio<2.0dev,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core->google-generativeai) (1.59.2)\n", "Requirement already satisfied: grpcio-status<2.0.dev0,>=1.33.2 in /usr/local/lib/python3.10/dist-packages (from google-api-core->google-generativeai) (1.48.2)\n", "Requirement already satisfied: pyasn1<0.6.0,>=0.4.6 in /usr/local/lib/python3.10/dist-packages (from pyasn1-modules>=0.2.1->google-auth->google-generativeai) (0.5.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core->google-generativeai) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core->google-generativeai) (3.4)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core->google-generativeai) (1.26.18)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core->google-generativeai) (2023.7.22)\n", "Installing collected packages: google-ai-generativelanguage, google-generativeai\n", "Successfully installed google-ai-generativelanguage-0.3.3 google-generativeai-0.2.2\n"]}, {"output_type": "display_data", "data": {"application/vnd.colab-display-data+json": {"pip_warning": {"packages": ["google"]}}}, "metadata": {}}]}, {"cell_type": "code", "source": ["from llama_index import SimpleDirectoryReader, VectorStoreIndex\n", "from llama_index.llms.palm import PaLM\n", "from llama_index import ServiceContext\n", "from llama_index import StorageContext, load_index_from_storage\n", "import os"], "metadata": {"id": "MvyvFxhy6fHx"}, "execution_count": 1, "outputs": []}, {"cell_type": "markdown", "source": ["## Load data"], "metadata": {"id": "ObWpCs617PnY"}}, {"cell_type": "code", "source": ["!mkdir data"], "metadata": {"id": "J0l92MYm7L5h"}, "execution_count": 3, "outputs": []}, {"cell_type": "code", "source": ["documents = SimpleDirectoryReader(\"data\").load_data()"], "metadata": {"id": "KQqxtdRa7UXx"}, "execution_count": 3, "outputs": []}, {"cell_type": "code", "source": ["documents"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xbYDQVTF9Hsr", "outputId": "c9fd5972-a741-4d34-a593-27b0c06b0aef"}, "execution_count": 4, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(id_='8f116f6f-e26f-4531-a60a-ce72a6e51d31', embedding=None, metadata={'file_name': '04. LlamaIndex.docx', 'file_path': 'data/04. LlamaIndex.docx', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='1b78ceb8a9386843bea0785fc6b5485d2292402f7bba1508ee0f3ce698a7b564', text=\"What is LlamaIndex?\\n\\n\\n\\nLlamaIndex is a data framework for building LLM applications. It provides a comprehensive toolkit for ingestion, management, and querying of your external data so that you can use it with your LLM app.  \\n\\n\\n\\n\\n\\nChatGPT is trained on huge amounts of data. But what if you wish to train ChatGPT on your private data. There are 3 ways in which you can achieve this.\\n\\n\\n\\n\\n\\n1.   Train an open-source LLM like Llama on your data. This is a complex and time taking process which is not scalable.\\n\\n2.   Pass all of your documents as prompt to LLM. This has limitations since the context window size is limited.\\n\\n3.   Fetch and pass only the relevant documents as input to your LLM.\\n\\n\\n\\nLlamaIndex works using the 3rd method and we will study how we can do that with the help of an example. Some of the concepts being covered by Index etc. will be covered in more detail in the upcoming lessons.\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nHere is the official docs of LlamaIndex\\n\\n https://www.llamaindex.ai/\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nConnect to external Data using LlamaIndex.\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nWe know chatGPT is trained on public data. ChatGPT is nothing but its a LLM model which is trained in this public datasets available over the internet. But let's say you want to use chatgpt to be worked with your custom datasets how to do it. That's where exactly LlamaIndex comes into the picture. Using LlamaIndex we can connect our custom documents into LLM models. So once we do it out LLM model won’t reach out to the public data instead it will provide the response from out custom dataset as we can from the figure mentioned above.\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nSteps to build apps using LlamaIndex.\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nWe will be implementing one simple project using LlamaIndex so that we can learn how LlamaIndex works internally. When we will be using LlamaIndex to build apps the very first step would be loading the data. Data can be in any form let's say it could be API, PDF, DOCS, or text data but once we will be loading the data it would be created as documents \\n\\nObject.\\n\\n\\n\\nThis document object can be created by two techniques either we can manually create it or we can use the load_data function. Once our document object is created the next step would be the Index creation. This Index is going to help us query our data so that we can\\n\\nQuery our data it will automatically scan the documents and respond the ans to us as you can see above figure. \\n\\n\\n\\n\\n\\nWhat is In Context Learning & Fine Tuning?\\n\\n\\n\\n\\n\\nIn context Learning:\\n\\n\\n\\nIn context, learning is very useful if we don’t have direct access to the model.\\n\\nHere we use the model through an API. So no change in the LLM model parameters is required.\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nFine Tuning:\\n\\n\\n\\nFine-tuning is performed by training the LLM on a specific task using a dataset relevant to the task. The training process involves adjusting the model’s parameters to minimize the differences between the model’s predictions and the actual outputs in the dataset.\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nYou can refer to this documentation for fine-tuning: \\n\\n\\n\\nhttps://huggingface.co/docs/transformers/training\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nHow do LlamaIndex applications work (Diagram)?\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nWhy indexing required in LLM apps?\\n\\n\\n\\nIndexing is required because it has the limitation. Each model can only \\n\\nProcess up to a certain number of tokens\\n\\n\\n\\nEx: 1000 tokens = 750 words\\n\\n\\n\\nWhat if you need to process a large amount of documents? Here this indexing concept comes into the picture\", start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='148e8dd2-3df3-4567-b2ba-4b69a56e9dd2', embedding=None, metadata={'page_label': '1', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='22c317f515eb74a1e9cc5bdb525243680b888e9823bf1de32ef4d07002e4cbba', text='CS391R: Robot Learning (Fall 2021)\\nYou Only Look Once (YOLO): Unified, Real-Time Object Detection\\n1Presenter: <PERSON><PERSON> 2nd, 2021', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='de4b25bc-4689-4148-a76d-14ef21c50709', embedding=None, metadata={'page_label': '2', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='8bebb74b926d5d4c8b673d71737783f08758b8861fcddcf7d2ab514e5983b7e6', text='CS391R: Robot Learning (Fall 2021)2Problem Addressed: Object Detection❖Object detection is the problem of both locating ANDclassifying objects ❖Goal of YOLO algorithm is to do object detection both fast ANDwith high accuracy\\n“Deep Learning for Vision Systems” (Elgendy)Object Detection vs Classification', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='d178f4a7-e73f-4f60-a22c-40f423e6997c', embedding=None, metadata={'page_label': '3', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='6708bd6ee2b49c72aaf49357006853ccc0c737a85b21aa61bf145d772ad47da7', text='CS391R: Robot Learning (Fall 2021)3Importance of Object Detection for Robotics❖Visual modality is very powerful❖Humans are able to detect objects and do perception using just this modality in real time (not needing radar) ❖If we want responsive robot systems that work in real time (without specialized sensors) almost real time vision based object detection can help greatly\\nVision based vs LIDAR (self driving)\\nTesla Investor Day Presentation', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='6db2ffed-f9b3-471f-8aa8-cad52d8e4020', embedding=None, metadata={'page_label': '4', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='26d405922cad8c3d79acfec718cfc7fb73939006bc1dc03b4cc0b56ae8978897', text='CS391R: Robot Learning (Fall 2021)4Previous Object Detection ParadigmThis pipeline was used in nearly all SOTA Object Detection prior: \\nStep 1: Scan the image to generate candidate bounding boxes\\nImage ClassifierLabel + confidencehat -0.92racket -0.2ball -0.23Step 2: Run the bounding box through a classifierStep 3: Conduct post-processing (filtering out redundant bounding boxes)Diagram developed by presenter', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='51f3bba9-cd47-4b11-a589-aa38eebe5998', embedding=None, metadata={'page_label': '5', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='e981347df3b910b6d9b05e3641271d0cefedd4c48b2af8707c6c85075c8c083c', text='CS391R: Robot Learning (Fall 2021)5Key Insights❖A separate model for generating bounding boxes and for classification (more complicated model pipeline)❖Need to run classification many times (expensive computation)❖Looks at limited part of the image (lacks contextual information for detection) Previous Approaches❖A single neural network for localization and for classification (less complicated pipeline)❖Need to inference only once (efficient computation)❖Looks at the entire image each time leading to less false positives (has contextual information for detection) YOLO algorithm', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='d41832d0-19c7-4ca9-a5e9-eccca6fc7214', embedding=None, metadata={'page_label': '6', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='07e689606a73ab4d2a635a150cc38255595245ff72357a5503b5841986c302cf', text='CS391R: Robot Learning (Fall 2021)6Formal Problem Setting❖Given an image generate bounding boxes, one for each detectable object in image ❖For each bounding box, output 5 predictions: x, y, w, h, confidence. Also output class❖x, y (coordinates for center of bounding box)❖w,h (width and height)❖confidence (probability bounding box has object)❖class (classification of object in bounding box)\\n', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='92960aa6-4d65-40c3-9cb4-88b577364817', embedding=None, metadata={'page_label': '7', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='fa927d9b5e650470969a836ac2a6f9d78190bee1ae1d04b12695d211410cac13', text='CS391R: Robot Learning (Fall 2021)7Related Work-R-CNN or Region Based Convolutional Network (Girshick et al. 2014):-Used the sliding window approach from earlier, with Selective Search, a smarter way to select candidates (which means there is less computation)-Still feeds a limited part of the image to the classifier-Drawbacks: Large pipeline, slow, too many false positives-Fast and Faster R-CNN: -Optimize parts of the pipeline described earlier -Drawbacks: loses accuracy-Deep Multibox (Szegedy et. al 2014):-Train a CNN to find areas of interest-Drawbacks: Doesn’t address classification only localization', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='a04f9bb9-8d6c-4a9d-ad79-445f2fe2dbdb', embedding=None, metadata={'page_label': '8', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='6e5189c798bf3875e01c3c312cb6fc07cd503f3a6c716d97692f38c231ab4cde', text='CS391R: Robot Learning (Fall 2021)8Related Work-MultiGrasp (<PERSON><PERSON> et. al 2014)-Similar to YOLO-A much simpler task (only needs to predict object not multiple objects)', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='6c3ac417-f331-4720-ae5a-ff31927c07b1', embedding=None, metadata={'page_label': '9', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='f41bda29b5858fbd0dfb2fba99f605c1b2e52fbd3fb1ee409823a882652d486b', text='CS391R: Robot Learning (Fall 2021)9YOLO overview❖First, image is split into a SxS grid❖For each grid square, generate B bounding boxes❖For each bounding box, there are 5 predictions: x, y, w, h, confidence \\nS = 3, B = 2', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='f17b7faf-7c44-4f88-bef6-817ba5634231', embedding=None, metadata={'page_label': '10', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='becccf72f6b72d5d9526bb58f6821eaf9c40235486e7fe7cd57aca4ebaa7a01f', text='CS391R: Robot Learning (Fall 2021)\\n10YOLO Training❖YOLO is a regression algorithm. What is X? What is Y?❖X is simple, just an image width (in pixels) * height (in pixels) * RGB values❖Y is a tensor of size S * S * (B * 5 + C)❖B*5 + C term represents the predictions + class predicted distribution for a grid blockFor each grid block, we have a vector like this. For this example B is 2 and C is 2\\nGT label example:\\n', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='80491feb-2af8-4556-ab9d-168657bf95fc', embedding=None, metadata={'page_label': '11', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='9197b9da4eb3102093a2261dea7213cf7f334f1f86336550ce75d73686d16544', text='CS391R: Robot Learning (Fall 2021)11YOLO Architecture-Now that we know the input and output, we can discuss the model-We are given 448 by 448 by 3 as our input.-Implementation uses 7 convolution layers -Paper parameters: S = 7, B = 2, C = 20-Output is S*S*(5B+C) = 7*7*(5*2+20) = 7*7*30\\n', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='291bafdc-fec1-4a36-ad05-6660e932bb8c', embedding=None, metadata={'page_label': '12', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='cf5887b2289035b55a353da80a70417d1ef7399f097e4df96dfb92932db42023', text='CS391R: Robot Learning (Fall 2021)12YOLO Prediction\\n❖We then use the output to make final detections❖Use a threshold to filter out bounding boxes with low P(Object)❖In order to know the class for the bounding box compute score take argmax over the distribution Pr(Class|Object) for the grid the bounding box’s center is in', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='d5f79234-7d50-4fca-b7f4-58b3d34ca974', embedding=None, metadata={'page_label': '13', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='7fec188d7ead6e4bc9255475f0243d22abc1cb1f7fed8436e6cc12989bf86ff8', text='CS391R: Robot Learning (Fall 2021)13Non-maximal suppression❖Most of the time objects fall in one grid, however it is still possible to get redundant boxes (rare case as object must be close to multiple grid cells for this to happen)❖Discard bounding box with high overlap (keeping the bounding box with highest confidence)❖Adds 2-3% on final mAP score\\n', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='0428eb94-9e2c-433a-bae7-5ceb5507a5aa', embedding=None, metadata={'page_label': '14', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='788f3026746733cf4440610d13923b1708ec02aa97b778d24ce1ccae096754f6', text='CS391R: Robot Learning (Fall 2021)14YOLO Objective Function❖For YOLO, we need to minimize the following loss❖Sum squared error is used\\nCoordinate Loss: Minimize the difference between x,y,w,h pred and x,y,w,h ground truth.  ONLY IF object exists in grid box and if bounding box is resp for predClass loss, minimize loss between true class of object in grid box Confidence Loss: Loss based on confidence ONLY IF there is object No Object Loss based on confidence if there is no object', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='c46b8c28-0d8b-4979-bcba-4a4f5f96c985', embedding=None, metadata={'page_label': '15', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='09713e861e6e7bf3c3143d0460009550a92ef592af24cbefe81a8dbe8ac9b84f', text='CS391R: Robot Learning (Fall 2021)15Experimental Setup❖Authors compare YOLO against the previous work described above on PASCAL VOC 2007, and VOC 2012 as well as out of domain art dataset ❖Correct if IOU metric above .5 and class is correct❖Use two performance metrics:➢mAP score: mean average precision➢FPS: frames per second❖Add FAST YOLO: which has less parameters\\n', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='34d625b5-0fb8-4d69-87fb-48b0df6befbb', embedding=None, metadata={'page_label': '16', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='f6753dacc0f12c1aa34780da41b7477c8b5207ddb9bde5441d10e5a2eeafdd2d', text='CS391R: Robot Learning (Fall 2021)16Experimental Results\\n❖Baseline YOLO outperform real time detectors by large amount❖Do better than most less than real time as well ', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='0a1cd9c9-caf0-4b2e-b6df-dd7fedd4f21a', embedding=None, metadata={'page_label': '17', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='f238cc85639f4fffe0b295c3f8c07a6304e0584e7c350904dc73c457744d7002', text='CS391R: Robot Learning (Fall 2021)17\\nExperimental Results', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='9ffac262-bd1e-486d-b46b-235cdd19f3d2', embedding=None, metadata={'page_label': '18', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='ce9fcd7498b62ca73b2b407bd4512ff0eb2994228b9f4f8d6e7b2f506ed89667', text='CS391R: Robot Learning (Fall 2021)18Exper-Makes far less background errors (less likely to predict false positives on background)-IOU is VERY small with any ground truth label-But far more localization errors-Correct class, IOU is somewhat small\\nExperimental Results -Error Analysis\\nBackground errorLocalization error', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='d192e91e-3d48-4d60-9d7b-1987aee62448', embedding=None, metadata={'page_label': '19', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='150cabbe1b67d35839bf4dd80eb42ab23d052344ad66e5d3b0de9aea47403c24', text='CS391R: Robot Learning (Fall 2021)19❖Ran YOLO + competitors (trained on natural images) on art ❖Does well on artistic datasets where more having global context greatly helpsExperimental Results -Out of Domain\\n', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='3afbe19f-4d10-4d9f-b9c6-4fd44c6caf3d', embedding=None, metadata={'page_label': '20', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='6356a8681b3f8ea8ed8ea6fef0e57750c89863c8322e30d70ab61fa6b8ef0bca', text='CS391R: Robot Learning (Fall 2021)20Discussion of Results❖Pro: YOLO is a lot faster than the other algorithms for image detection❖Pro: YOLO’s use of global information rather than only local information allows it to understand contextual information when doing object detection➢Does better in domains such as artwork due to this❖Con: YOLO lagged behind the SOTA models in object detection➢This is attributed to making many localization errors and unable to detect small object', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='7de82731-21e1-460d-a5d4-5f5b6d13293a', embedding=None, metadata={'page_label': '21', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='9a1df94ad35d9be2efb0563c6f044132eec8a29c081d5b14f6fbf00067c4b98f', text='CS391R: Robot Learning (Fall 2021)21Critique / Limitations / Open Issues ❖Performance lags behind SOTA ❖Requires data to be labeled with bounding boxes, hard to collect for many classes➢Previous work could generalize better since it used image classifier➢2014 COCO dataset (very large dataset) addressed this somewhat❖Regarding experiments: number of classes predicted is very limited➢Not convinced that YOLO v1 is generalizable ❖Confidence output of YOLO not confidence of class but P(Object), lowers interpretability❖Another limitation of YOLO is that it imposed spatial constraints on the objects in the image since only B boxes can be predicted on an SxS grid❖Since the architecture only predicts boxes, this might make it less useful for irregular shapes ', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='811365c9-a956-4e7d-848c-8049900d83be', embedding=None, metadata={'page_label': '22', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='ac75330a440b6e62dde2667b57c65fdb25827feb5a7e50b2ebe52f2f572f4e79', text='CS391R: Robot Learning (Fall 2021)22Future Work for Paper / Reading❖One extension of this work would be to look at image segmentation and see if the insights carry over○YOLOACT (<PERSON><PERSON> et al 2019): Real time image segmentation ❖YOLO has been upgraded 2 times ○Solves a lot of issues relating to detecting small objects, generalizability, and localization\\nYOLOACT example', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='18aeeda7-aea4-4850-8f79-aeb4ca643992', embedding=None, metadata={'page_label': '23', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='f3fdedd489512a9934b8c47e5e158157c8aa83a087de7a52f6407704432e21ee', text='CS391R: Robot Learning (Fall 2021)23Extended Readings❖YOLO v2 (https://arxiv.org/abs/1506.02640) (extends on the work greatly)(<PERSON><PERSON> et al 2016)➢Deals with the generalizability problem, has 9000 classes➢Class probability distribution per bounding box, not per grid➢High resolution classifier (finetune on high resolution)➢Batch norm➢Trained on MSCOCO (released after YOLO v1 paper)❖YOLO v3 (https://arxiv.org/abs/1804.02767) ➢“Incremental Improvement”➢Uses independent logistic classifiers for class■Allows for more specificity in classes\\n', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'),\n", " Document(id_='533792f8-261d-4504-b090-52cfb981b637', embedding=None, metadata={'page_label': '24', 'file_name': 'yolo.pdf', 'file_path': 'data/yolo.pdf', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={}, hash='563897b3811baab4a46a8f50dab7c0791cf23df748a422c615a6a002fff17ad3', text='CS391R: Robot Learning (Fall 2021)24Summary❖Object detection is the problem of detecting multiple objects in an image❖Almost real time object detection can make highly responsive robot systems without complex sensors❖Prior work relies on a large architecture with numerous parts to optimize❖YOLO proposes a unified architecture, which does all the tasks in one model and by one inference over the entire image❖They show enormous speed improvement and show that they can beat most other prior work in terms of mAPs', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n')]"]}, "metadata": {}, "execution_count": 4}]}, {"cell_type": "markdown", "source": ["## Split the Text into Small Chunks"], "metadata": {"id": "UxcIz54z9Qww"}}, {"cell_type": "code", "source": ["!pip install sentence_transformers"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UpNKrHty_WMh", "outputId": "ef7b9b6f-1f39-4905-cc54-02cbf31b92a4"}, "execution_count": 12, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting sentence_transformers\n", "  Downloading sentence-transformers-2.2.2.tar.gz (85 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.0/86.0 kB\u001b[0m \u001b[31m1.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: transformers<5.0.0,>=4.6.0 in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (4.35.0)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (4.66.1)\n", "Requirement already satisfied: torch>=1.6.0 in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (2.1.0+cu118)\n", "Requirement already satisfied: torchvision in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (0.16.0+cu118)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (1.23.5)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (1.2.2)\n", "Requirement already satisfied: scipy in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (1.11.3)\n", "Requirement already satisfied: nltk in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (3.8.1)\n", "Collecting sentencepiece (from sentence_transformers)\n", "  Downloading sentencepiece-0.1.99-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.3 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.3/1.3 MB\u001b[0m \u001b[31m36.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: huggingface-hub>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from sentence_transformers) (0.17.3)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.4.0->sentence_transformers) (3.13.1)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.4.0->sentence_transformers) (2023.6.0)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.4.0->sentence_transformers) (2.31.0)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.4.0->sentence_transformers) (6.0.1)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.4.0->sentence_transformers) (4.5.0)\n", "Requirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.4.0->sentence_transformers) (23.2)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from torch>=1.6.0->sentence_transformers) (1.12)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.10/dist-packages (from torch>=1.6.0->sentence_transformers) (3.2.1)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch>=1.6.0->sentence_transformers) (3.1.2)\n", "Requirement already satisfied: triton==2.1.0 in /usr/local/lib/python3.10/dist-packages (from torch>=1.6.0->sentence_transformers) (2.1.0)\n", "Requirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.6.0->sentence_transformers) (2023.6.3)\n", "Requirement already satisfied: tokenizers<0.15,>=0.14 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.6.0->sentence_transformers) (0.14.1)\n", "Requirement already satisfied: safetensors>=0.3.1 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.6.0->sentence_transformers) (0.4.0)\n", "Requirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from nltk->sentence_transformers) (8.1.7)\n", "Requirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk->sentence_transformers) (1.3.2)\n", "Requirement already satisfied: threadpoolctl>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from scikit-learn->sentence_transformers) (3.2.0)\n", "Requirement already satisfied: pillow!=8.3.*,>=5.3.0 in /usr/local/lib/python3.10/dist-packages (from torchvision->sentence_transformers) (9.4.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch>=1.6.0->sentence_transformers) (2.1.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.4.0->sentence_transformers) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.4.0->sentence_transformers) (3.4)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.4.0->sentence_transformers) (1.26.18)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.4.0->sentence_transformers) (2023.7.22)\n", "Requirement already satisfied: mpmath>=0.19 in /usr/local/lib/python3.10/dist-packages (from sympy->torch>=1.6.0->sentence_transformers) (1.3.0)\n", "Building wheels for collected packages: sentence_transformers\n", "  Building wheel for sentence_transformers (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for sentence_transformers: filename=sentence_transformers-2.2.2-py3-none-any.whl size=125923 sha256=dc591a2233665c4c22368304270da662c931509eb20b0a4998fda47e8049f466\n", "  Stored in directory: /root/.cache/pip/wheels/62/f2/10/1e606fd5f02395388f74e7462910fe851042f97238cbbd902f\n", "Successfully built sentence_transformers\n", "Installing collected packages: sentencepiece, sentence_transformers\n", "Successfully installed sentence_transformers-2.2.2 sentencepiece-0.1.99\n"]}]}, {"cell_type": "code", "source": ["from langchain.embeddings.huggingface import HuggingFaceBgeEmbeddings"], "metadata": {"id": "I1LUTjYh_NvP"}, "execution_count": 10, "outputs": []}, {"cell_type": "code", "source": ["os.environ['GOOGLE_API_KEY'] = 'AIzaSyCON4Y32JXFTj6NzeSBQJqfTtxoWU4DepM'"], "metadata": {"id": "WiBr3L2E9JMh"}, "execution_count": 5, "outputs": []}, {"cell_type": "code", "source": ["llm = PaLM()"], "metadata": {"id": "yz2x9zol9fix"}, "execution_count": 6, "outputs": []}, {"cell_type": "code", "source": ["embed_model = HuggingFaceBgeEmbeddings(model_name=\"BAAI/bge-base-en\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 433, "referenced_widgets": ["903d15f299df449da52b6b8233850857", "9837aa7584904ef7bdc8e4bbb128ba40", "881063785b8b43e7a86c1de04d42e5c1", "25df0a34e4b349d29ac206eaa36c241d", "f7211751c1384a36b7466ebd8f0e288d", "e4d31c45c3124a0586586eb993f7bc9d", "4c7abe27dc404dea90a0cd6a196c70c4", "2b1ea9a27b73477abfb5804d7f45f805", "8516a83f732f40e3afa2399a90d13eb9", "ceb06c7e5e0a46fab78224e734ca6c17", "bb1ef1c7a49c42ae885070a52ede2035", "c8aa6e8d98924f328db627b73d147be5", "cf8be049f5514207b790fc42458b9fbe", "36e521f3005f4e098eb2d523587a0d7a", "d98d233a558d4f7389fd530acda855fe", "c751bde3426747b498a11f16b70bdc41", "f118255250004e2cba2295993c5aa082", "57a8e3bfb8f24ecdb9a28237f6c5fd95", "4e03dad574a64e8c844c2b6c2e60811b", "ca788245ecd540fb9d99deff1d1059c2", "8558789a3fc34d64b3a8bfff3e66132f", "2aaa96d1868a489c874f2d8a3b10b7fa", "a93ed370645c4634af0d189870228a31", "176003174a224871845a98f3899066ba", "984d8d44d8124161b3c7834dd454c66b", "b628979a52264881a07659aaee86ea95", "2579fcd61d7c46a180bfc41ed7bfd77e", "776dee3627564f5da71bce7113a7e740", "051a6e6a5eee499db760abbbba228f19", "97e817c44d734531bb4a21f9a70708dd", "cac05914e6dd4febaec54f6649eed839", "7e828b0bd0904f099e6185276615509d", "192ffcad51d944adae634aeb7063b5f2", "9c895f9f349a4708b42db32ce83a02af", "4ce909dfe1f645bdb92234bd578be953", "294e951c5c7046a389427f9240ef6529", "12cbf197afa54c5794ed57901e07a535", "f0a3883c3c81498d850286bebbc272c7", "293d3434556e4fa88ab86dc4426504e3", "074b2b434ff5420e96cb5071c33442cc", "6936e212d1e54ac9935ee9dc0effab32", "50a7c7334b18453fa8a177a6968f7f24", "6aeaf8ff8b4243ddaf08660405d0de9f", "b83ea89f96e24d33a09c397d73de1fdd", "ab3c09044f6d43abbd5b6370587b4d93", "0023bcc4aaf3481f95e4d0a709bb9ac7", "9abd1849142d4633b37dd20717ab5f21", "8e0ee13db9c24fdf9b32fc8aeaea40d8", "ad724eec0ccd448cb6adac798042d0f3", "ac681ef384a948b1996fed5e23431111", "9a1a6523166d463a8429dee17e4626a1", "5345eb4c78244e0492902bd28046f88d", "50292be14b254a55b6626232aac3dae2", "bafeac4919a9435a8ae925ef2d0629e0", "95f2c40b12c24e6c94777bd2506ee051", "45f02c3ddaf949cea984cd43275cf4f1", "f862711bb01a40f5a9afca453902ab13", "7c9b0a38175f41f986cc0db4f636e4ad", "5c83269e53fa4cd586abf8377ad55491", "7eced5ed7b154d24b4f0eb0e2107ded8", "021647ecd5a84f719b6eca7cbf4ce4dd", "0260afb1a3114d258557d8abb8b648e2", "6b694071e135468fadc31f7a09530e34", "c6ec655ebced41b9b9199b3c95ffb5e6", "0d3ec1aa51b84692a5a80e9a0f4655fc", "6b6e6a4123af4be88a33708a4d6bbe30", "c708bd05142341e4af744ce01a2e1bf7", "f4cf5cd8699a4a0182167576ed89c25a", "f6a51cf9d91845e29e1c1f2a864ae097", "8c5b4e62eb054978b3b23461c2cbf4e7", "f2827bd2e94b43d8b09302ab5a8b3506", "15b870ef0cb8472b913c49539d8536fa", "3a55dbc9e85b4d868f59a2bcc6a7c554", "9a997581ecd24c3faa3ba96d06ba0e35", "28cc265ac3ef46c69ab811fea991f364", "b6c0ceecfb524a918d1c904c0687f58e", "df404bde13224be2813f8a31585eb774", "5e309dc0a13a4742a08bb8eb5a1be968", "9aa030848cd34f6c8cfa17e614e9faae", "482f63e1b3ff42dfa2ce8491e02c386d", "ee55b32cd81e4413bd5f694d190ccaae", "b1bd09849a094f32bcb07c8eab3c2028", "a20b4a36f18846ed93db124fe571f023", "987ffb7983c04071a46d35494a3cf989", "1470721076cf41d984e231d9b052f0b3", "ec251b4f5e5e45ac8b39337b883343d6", "e86b0d3b2abe40019b84d6b9e804b080", "a8f250319fa842998a8bb8d9a26deaf8", "2a1aba2615c047fb9a9e3590800c0215", "df6981f3b26941618a5be9391fbb4dd0", "6d712fffdde643f689c4b0b6fad3fc89", "3be8373f147b4bdba20b054302074948", "2b67722d589842248b956d66dcf09322", "d8c86ccf604547ac9ef9cc91d55fe927", "4fb6a908acca43caaa5ddabbaa8f2bf5", "6e793ab45d834b05ac7924b628baa356", "1b442f31e13b4b18a08669c1a150c615", "19fbc7421db64995995da9ca8b54a99d", "92d8f19db5224c619e864cfd2b2f9b5b", "5858a6ab6aa847b7a38d6916655ce5b8", "1ecf62b41229495f8efa4981ca8177e8", "3b5a94e0428342da8ab16ed0ec906ccd", "341c06f3bf6e4b29a4010babe07eeebf", "233cb3581524453ca31b51d2dca916f3", "0061f308bdc2484fb81b884cfdc4d7d5", "f0b213f0db1e4450b8fed947932cd0e9", "0e2bcaff68fd4fb98788fac9e5c6596b", "bd7cf408e0414400914e2643094a262a", "0361566722974545a43de179dab68ecc", "cdc430ed8e594fe1905cbe3d3882a622", "3e9a8333200a445982edadb13efcf5d8", "eaf3a213ac594cf8818b19e75e7e0057", "1fb62612a1f845a890d77a38b6b5223e", "c82ec150d5a5486d9712f9f87363e8da", "d108b3be110642898a6896b62e369c2f", "6a5cbc388ca04f17930fa51e3b479d33", "dce6f13b0bf447839d3d71e4d174ad41", "282e99c67b944ee1ae76357e86c648bf", "4738ccdd38af4d3798a92f67dad16e04", "82899aa739f54d98aa9c7955f18dc2c8", "b223dd4676424856b9264e2c0e7c6eab", "fce4280420754dd694d79d83d568dda4", "6a5e97f44eae45f9898cae48910f6d72", "c070911b61d349b39559a25b699b240a", "952cdd03d79d40b2aa93f25c990860b1", "78fade87f26f4cf5b54270e30813c4ba", "19ea69e25bd74105ba657f9dd88e137c", "13ea918b604c46e0aafa2114549c0b0e", "5591e8c671a24baa97ac2ba9dd87d681", "ae7d5d105ea044598e76be5bc0cc0ccd", "65bb4bfa2d834c4c8bfbeae48808b3a4", "0309575b12f3437697263a2a07f33f77", "c97068aba0034a11bf8aaeca27d07829", "7b57fda9ecc849b2a9cfa8eb413c8550", "697d818fd9934b68b0e8f876532677d1", "4a9e38877ca545268258fb17ae9445bc", "6cc21d0d49dd4ecfab03953533924a25", "686f8cf3b0d04a3b8dffad47ed8e1404", "0580e93ad0bf4a62ab9684b8a517cc2c", "92fe0f8643bb465a9f9935b00b36d558", "bf3228423bd64b95af2503c3e4a007e8", "e65894c129cb4d1b9d23067e16c018c8", "7ffc411962e74dff8be3735428911b8e"]}, "id": "GLdm3XwL_RmY", "outputId": "f1c4117f-9582-4889-faf5-52f38e10e5a7"}, "execution_count": 13, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Downloading (…)9a243/.gitattributes:   0%|          | 0.00/1.52k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "903d15f299df449da52b6b8233850857"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Downloading (…)_Pooling/config.json:   0%|          | 0.00/190 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "c8aa6e8d98924f328db627b73d147be5"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Downloading (…)1e3c49a243/README.md:   0%|          | 0.00/90.1k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "a93ed370645c4634af0d189870228a31"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Downloading (…)3c49a243/config.json:   0%|          | 0.00/719 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "9c895f9f349a4708b42db32ce83a02af"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Downloading (…)ce_transformers.json:   0%|          | 0.00/124 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "ab3c09044f6d43abbd5b6370587b4d93"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Downloading model.safetensors:   0%|          | 0.00/438M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "45f02c3ddaf949cea984cd43275cf4f1"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Downloading pytorch_model.bin:   0%|          | 0.00/438M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "c708bd05142341e4af744ce01a2e1bf7"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Downloading (…)nce_bert_config.json:   0%|          | 0.00/52.0 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "5e309dc0a13a4742a08bb8eb5a1be968"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Downloading (…)cial_tokens_map.json:   0%|          | 0.00/125 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "2a1aba2615c047fb9a9e3590800c0215"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Downloading (…)9a243/tokenizer.json:   0%|          | 0.00/711k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "5858a6ab6aa847b7a38d6916655ce5b8"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Downloading (…)okenizer_config.json:   0%|          | 0.00/366 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "3e9a8333200a445982edadb13efcf5d8"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Downloading (…)1e3c49a243/vocab.txt:   0%|          | 0.00/232k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "fce4280420754dd694d79d83d568dda4"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Downloading (…)c49a243/modules.json:   0%|          | 0.00/349 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "c97068aba0034a11bf8aaeca27d07829"}}, "metadata": {}}]}, {"cell_type": "code", "source": ["service_context = ServiceContext.from_defaults(llm = llm, embed_model=embed_model, chunk_size = 800, chunk_overlap=20)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "VQKKqd3d9fgZ", "outputId": "04cd8ee3-6703-4c2b-99d7-d91aa1ccaf73"}, "execution_count": 14, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["[nltk_data] Downloading package punkt to /tmp/llama_index...\n", "[nltk_data]   Unzipping tokenizers/punkt.zip.\n"]}]}, {"cell_type": "code", "source": ["index = VectorStoreIndex.from_documents(documents, service_context= service_context)"], "metadata": {"id": "JDcjlbu1-ity"}, "execution_count": 15, "outputs": []}, {"cell_type": "markdown", "source": ["## Storing and Loading the Index"], "metadata": {"id": "ctjpkZp7AAOh"}}, {"cell_type": "code", "source": ["index.storage_context.persist()"], "metadata": {"id": "ylWymjiv_7sa"}, "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["# Loading the index\n", "# storage_context = StorageContext.from_defaults(persist_dir = './storage')\n", "# index = load_index_from_storage(storage_context=storage_context)"], "metadata": {"id": "XPQ3253aAGih"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Q/A"], "metadata": {"id": "ZI2kDnDNAfpI"}}, {"cell_type": "code", "source": ["query_engine = index.as_query_engine()"], "metadata": {"id": "Ql4wicxIAa0J"}, "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": ["response = query_engine.query(\"What is llamaindex?\")\n", "response"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "hUEBbTA-AayB", "outputId": "18858f12-d3bd-420a-ba81-78291415aaa0"}, "execution_count": 29, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Response(response='LlamaIndex is a data framework for building LLM applications. It provides a comprehensive toolkit for ingestion, management, and querying of your external data so that you can use it with your LLM app.', source_nodes=[NodeWithScore(node=TextNode(id_='138b2e41-7dea-4c40-9f14-fdaa958ae90d', embedding=None, metadata={'file_name': '04. LlamaIndex.docx', 'file_path': 'data/04. LlamaIndex.docx', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={<NodeRelationship.SOURCE: '1'>: RelatedNodeInfo(node_id='8f116f6f-e26f-4531-a60a-ce72a6e51d31', node_type=<ObjectType.DOCUMENT: '4'>, metadata={'file_name': '04. LlamaIndex.docx', 'file_path': 'data/04. LlamaIndex.docx', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, hash='1b78ceb8a9386843bea0785fc6b5485d2292402f7bba1508ee0f3ce698a7b564'), <NodeRelationship.NEXT: '3'>: RelatedNodeInfo(node_id='17b97524-585d-42d9-be1d-62fa7f18d169', node_type=<ObjectType.TEXT: '1'>, metadata={'file_name': '04. LlamaIndex.docx', 'file_path': 'data/04. LlamaIndex.docx', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, hash='34d1e1de1d79b062c235057445f1b67de89da7f85b5db5ac9f5dbfc2ffd9af52')}, hash='7ca8941e807e7a1d99de4de8c5930dc85ee94894010359ed7e3797b04ddae4e1', text=\"What is LlamaIndex?\\n\\n\\n\\nLlamaIndex is a data framework for building LLM applications. It provides a comprehensive toolkit for ingestion, management, and querying of your external data so that you can use it with your LLM app.  \\n\\n\\n\\n\\n\\nChatGPT is trained on huge amounts of data. But what if you wish to train ChatGPT on your private data. There are 3 ways in which you can achieve this.\\n\\n\\n\\n\\n\\n1.   Train an open-source LLM like Llama on your data. This is a complex and time taking process which is not scalable.\\n\\n2.   Pass all of your documents as prompt to LLM. This has limitations since the context window size is limited.\\n\\n3.   Fetch and pass only the relevant documents as input to your LLM.\\n\\n\\n\\nLlamaIndex works using the 3rd method and we will study how we can do that with the help of an example. Some of the concepts being covered by Index etc. will be covered in more detail in the upcoming lessons.\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nHere is the official docs of LlamaIndex\\n\\n https://www.llamaindex.ai/\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nConnect to external Data using LlamaIndex.\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nWe know chatGPT is trained on public data. ChatGPT is nothing but its a LLM model which is trained in this public datasets available over the internet. But let's say you want to use chatgpt to be worked with your custom datasets how to do it. That's where exactly LlamaIndex comes into the picture. Using LlamaIndex we can connect our custom documents into LLM models. So once we do it out LLM model won’t reach out to the public data instead it will provide the response from out custom dataset as we can from the figure mentioned above.\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nSteps to build apps using LlamaIndex.\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nWe will be implementing one simple project using LlamaIndex so that we can learn how LlamaIndex works internally. When we will be using LlamaIndex to build apps the very first step would be loading the data. Data can be in any form let's say it could be API, PDF, DOCS, or text data but once we will be loading the data it would be created as documents \\n\\nObject.\\n\\n\\n\\nThis document object can be created by two techniques either we can manually create it or we can use the load_data function. Once our document object is created the next step would be the Index creation. This Index is going to help us query our data so that we can\\n\\nQuery our data it will automatically scan the documents and respond the ans to us as you can see above figure. \\n\\n\\n\\n\\n\\nWhat is In Context Learning & Fine Tuning?\\n\\n\\n\\n\\n\\nIn context Learning:\\n\\n\\n\\nIn context, learning is very useful if we don’t have direct access to the model.\\n\\nHere we use the model through an API. So no change in the LLM model parameters is required.\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nFine Tuning:\\n\\n\\n\\nFine-tuning is performed by training the LLM on a specific task using a dataset relevant to the task. The training process involves adjusting the model’s parameters to minimize the differences between the model’s predictions and the actual outputs in the dataset.\", start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'), score=0.8519197371204124), NodeWithScore(node=TextNode(id_='17b97524-585d-42d9-be1d-62fa7f18d169', embedding=None, metadata={'file_name': '04. LlamaIndex.docx', 'file_path': 'data/04. LlamaIndex.docx', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, excluded_embed_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], excluded_llm_metadata_keys=['creation_date', 'last_modified_date', 'last_accessed_date'], relationships={<NodeRelationship.SOURCE: '1'>: RelatedNodeInfo(node_id='8f116f6f-e26f-4531-a60a-ce72a6e51d31', node_type=<ObjectType.DOCUMENT: '4'>, metadata={'file_name': '04. LlamaIndex.docx', 'file_path': 'data/04. LlamaIndex.docx', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, hash='1b78ceb8a9386843bea0785fc6b5485d2292402f7bba1508ee0f3ce698a7b564'), <NodeRelationship.PREVIOUS: '2'>: RelatedNodeInfo(node_id='138b2e41-7dea-4c40-9f14-fdaa958ae90d', node_type=<ObjectType.TEXT: '1'>, metadata={'file_name': '04. LlamaIndex.docx', 'file_path': 'data/04. LlamaIndex.docx', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, hash='7ca8941e807e7a1d99de4de8c5930dc85ee94894010359ed7e3797b04ddae4e1')}, hash='34d1e1de1d79b062c235057445f1b67de89da7f85b5db5ac9f5dbfc2ffd9af52', text='You can refer to this documentation for fine-tuning: \\n\\n\\n\\nhttps://huggingface.co/docs/transformers/training\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nHow do LlamaIndex applications work (Diagram)?\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nWhy indexing required in LLM apps?\\n\\n\\n\\nIndexing is required because it has the limitation. Each model can only \\n\\nProcess up to a certain number of tokens\\n\\n\\n\\nEx: 1000 tokens = 750 words\\n\\n\\n\\nWhat if you need to process a large amount of documents? Here this indexing concept comes into the picture', start_char_idx=None, end_char_idx=None, text_template='{metadata_str}\\n\\n{content}', metadata_template='{key}: {value}', metadata_seperator='\\n'), score=0.8480016917284606)], metadata={'138b2e41-7dea-4c40-9f14-fdaa958ae90d': {'file_name': '04. LlamaIndex.docx', 'file_path': 'data/04. LlamaIndex.docx', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}, '17b97524-585d-42d9-be1d-62fa7f18d169': {'file_name': '04. LlamaIndex.docx', 'file_path': 'data/04. LlamaIndex.docx', 'creation_date': '2023-11-13', 'last_modified_date': '2023-11-13', 'last_accessed_date': '2023-11-13'}})"]}, "metadata": {}, "execution_count": 29}]}, {"cell_type": "code", "source": ["from IPython.display import Markdown, display"], "metadata": {"id": "JqQZlIzoA7QS"}, "execution_count": 30, "outputs": []}, {"cell_type": "code", "source": ["display(Markdown(f\"<b>{response}</b>\"))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 64}, "id": "rfYaAU0sBEUK", "outputId": "11242986-a319-457d-837f-1eeee77ce696"}, "execution_count": 31, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Markdown object>"], "text/markdown": "<b>LlamaIndex is a data framework for building LLM applications. It provides a comprehensive toolkit for ingestion, management, and querying of your external data so that you can use it with your LLM app.</b>"}, "metadata": {}}]}, {"cell_type": "code", "source": [], "metadata": {"id": "E89gUt9vBHwK"}, "execution_count": null, "outputs": []}]}