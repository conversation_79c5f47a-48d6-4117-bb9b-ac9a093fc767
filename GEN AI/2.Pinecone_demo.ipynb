{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["## Install All the Required Packages"], "metadata": {"id": "DVI3tJKR4naq"}}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "3APQ7Sdk2H2A", "outputId": "d20a61cf-919f-4dc8-cf95-ec4838de2279"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting langchain\n", "  Downloading langchain-0.0.330-py3-none-any.whl (2.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m25.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.0.22)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (3.8.6)\n", "Requirement already satisfied: anyio<4.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (3.7.1)\n", "Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (4.0.3)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain)\n", "  Downloading dataclasses_json-0.6.1-py3-none-any.whl (27 kB)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain)\n", "  Downloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Collecting langsmith<0.1.0,>=0.0.52 (from langchain)\n", "  Downloading langsmith-0.0.57-py3-none-any.whl (44 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m44.5/44.5 kB\u001b[0m \u001b[31m6.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.23.5)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.10.13)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (8.2.3)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (23.1.0)\n", "Requirement already satisfied: charset-normalizer<4.0,>=2.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (3.3.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (6.0.4)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.9.2)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.4.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.3.1)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.10/dist-packages (from anyio<4.0->langchain) (3.4)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.10/dist-packages (from anyio<4.0->langchain) (1.3.0)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio<4.0->langchain) (1.1.3)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain)\n", "  Downloading marshmallow-3.20.1-py3-none-any.whl (49 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.4/49.4 kB\u001b[0m \u001b[31m7.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain)\n", "  Downloading jsonpointer-2.4-py2.py3-none-any.whl (7.8 kB)\n", "Requirement already satisfied: typing-extensions>=4.2.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (4.5.0)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2023.7.22)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.0.0)\n", "Requirement already satisfied: packaging>=17.0 in /usr/local/lib/python3.10/dist-packages (from marshmallow<4.0.0,>=3.18.0->dataclasses-json<0.7,>=0.5.7->langchain) (23.2)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Installing collected packages: mypy-extensions, marshmallow, jsonpointer, typing-inspect, langsmith, jsonpatch, dataclasses-json, langchain\n", "Successfully installed dataclasses-json-0.6.1 jsonpatch-1.33 jsonpointer-2.4 langchain-0.0.330 langsmith-0.0.57 marshmallow-3.20.1 mypy-extensions-1.0.0 typing-inspect-0.9.0\n", "Collecting pinecone-client\n", "  Downloading pinecone_client-2.2.4-py3-none-any.whl (179 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m179.4/179.4 kB\u001b[0m \u001b[31m3.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: requests>=2.19.0 in /usr/local/lib/python3.10/dist-packages (from pinecone-client) (2.31.0)\n", "Requirement already satisfied: pyyaml>=5.4 in /usr/local/lib/python3.10/dist-packages (from pinecone-client) (6.0.1)\n", "Collecting loguru>=0.5.0 (from pinecone-client)\n", "  Downloading loguru-0.7.2-py3-none-any.whl (62 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m62.5/62.5 kB\u001b[0m \u001b[31m7.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: typing-extensions>=3.7.4 in /usr/local/lib/python3.10/dist-packages (from pinecone-client) (4.5.0)\n", "Collecting dnspython>=2.0.0 (from pinecone-client)\n", "  Downloading dnspython-2.4.2-py3-none-any.whl (300 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m300.4/300.4 kB\u001b[0m \u001b[31m8.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: python-dateutil>=2.5.3 in /usr/local/lib/python3.10/dist-packages (from pinecone-client) (2.8.2)\n", "Requirement already satisfied: urllib3>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from pinecone-client) (2.0.7)\n", "Requirement already satisfied: tqdm>=4.64.1 in /usr/local/lib/python3.10/dist-packages (from pinecone-client) (4.66.1)\n", "Requirement already satisfied: numpy>=1.22.0 in /usr/local/lib/python3.10/dist-packages (from pinecone-client) (1.23.5)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.5.3->pinecone-client) (1.16.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.19.0->pinecone-client) (3.3.1)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests>=2.19.0->pinecone-client) (3.4)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests>=2.19.0->pinecone-client) (2023.7.22)\n", "Installing collected packages: loguru, dnspython, pinecone-client\n", "Successfully installed dnspython-2.4.2 loguru-0.7.2 pinecone-client-2.2.4\n", "Collecting pypdf\n", "  Downloading pypdf-3.17.0-py3-none-any.whl (277 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m277.4/277.4 kB\u001b[0m \u001b[31m6.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: pypdf\n", "Successfully installed pypdf-3.17.0\n"]}], "source": ["!pip install langchain\n", "!pip install pinecone-client\n", "!pip install pypdf"]}, {"cell_type": "code", "source": ["!pip install openai\n", "!pip install tiktoken"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_P_QHai95FgW", "outputId": "986fc9d3-eebf-49ed-98c5-b161bd532f41"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting openai\n", "  Downloading openai-0.28.1-py3-none-any.whl (76 kB)\n", "\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/77.0 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.0/77.0 kB\u001b[0m \u001b[31m2.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: requests>=2.20 in /usr/local/lib/python3.10/dist-packages (from openai) (2.31.0)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from openai) (4.66.1)\n", "Requirement already satisfied: aiohttp in /usr/local/lib/python3.10/dist-packages (from openai) (3.8.6)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.20->openai) (3.3.1)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests>=2.20->openai) (3.4)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.20->openai) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests>=2.20->openai) (2023.7.22)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai) (23.1.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai) (6.0.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0.0a3 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai) (4.0.3)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai) (1.9.2)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai) (1.4.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai) (1.3.1)\n", "Installing collected packages: openai\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "llmx 0.0.15a0 requires cohere, which is not installed.\n", "llmx 0.0.15a0 requires tiktoken, which is not installed.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed openai-0.28.1\n", "Collecting tik<PERSON>en\n", "  Downloading tiktoken-0.5.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m9.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: regex>=2022.1.18 in /usr/local/lib/python3.10/dist-packages (from tiktoken) (2023.6.3)\n", "Requirement already satisfied: requests>=2.26.0 in /usr/local/lib/python3.10/dist-packages (from tiktoken) (2.31.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken) (3.3.1)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken) (3.4)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken) (2023.7.22)\n", "Installing collected packages: tiktoken\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "llmx 0.0.15a0 requires cohere, which is not installed.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed tiktoken-0.5.1\n"]}]}, {"cell_type": "markdown", "source": ["## Import All the Required Libraries"], "metadata": {"id": "3WNrsq005Oa5"}}, {"cell_type": "code", "source": ["from langchain.document_loaders import PyPDFDirectoryLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain.embeddings import OpenAIEmbeddings\n", "from langchain.llms import OpenAI\n", "from langchain.vectorstores import Pinecone\n", "from langchain.chains import RetrievalQA\n", "from langchain.prompts import PromptTemplate\n", "import os"], "metadata": {"id": "QadipyWC5FeA"}, "execution_count": 3, "outputs": []}, {"cell_type": "markdown", "source": ["## Load the PDF Files"], "metadata": {"id": "Jt3yEYqk5l1G"}}, {"cell_type": "code", "source": ["!mkdir pdfs"], "metadata": {"id": "5aPGq0qI5Fbc"}, "execution_count": 4, "outputs": []}, {"cell_type": "code", "source": ["!gdown 1hPQlXrX8FbaYaLypxTmeVOFNitbBMlEE -O pdfs/yolov7paper.pdf\n", "!gdown 1vILwiv6nS2wI3chxNabMgry3qnV67TxM -O pdfs/rachelgreecv.pdf"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "717PStbq5FZN", "outputId": "77cf9446-0113-4532-abee-25a4fb03f506"}, "execution_count": 5, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Downloading...\n", "From: https://drive.google.com/uc?id=1hPQlXrX8FbaYaLypxTmeVOFNitbBMlEE\n", "To: /content/pdfs/yolov7paper.pdf\n", "100% 2.27M/2.27M [00:00<00:00, 14.6MB/s]\n", "Downloading...\n", "From: https://drive.google.com/uc?id=1vILwiv6nS2wI3chxNabMgry3qnV67TxM\n", "To: /content/pdfs/rachelgreecv.pdf\n", "100% 271k/271k [00:00<00:00, 3.62MB/s]\n"]}]}, {"cell_type": "markdown", "source": ["## Extract the Text from the PDF's"], "metadata": {"id": "l9G_3VNU523F"}}, {"cell_type": "code", "source": ["loader = PyPDFDirectoryLoader(\"pdfs\")\n", "data = loader.load()"], "metadata": {"id": "bLAl0c4o51Et"}, "execution_count": 6, "outputs": []}, {"cell_type": "code", "source": ["data"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "EULIR0lq58_l", "outputId": "3a30fbb4-2eeb-4e6a-f1d6-33a9058ca3c5"}, "execution_count": 7, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='YOLOv7: Trainable bag-of-freebies sets new state-of-the-art for real-time object\\ndetectors\\nC<PERSON><PERSON>-<PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>\\n1Institute of Information Science, Academia Sinica, Taiwan\\<EMAIL>, <EMAIL>, and <EMAIL>\\nAbstract\\nYOLOv7 surpasses all known object detectors in both\\nspeed and accuracy in the range from 5 FPS to 160 FPS\\nand has the highest accuracy 56.8% AP among all known\\nreal-time object detectors with 30 FPS or higher on GPU\\nV100. YOLOv7-E6 object detector (56 FPS V100, 55.9%\\nAP) outperforms both transformer-based detector SWIN-\\nL Cascade-Mask R-CNN (9.2 FPS A100, 53.9% AP) by\\n509% in speed and 2% in accuracy, and convolutional-\\nbased detector ConvNeXt-XL Cascade-Mask R-CNN (8.6\\nFPS A100, 55.2% AP) by 551% in speed and 0.7% AP\\nin accuracy, as well as YOLOv7 outperforms: YOLOR,\\nYOLOX, Scaled-YOLOv4, YOLOv5, DETR, Deformable\\nDETR, DINO-5scale-R50, ViT-Adapter-B and many other\\nobject detectors in speed and accuracy. Moreover, we train\\nYOLOv7 only on MS COCO dataset from scratch without\\nusing any other datasets or pre-trained weights. Source\\ncode is released in https://github.com/WongKinYiu/yolov7.\\n1. Introduction\\nReal-time object detection is a very important topic in\\ncomputer vision, as it is often a necessary component in\\ncomputer vision systems. For example, multi-object track-\\ning [94, 93], autonomous driving [40, 18], robotics [35, 58],\\nmedical image analysis [34, 46], etc. The computing de-\\nvices that execute real-time object detection is usually some\\nmobile CPU or GPU, as well as various neural processing\\nunits (NPU) developed by major manufacturers. For exam-\\nple, the Apple neural engine (Apple), the neural compute\\nstick (Intel), Jetson AI edge devices (Nvidia), the edge TPU\\n(Google), the neural processing engine (Qualcomm), the AI\\nprocessing unit (MediaTek), and the AI SoCs (Kneron), are\\nall NPUs. Some of the above mentioned edge devices focus\\non speeding up different operations such as vanilla convolu-\\ntion, depth-wise convolution, or MLP operations. In this pa-\\nper, the real-time object detector we proposed mainly hopes\\nthat it can support both mobile GPU and GPU devices from\\nthe edge to the cloud.\\nIn recent years, the real-time object detector is still de-\\nveloped for different edge device. For example, the devel-\\nFigure 1: Comparison with other real-time object detectors, our\\nproposed methods achieve state-of-the-arts performance.\\nopment of MCUNet [49, 48] and NanoDet [54] focused on\\nproducing low-power single-chip and improving the infer-\\nence speed on edge CPU. As for methods such as YOLOX\\n[21] and YOLOR [81], they focus on improving the infer-\\nence speed of various GPUs. More recently, the develop-\\nment of real-time object detector has focused on the de-\\nsign of efﬁcient architecture. As for real-time object de-\\ntectors that can be used on CPU [54, 88, 84, 83], their de-\\nsign is mostly based on MobileNet [28, 66, 27], ShufﬂeNet\\n[92, 55], or GhostNet [25]. Another mainstream real-time\\nobject detectors are developed for GPU [81, 21, 97], they\\nmostly use ResNet [26], DarkNet [63], or DLA [87], and\\nthen use the CSPNet [80] strategy to optimize the architec-\\nture. The development direction of the proposed methods in\\nthis paper are different from that of the current mainstream\\nreal-time object detectors. In addition to architecture op-\\ntimization, our proposed methods will focus on the opti-\\nmization of the training process. Our focus will be on some\\noptimized modules and optimization methods which may\\nstrengthen the training cost for improving the accuracy of\\nobject detection, but without increasing the inference cost.\\nWe call the proposed modules and optimization methods\\ntrainable bag-of-freebies.\\n1arXiv:2207.02696v1  [cs.CV]  6 Jul 2022', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 0}),\n", " Document(page_content='Recently, model re-parameterization [13, 12, 29] and dy-\\nnamic label assignment [20, 17, 42] have become important\\ntopics in network training and object detection. Mainly af-\\nter the above new concepts are proposed, the training of\\nobject detector evolves many new issues. In this paper, we\\nwill present some of the new issues we have discovered and\\ndevise effective methods to address them. For model re-\\nparameterization, we analyze the model re-parameterization\\nstrategies applicable to layers in different networks with the\\nconcept of gradient propagation path, and propose planned\\nre-parameterized model. In addition, when we discover that\\nwith dynamic label assignment technology, the training of\\nmodel with multiple output layers will generate new issues.\\nThat is: “How to assign dynamic targets for the outputs of\\ndifferent branches?” For this problem, we propose a new\\nlabel assignment method called coarse-to-ﬁne lead guided\\nlabel assignment.\\nThe contributions of this paper are summarized as fol-\\nlows: (1) we design several trainable bag-of-freebies meth-\\nods, so that real-time object detection can greatly improve\\nthe detection accuracy without increasing the inference\\ncost; (2) for the evolution of object detection methods, we\\nfound two new issues, namely how re-parameterized mod-\\nule replaces original module, and how dynamic label as-\\nsignment strategy deals with assignment to different output\\nlayers. In addition, we also propose methods to address the\\ndifﬁculties arising from these issues; (3) we propose “ex-\\ntend” and “compound scaling” methods for the real-time\\nobject detector that can effectively utilize parameters and\\ncomputation; and (4) the method we proposed can effec-\\ntively reduce about 40% parameters and 50% computation\\nof state-of-the-art real-time object detector, and has faster\\ninference speed and higher detection accuracy.\\n2. Related work\\n2.1. Real-time object detectors\\nCurrently state-of-the-art real-time object detectors are\\nmainly based on YOLO [61, 62, 63] and FCOS [76, 77],\\nwhich are [3, 79, 81, 21, 54, 85, 23]. Being able to become\\na state-of-the-art real-time object detector usually requires\\nthe following characteristics: (1) a faster and stronger net-\\nwork architecture; (2) a more effective feature integration\\nmethod [22, 97, 37, 74, 59, 30, 9, 45]; (3) a more accurate\\ndetection method [76, 77, 69]; (4) a more robust loss func-\\ntion [96, 64, 6, 56, 95, 57]; (5) a more efﬁcient label assign-\\nment method [99, 20, 17, 82, 42]; and (6) a more efﬁcient\\ntraining method. In this paper, we do not intend to explore\\nself-supervised learning or knowledge distillation methods\\nthat require additional data or large model. Instead, we will\\ndesign new trainable bag-of-freebies method for the issues\\nderived from the state-of-the-art methods associated with\\n(4), (5), and (6) mentioned above.2.2. Model re-parameterization\\nModel re-parametrization techniques [71, 31, 75, 19, 33,\\n11, 4, 24, 13, 12, 10, 29, 14, 78] merge multiple compu-\\ntational modules into one at inference stage. The model\\nre-parameterization technique can be regarded as an en-\\nsemble technique, and we can divide it into two cate-\\ngories, i.e., module-level ensemble and model-level ensem-\\nble. There are two common practices for model-level re-\\nparameterization to obtain the ﬁnal inference model. One\\nis to train multiple identical models with different train-\\ning data, and then average the weights of multiple trained\\nmodels. The other is to perform a weighted average of the\\nweights of models at different iteration number. Module-\\nlevel re-parameterization is a more popular research issue\\nrecently. This type of method splits a module into multi-\\nple identical or different module branches during training\\nand integrates multiple branched modules into a completely\\nequivalent module during inference. However, not all pro-\\nposed re-parameterized module can be perfectly applied to\\ndifferent architectures. With this in mind, we have devel-\\noped new re-parameterization module and designed related\\napplication strategies for various architectures.\\n2.3. Model scaling\\nModel scaling [72, 60, 74, 73, 15, 16, 2, 51] is a way\\nto scale up or down an already designed model and make\\nit ﬁt in different computing devices. The model scaling\\nmethod usually uses different scaling factors, such as reso-\\nlution (size of input image), depth (number of layer), width\\n(number of channel), and stage (number of feature pyra-\\nmid), so as to achieve a good trade-off for the amount of\\nnetwork parameters, computation, inference speed, and ac-\\ncuracy. Network architecture search (NAS) is one of the\\ncommonly used model scaling methods. NAS can automat-\\nically search for suitable scaling factors from search space\\nwithout deﬁning too complicated rules. The disadvantage\\nof NAS is that it requires very expensive computation to\\ncomplete the search for model scaling factors. In [15], the\\nresearcher analyzes the relationship between scaling factors\\nand the amount of parameters and operations, trying to di-\\nrectly estimate some rules, and thereby obtain the scaling\\nfactors required by model scaling. Checking the literature,\\nwe found that almost all model scaling methods analyze in-\\ndividual scaling factor independently, and even the methods\\nin the compound scaling category also optimized scaling\\nfactor independently. The reason for this is because most\\npopular NAS architectures deal with scaling factors that are\\nnot very correlated. We observed that all concatenation-\\nbased models, such as DenseNet [32] or V oVNet [39], will\\nchange the input width of some layers when the depth of\\nsuch models is scaled. Since the proposed architecture is\\nconcatenation-based, we have to design a new compound\\nscaling method for this model.\\n2', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 1}),\n", " Document(page_content='Figure 2: Extended efﬁcient layer aggregation networks. The proposed extended ELAN (E-ELAN) does not change the gradient transmis-\\nsion path of the original architecture at all, but use group convolution to increase the cardinality of the added features, and combine the\\nfeatures of different groups in a shufﬂe and merge cardinality manner. This way of operation can enhance the features learned by different\\nfeature maps and improve the use of parameters and calculations.\\n3. Architecture\\n3.1. Extended efﬁcient layer aggregation networks\\nIn most of the literature on designing the efﬁcient ar-\\nchitectures, the main considerations are no more than the\\nnumber of parameters, the amount of computation, and the\\ncomputational density. Starting from the characteristics of\\nmemory access cost, <PERSON> et al. [55] also analyzed the in-\\nﬂuence of the input/output channel ratio, the number of\\nbranches of the architecture, and the element-wise opera-\\ntion on the network inference speed. Doll ´aret al. [15] addi-\\ntionally considered activation when performing model scal-\\ning, that is, to put more consideration on the number of el-\\nements in the output tensors of convolutional layers. The\\ndesign of CSPV oVNet [79] in Figure 2 (b) is a variation of\\nV oVNet [39]. In addition to considering the aforementioned\\nbasic designing concerns, the architecture of CSPV oVNet\\n[79] also analyzes the gradient path, in order to enable the\\nweights of different layers to learn more diverse features.\\nThe gradient analysis approach described above makes in-\\nferences faster and more accurate. ELAN [1] in Figure 2 (c)\\nconsiders the following design strategy – “How to design an\\nefﬁcient network?.” They came out with a conclusion: By\\ncontrolling the shortest longest gradient path, a deeper net-\\nwork can learn and converge effectively. In this paper, we\\npropose Extended-ELAN (E-ELAN) based on ELAN and\\nits main architecture is shown in Figure 2 (d).\\nRegardless of the gradient path length and the stacking\\nnumber of computational blocks in large-scale ELAN, it has\\nreached a stable state. If more computational blocks are\\nstacked unlimitedly, this stable state may be destroyed, and\\nthe parameter utilization rate will decrease. The proposedE-ELAN uses expand, shufﬂe, merge cardinality to achieve\\nthe ability to continuously enhance the learning ability of\\nthe network without destroying the original gradient path.\\nIn terms of architecture, E-ELAN only changes the archi-\\ntecture in computational block, while the architecture of\\ntransition layer is completely unchanged. Our strategy is\\nto use group convolution to expand the channel and car-\\ndinality of computational blocks. We will apply the same\\ngroup parameter and channel multiplier to all the compu-\\ntational blocks of a computational layer. Then, the feature\\nmap calculated by each computational block will be shuf-\\nﬂed into ggroups according to the set group parameter g,\\nand then concatenate them together. At this time, the num-\\nber of channels in each group of feature map will be the\\nsame as the number of channels in the original architec-\\nture. Finally, we add ggroups of feature maps to perform\\nmerge cardinality. In addition to maintaining the original\\nELAN design architecture, E-ELAN can also guide differ-\\nent groups of computational blocks to learn more diverse\\nfeatures.\\n3.2. Model scaling for concatenation-based models\\nThe main purpose of model scaling is to adjust some at-\\ntributes of the model and generate models of different scales\\nto meet the needs of different inference speeds. For ex-\\nample the scaling model of EfﬁcientNet [72] considers the\\nwidth, depth, and resolution. As for the scaled-YOLOv4\\n[79], its scaling model is to adjust the number of stages. In\\n[15], Doll ´aret al. analyzed the inﬂuence of vanilla convolu-\\ntion and group convolution on the amount of parameter and\\ncomputation when performing width and depth scaling, and\\nused this to design the corresponding model scaling method.\\n3', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 2}),\n", " Document(page_content='Figure 3: Model scaling for concatenation-based models. From (a) to (b), we observe that when depth scaling is performed on\\nconcatenation-based models, the output width of a computational block also increases. This phenomenon will cause the input width\\nof the subsequent transmission layer to increase. Therefore, we propose (c), that is, when performing model scaling on concatenation-\\nbased models, only the depth in a computational block needs to be scaled, and the remaining of transmission layer is performed with\\ncorresponding width scaling.\\nThe above methods are mainly used in architectures such as\\nPlainNet or ResNet. When these architectures are in execut-\\ning scaling up or scaling down, the in-degree and out-degree\\nof each layer will not change, so we can independently an-\\nalyze the impact of each scaling factor on the amount of\\nparameters and computation. However, if these methods\\nare applied to the concatenation-based architecture, we will\\nﬁnd that when scaling up or scaling down is performed on\\ndepth, the in-degree of a translation layer which is immedi-\\nately after a concatenation-based computational block will\\ndecrease or increase, as shown in Figure 3 (a) and (b).\\nIt can be inferred from the above phenomenon that\\nwe cannot analyze different scaling factors separately for\\na concatenation-based model but must be considered to-\\ngether. Take scaling-up depth as an example, such an ac-\\ntion will cause a ratio change between the input channel and\\noutput channel of a transition layer, which may lead to a de-\\ncrease in the hardware usage of the model. Therefore, we\\nmust propose the corresponding compound model scaling\\nmethod for a concatenation-based model. When we scale\\nthe depth factor of a computational block, we must also cal-\\nculate the change of the output channel of that block. Then,\\nwe will perform width factor scaling with the same amount\\nof change on the transition layers, and the result is shown\\nin Figure 3 (c). Our proposed compound scaling method\\ncan maintain the properties that the model had at the initial\\ndesign and maintains the optimal structure.\\n4. Trainable bag-of-freebies\\n4.1. Planned re-parameterized convolution\\nAlthough RepConv [13] has achieved excellent perfor-\\nmance on the VGG [68], when we directly apply it to\\nResNet [26] and DenseNet [32] and other architectures,\\nits accuracy will be signiﬁcantly reduced. We use gradi-\\nent ﬂow propagation paths to analyze how re-parameterized\\nconvolution should be combined with different network.\\nWe also designed planned re-parameterized convolution ac-\\ncordingly.\\nFigure 4: Planned re-parameterized model. In the proposed\\nplanned re-parameterized model, we found that a layer with resid-\\nual or concatenation connections, its RepConv should not have\\nidentity connection. Under these circumstances, it can be replaced\\nby RepConvN that contains no identity connections.\\nRepConv actually combines 3×3convolution, 1×1\\nconvolution, and identity connection in one convolutional\\nlayer. After analyzing the combination and correspond-\\ning performance of RepConv and different architectures,\\nwe ﬁnd that the identity connection in RepConv destroys\\nthe residual in ResNet and the concatenation in DenseNet,\\nwhich provides more diversity of gradients for different fea-\\nture maps. For the above reasons, we use RepConv with-\\nout identity connection (RepConvN) to design the architec-\\nture of planned re-parameterized convolution. In our think-\\ning, when a convolutional layer with residual or concate-\\nnation is replaced by re-parameterized convolution, there\\nshould be no identity connection. Figure 4 shows an exam-\\nple of our designed “planned re-parameterized convolution”\\nused in PlainNet and ResNet. As for the complete planned\\nre-parameterized convolution experiment in residual-based\\nmodel and concatenation-based model, it will be presented\\nin the ablation study session.\\n4', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 3}),\n", " Document(page_content='Figure 5: Coarse for auxiliary and ﬁne for lead head label assigner. Compare with normal model (a), the schema in (b) has auxiliary head.\\nDifferent from the usual independent label assigner (c), we propose (d) lead head guided label assigner and (e) coarse-to-ﬁne lead head\\nguided label assigner. The proposed label assigner is optimized by lead head prediction and the ground truth to get the labels of training\\nlead head and auxiliary head at the same time. The detailed coarse-to-ﬁne implementation method and constraint design details will be\\nelaborated in Apendix.\\n4.2. Coarse for auxiliary and ﬁne for lead loss\\nDeep supervision [38] is a technique that is often used\\nin training deep networks. Its main concept is to add\\nextra auxiliary head in the middle layers of the network,\\nand the shallow network weights with assistant loss as the\\nguide. Even for architectures such as ResNet [26] and\\nDenseNet [32] which usually converge well, deep supervi-\\nsion [70, 98, 67, 47, 82, 65, 86, 50] can still signiﬁcantly\\nimprove the performance of the model on many tasks. Fig-\\nure 5 (a) and (b) show, respectively, the object detector ar-\\nchitecture “without” and “with” deep supervision. In this\\npaper, we call the head responsible for the ﬁnal output as\\nthe lead head, and the head used to assist training is called\\nauxiliary head.\\nNext we want to discuss the issue of label assignment. In\\nthe past, in the training of deep network, label assignment\\nusually refers directly to the ground truth and generate hard\\nlabel according to the given rules. However, in recent years,\\nif we take object detection as an example, researchers often\\nuse the quality and distribution of prediction output by the\\nnetwork, and then consider together with the ground truth to\\nuse some calculation and optimization methods to generate\\na reliable soft label [61, 8, 36, 99, 91, 44, 43, 90, 20, 17, 42].\\nFor example, YOLO [61] use IoU of prediction of bounding\\nbox regression and ground truth as the soft label of object-\\nness. In this paper, we call the mechanism that considers\\nthe network prediction results together with the ground truth\\nand then assigns soft labels as “label assigner.”\\nDeep supervision needs to be trained on the target ob-\\njectives regardless of the circumstances of auxiliary head or\\nlead head. During the development of soft label assigner re-\\nlated techniques, we accidentally discovered a new deriva-\\ntive issue, i.e., “How to assign soft label to auxiliary head\\nand lead head ?” To the best of our knowledge, the relevant\\nliterature has not explored this issue so far. The results of\\nthe most popular method at present is as shown in Figure 5\\n(c), which is to separate auxiliary head and lead head, and\\nthen use their own prediction results and the ground truthto execute label assignment. The method proposed in this\\npaper is a new label assignment method that guides both\\nauxiliary head and lead head by the lead head prediction.\\nIn other words, we use lead head prediction as guidance to\\ngenerate coarse-to-ﬁne hierarchical labels, which are used\\nfor auxiliary head and lead head learning, respectively. The\\ntwo proposed deep supervision label assignment strategies\\nare shown in Figure 5 (d) and (e), respectively.\\nLead head guided label assigner is mainly calculated\\nbased on the prediction result of the lead head and the\\nground truth, and generate soft label through the optimiza-\\ntion process. This set of soft labels will be used as the tar-\\nget training model for both auxiliary head and lead head.\\nThe reason to do this is because lead head has a relatively\\nstrong learning capability, so the soft label generated from it\\nshould be more representative of the distribution and corre-\\nlation between the source data and the target. Furthermore,\\nwe can view such learning as a kind of generalized residual\\nlearning. By letting the shallower auxiliary head directly\\nlearn the information that lead head has learned, lead head\\nwill be more able to focus on learning residual information\\nthat has not yet been learned.\\nCoarse-to-ﬁne lead head guided label assigner also\\nused the predicted result of the lead head and the ground\\ntruth to generate soft label. However, in the process we gen-\\nerate two different sets of soft label, i.e., coarse label and\\nﬁne label, where ﬁne label is the same as the soft label gen-\\nerated by lead head guided label assigner, and coarse label\\nis generated by allowing more grids to be treated as posi-\\ntive target by relaxing the constraints of the positive sample\\nassignment process. The reason for this is that the learning\\nability of an auxiliary head is not as strong as that of a lead\\nhead, and in order to avoid losing the information that needs\\nto be learned, we will focus on optimizing the recall of aux-\\niliary head in the object detection task. As for the output\\nof lead head, we can ﬁlter the high precision results from\\nthe high recall results as the ﬁnal output. However, we must\\nnote that if the additional weight of coarse label is close to\\n5', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 4}),\n", " Document(page_content='Table 1: Comparison of baseline object detectors.\\nModel #Param. FLOPs Size APvalAPval\\n50APval\\n75APval\\nSAPval\\nMAPval\\nL\\nYOLOv4 [3] 64.4M 142.8G 640 49.7% 68.2% 54.3% 32.9% 54.8% 63.7%\\nYOLOR-u5 (r6.1) [81] 46.5M 109.1G 640 50.2% 68.7% 54.6% 33.2% 55.5% 63.7%\\nYOLOv4-CSP [79] 52.9M 120.4G 640 50.3% 68.6% 54.9% 34.2% 55.6% 65.1%\\nYOLOR-CSP [81] 52.9M 120.4G 640 50.8% 69.5% 55.3% 33.7% 56.0% 65.4%\\nYOLOv7 36.9M 104.7G 640 51.2% 69.7% 55.5% 35.2% 56.0% 66.7%\\nimprovement -43% -15% - +0.4 +0.2 +0.2 +1.5 = +1.3\\nYOLOR-CSP-X [81] 96.9M 226.8G 640 52.7% 71.3% 57.4% 36.3% 57.5% 68.3%\\nYOLOv7-X 71.3M 189.9G 640 52.9% 71.1% 57.5% 36.9% 57.7% 68.6%\\nimprovement -36% -19% - +0.2 -0.2 +0.1 +0.6 +0.2 +0.3\\nYOLOv4-tiny [79] 6.1 6.9 416 24.9% 42.1% 25.7% 8.7% 28.4% 39.2%\\nYOLOv7-tiny 6.2 5.8 416 35.2% 52.8% 37.3% 15.7% 38.0% 53.4%\\nimprovement +2% -19% - +10.3 +10.7 +11.6 +7.0 +9.6 +14.2\\nYOLOv4-tiny-3l [79] 8.7 5.2 320 30.8% 47.3% 32.2% 10.9% 31.9% 51.5%\\nYOLOv7-tiny 6.2 3.5 320 30.8% 47.3% 32.2% 10.0% 31.9% 52.2%\\nimprovement -39% -49% - = = = -0.9 = +0.7\\nYOLOR-E6 [81] 115.8M 683.2G 1280 55.7% 73.2% 60.7% 40.1% 60.4% 69.2%\\nYOLOv7-E6 97.2M 515.2G 1280 55.9% 73.5% 61.1% 40.6% 60.3% 70.0%\\nimprovement -19% -33% - +0.2 +0.3 +0.4 +0.5 -0.1 +0.8\\nYOLOR-D6 [81] 151.7M 935.6G 1280 56.1% 73.9% 61.2% 42.4% 60.5% 69.9%\\nYOLOv7-D6 154.7M 806.8G 1280 56.3% 73.8% 61.4% 41.3% 60.6% 70.1%\\nYOLOv7-E6E 151.7M 843.2G 1280 56.8% 74.4% 62.1% 40.8% 62.1% 70.6%\\nimprovement = -11% - +0.7 +0.5 +0.9 -1.6 +1.6 +0.7\\nthat of ﬁne label, it may produce bad prior at ﬁnal predic-\\ntion. Therefore, in order to make those extra coarse positive\\ngrids have less impact, we put restrictions in the decoder,\\nso that the extra coarse positive grids cannot produce soft\\nlabel perfectly. The mechanism mentioned above allows\\nthe importance of ﬁne label and coarse label to be dynam-\\nically adjusted during the learning process, and makes the\\noptimizable upper bound of ﬁne label always higher than\\ncoarse label.\\n4.3. Other trainable bag-of-freebies\\nIn this section we will list some trainable bag-of-\\nfreebies. These freebies are some of the tricks we used\\nin training, but the original concepts were not proposed\\nby us. The training details of these freebies will be elab-\\norated in the Appendix, including (1) Batch normalization\\nin conv-bn-activation topology: This part mainly connects\\nbatch normalization layer directly to convolutional layer.\\nThe purpose of this is to integrate the mean and variance\\nof batch normalization into the bias and weight of convolu-\\ntional layer at the inference stage. (2) Implicit knowledge\\nin YOLOR [81] combined with convolution feature map in\\naddition and multiplication manner: Implicit knowledge in\\nYOLOR can be simpliﬁed to a vector by pre-computing at\\nthe inference stage. This vector can be combined with the\\nbias and weight of the previous or subsequent convolutional\\nlayer. (3) EMA model: EMA is a technique used in mean\\nteacher [75], and in our system we use EMA model purely\\nas the ﬁnal inference model.5. Experiments\\n5.1. Experimental setup\\nWe use Microsoft COCO dataset to conduct experiments\\nand validate our object detection method. All our experi-\\nments did not use pre-trained models. That is, all models\\nwere trained from scratch. During the development pro-\\ncess, we used train 2017 set for training, and then used val\\n2017 set for veriﬁcation and choosing hyperparameters. Fi-\\nnally, we show the performance of object detection on the\\ntest 2017 set and compare it with the state-of-the-art object\\ndetection algorithms. Detailed training parameter settings\\nare described in Appendix.\\nWe designed basic model for edge GPU, normal GPU,\\nand cloud GPU, and they are respectively called YOLOv7-\\ntiny, YOLOv7, and YOLOv7-W6. At the same time, we\\nalso use basic model for model scaling for different ser-\\nvice requirements and get different types of models. For\\nYOLOv7, we do stack scaling on neck, and use the pro-\\nposed compound scaling method to perform scaling-up of\\nthe depth and width of the entire model, and use this to ob-\\ntain YOLOv7-X. As for YOLOv7-W6, we use the newly\\nproposed compound scaling method to obtain YOLOv7-E6\\nand YOLOv7-D6. In addition, we use the proposed E-\\nELAN for YOLOv7-E6, and thereby complete YOLOv7-\\nE6E. Since YOLOv7-tiny is an edge GPU-oriented archi-\\ntecture, it will use leaky ReLU as activation function. As\\nfor other models we use SiLU as activation function. We\\nwill describe the scaling factor of each model in detail in\\nAppendix.\\n6', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 5}),\n", " Document(page_content='Table 2: Comparison of state-of-the-art real-time object detectors.\\nModel #Param. FLOPs Size FPS APtest/APvalAPtest\\n50APtest\\n75APtest\\nSAPtest\\nMAPtest\\nL\\nYOLOX-S [21] 9.0M 26.8G 640 102 40.5% / 40.5% - - - - -\\nYOLOX-M [21] 25.3M 73.8G 640 81 47.2% / 46.9% - - - - -\\nYOLOX-L [21] 54.2M 155.6G 640 69 50.1% / 49.7% - - - - -\\nYOLOX-X [21] 99.1M 281.9G 640 58 51.5% / 51.1% - - - - -\\nPPYOLOE-S [85] 7.9M 17.4G 640 208 43.1% / 42.7% 60.5% 46.6% 23.2% 46.4% 56.9%\\nPPYOLOE-M [85] 23.4M 49.9G 640 123 48.9% / 48.6% 66.5% 53.0% 28.6% 52.9% 63.8%\\nPPYOLOE-L [85] 52.2M 110.1G 640 78 51.4% / 50.9% 68.9% 55.6% 31.4% 55.3% 66.1%\\nPPYOLOE-X [85] 98.4M 206.6G 640 45 52.2% / 51.9% 69.9% 56.5% 33.3% 56.3% 66.4%\\nYOLOv5-N (r6.1) [23] 1.9M 4.5G 640 159 - / 28.0% - - - - -\\nYOLOv5-S (r6.1) [23] 7.2M 16.5G 640 156 - / 37.4% - - - - -\\nYOLOv5-M (r6.1) [23] 21.2M 49.0G 640 122 - / 45.4% - - - - -\\nYOLOv5-L (r6.1) [23] 46.5M 109.1G 640 99 - / 49.0% - - - - -\\nYOLOv5-X (r6.1) [23] 86.7M 205.7G 640 83 - / 50.7% - - - - -\\nYOLOR-CSP [81] 52.9M 120.4G 640 106 51.1% / 50.8% 69.6% 55.7% 31.7% 55.3% 64.7%\\nYOLOR-CSP-X [81] 96.9M 226.8G 640 87 53.0% / 52.7% 71.4% 57.9% 33.7% 57.1% 66.8%\\nYOLOv7-tiny-SiLU 6.2M 13.8G 640 286 38.7% / 38.7% 56.7% 41.7% 18.8% 42.4% 51.9%\\nYOLOv7 36.9M 104.7G 640 161 51.4% / 51.2% 69.7% 55.9% 31.8% 55.5% 65.0%\\nYOLOv7-X 71.3M 189.9G 640 114 53.1% / 52.9% 71.2% 57.8% 33.8% 57.1% 67.4%\\nYOLOv5-N6 (r6.1) [23] 3.2M 18.4G 1280 123 - / 36.0% - - - - -\\nYOLOv5-S6 (r6.1) [23] 12.6M 67.2G 1280 122 - / 44.8% - - - - -\\nYOLOv5-M6 (r6.1) [23] 35.7M 200.0G 1280 90 - / 51.3% - - - - -\\nYOLOv5-L6 (r6.1) [23] 76.8M 445.6G 1280 63 - / 53.7% - - - - -\\nYOLOv5-X6 (r6.1) [23] 140.7M 839.2G 1280 38 - / 55.0% - - - - -\\nYOLOR-P6 [81] 37.2M 325.6G 1280 76 53.9% / 53.5% 71.4% 58.9% 36.1% 57.7% 65.6%\\nYOLOR-W6 [81] 79.8G 453.2G 1280 66 55.2% / 54.8% 72.7% 60.5% 37.7% 59.1% 67.1%\\nYOLOR-E6 [81] 115.8M 683.2G 1280 45 55.8% / 55.7% 73.4% 61.1% 38.4% 59.7% 67.7%\\nYOLOR-D6 [81] 151.7M 935.6G 1280 34 56.5% / 56.1% 74.1% 61.9% 38.9% 60.4% 68.7%\\nYOLOv7-W6 70.4M 360.0G 1280 84 54.9% / 54.6% 72.6% 60.1% 37.3% 58.7% 67.1%\\nYOLOv7-E6 97.2M 515.2G 1280 56 56.0% / 55.9% 73.5% 61.2% 38.0% 59.9% 68.4%\\nYOLOv7-D6 154.7M 806.8G 1280 44 56.6% / 56.3% 74.0% 61.8% 38.8% 60.1% 69.5%\\nYOLOv7-E6E 151.7M 843.2G 1280 36 56.8% / 56.8% 74.4% 62.1% 39.3% 60.5% 69.0%\\n1Our FLOPs is calaculated by rectangle input resolution like 640 ×640 or 1280 ×1280.\\n2Our inference time is estimated by using letterbox resize input image to make its long side equals to 640 or 1280.\\n5.2. Baselines\\nWe choose previous version of YOLO [3, 79] and state-\\nof-the-art object detector YOLOR [81] as our baselines. Ta-\\nble 1 shows the comparison of our proposed YOLOv7 mod-\\nels and those baseline that are trained with the same settings.\\nFrom the results we see that if compared with YOLOv4,\\nYOLOv7 has 75% less parameters, 36% less computation,\\nand brings 1.5% higher AP. If compared with state-of-the-\\nart YOLOR-CSP, YOLOv7 has 43% fewer parameters, 15%\\nless computation, and 0.4% higher AP. In the performance\\nof tiny model, compared with YOLOv4-tiny-31, YOLOv7-\\ntiny reduces the number of parameters by 39% and the\\namount of computation by 49%, but maintains the same AP.\\nOn the cloud GPU model, our model can still have a higher\\nAP while reducing the number of parameters by 19% and\\nthe amount of computation by 33%.5.3. Comparison with state-of-the-arts\\nWe compare the proposed method with state-of-the-art\\nobject detectors for general GPUs and Mobile GPUs, and\\nthe results are shown in Table 2. From the results in\\nTable 2 we know that the proposed method has the best\\nspeed-accuracy trade-off comprehensively. If we compare\\nYOLOv7-tiny-SiLU with YOLOv5-N (r6.1), our method\\nis 127 fps faster and 10.7% more accurate on AP. In ad-\\ndition, YOLOv7 has 51.4% AP at frame rate of 161 fps,\\nwhile PPYOLOE-L with the same AP has only 78 fps frame\\nrate. In terms of parameter usage, YOLOv7 is 41% less than\\nPPYOLOE-L. If we compare YOLOv7-X with 114 fps in-\\nference speed to YOLOv5-L (r6.1) with 99 fps inference\\nspeed, YOLOv7-X can improve AP by 3.9%. If YOLOv7-\\nX is compared with YOLOv5-X (r6.1) of similar scale, the\\ninference speed of YOLOv7-X is 31 fps faster. In addi-\\ntion, in terms of the amount of parameters and computation,\\nYOLOv7-X reduces 22% of parameters and 8% of compu-\\ntation compared to YOLOv5-X (r6.1), but improves AP by\\n2.2%.\\n7', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 6}),\n", " Document(page_content='If we compare YOLOv7 with YOLOR using the input\\nresolution 1280, the inference speed of YOLOv7-W6 is 8\\nfps faster than that of YOLOR-P6, and the detection rate is\\nalso increased by 1% AP. As for the comparison between\\nYOLOv7-E6 and YOLOv5-X6 (r6.1), the former has 0.9%\\nAP gain than the latter, 45% less parameters and 63% less\\ncomputation, and the inference speed is increased by 47%.\\nYOLOv7-D6 has close inference speed to YOLOR-E6, but\\nimproves AP by 0.8%. YOLOv7-E6E has close inference\\nspeed to YOLOR-D6, but improves AP by 0.3%.\\n5.4. Ablation study\\n5.4.1 Proposed compound scaling method\\nTable 3 shows the results obtained when using different\\nmodel scaling strategies for scaling up. Among them, our\\nproposed compound scaling method is to scale up the depth\\nof computational block by 1.5 times and the width of tran-\\nsition block by 1.25 times. If our method is compared with\\nthe method that only scaled up the width, our method can\\nimprove the AP by 0.5% with less parameters and amount\\nof computation. If our method is compared with the method\\nthat only scales up the depth, our method only needs to in-\\ncrease the number of parameters by 2.9% and the amount of\\ncomputation by 1.2%, which can improve the AP by 0.2%.\\nIt can be seen from the results of Table 3 that our proposed\\ncompound scaling strategy can utilize parameters and com-\\nputation more efﬁciently.\\nTable 3: Ablation study on proposed model scaling.\\nModel #Param. FLOPs Size APvalAPval\\n50APval\\n75\\nbase (v7-X light) 47.0M 125.5G 640 51.7% 70.1% 56.0%\\nwidth only (1.25 w)73.4M 195.5G 640 52.4% 70.9% 57.1%\\ndepth only (2.0 d) 69.3M 187.6G 640 52.7% 70.8% 57.3%\\ncompound (v7-X) 71.3M 189.9G 640 52.9% 71.1% 57.5%\\nimprovement - - - +1.2 +1.0 +1.5\\n5.4.2 Proposed planned re-parameterized model\\nIn order to verify the generality of our proposed planed\\nre-parameterized model, we use it on concatenation-based\\nmodel and residual-based model respectively for veriﬁca-\\ntion. The concatenation-based model and residual-based\\nmodel we chose for veriﬁcation are 3-stacked ELAN and\\nCSPDarknet, respectively.\\nIn the experiment of concatenation-based model, we re-\\nplace the 3×3convolutional layers in different positions in\\n3-stacked ELAN with RepConv, and the detailed conﬁgura-\\ntion is shown in Figure 6. From the results shown in Table 4\\nwe see that all higher AP values are present on our proposed\\nplanned re-parameterized model.\\nIn the experiment dealing with residual-based model,\\nsince the original dark block does not have a 3×3con-\\nFigure 6: Planned RepConv 3-stacked ELAN. Blue circles are the\\nposition we replace Conv by RepConv.\\nTable 4: Ablation study on planned RepConcatenation model.\\nModel APvalAPval\\n50APval\\n75APval\\nSAPval\\nMAPval\\nL\\nbase (3-S ELAN) 52.26% 70.41% 56.77% 35.81% 57.00% 67.59%\\nFigure 6 (a) 52.18% 70.34% 56.90% 35.71% 56.83% 67.51%\\nFigure 6 (b) 52.30% 70.30% 56.92% 35.76% 56.95% 67.74%\\nFigure 6 (c) 52.33% 70.56% 56.91% 35.90% 57.06% 67.50%\\nFigure 6 (d) 52.17% 70.32% 56.82% 35.33% 57.06% 68.09%\\nFigure 6 (e) 52.23% 70.20% 56.81% 35.34% 56.97% 66.88%\\nvolution block that conforms to our design strategy, we ad-\\nditionally design a reversed dark block for the experiment,\\nwhose architecture is shown in Figure 7. Since the CSP-\\nDarknet with dark block and reversed dark block has exactly\\nthe same amount of parameters and operations, it is fair to\\ncompare. The experiment results illustrated in Table 5 fully\\nconﬁrm that the proposed planned re-parameterized model\\nis equally effective on residual-based model. We ﬁnd that\\nthe design of RepCSPResNet [85] also ﬁt our design pat-\\ntern.\\nFigure 7: Reversed CSPDarknet. We reverse the position of 1×1\\nand3×3convolutional layer in dark block to ﬁt our planned re-\\nparameterized model design strategy.\\nTable 5: Ablation study on planned RepResidual model.\\nModel APvalAPval\\n50APval\\n75APval\\nSAPval\\nMAPval\\nL\\nbase (YOLOR-W6) 54.82% 72.39% 59.95% 39.68% 59.38% 68.30%\\nRepCSP 54.67% 72.50% 59.58% 40.22% 59.61% 67.87%\\nRCSP 54.36% 71.95% 59.54% 40.15% 59.02% 67.44%\\nRepRCSP 54.85% 72.51% 60.08% 40.53% 59.52% 68.06%\\nbase (YOLOR-CSP) 50.81% 69.47% 55.28% 33.74% 56.01% 65.38%\\nRepRCSP 50.91% 69.54% 55.55% 34.44% 55.74% 65.46%\\n8', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 7}),\n", " Document(page_content='Figure 8: Objectness map predicted by different methods at auxiliary head and lead head.\\n5.4.3 Proposed assistant loss for auxiliary head\\nIn the assistant loss for auxiliary head experiments, we com-\\npare the general independent label assignment for lead head\\nand auxiliary head methods, and we also compare the two\\nproposed lead guided label assignment methods. We show\\nall comparison results in Table 6. From the results listed in\\nTable 6, it is clear that any model that increases assistant\\nloss can signiﬁcantly improve the overall performance. In\\naddition, our proposed lead guided label assignment strat-\\negy receives better performance than the general indepen-\\ndent label assignment strategy in AP, AP 50, and AP 75. As\\nfor our proposed coarse for assistant and ﬁne for lead label\\nassignment strategy, it results in best results in all cases. In\\nFigure 8 we show the objectness map predicted by different\\nmethods at auxiliary head and lead head. From Figure 8 we\\nﬁnd that if auxiliary head learns lead guided soft label, it\\nwill indeed help lead head to extract the residual informa-\\ntion from the consistant targets.\\nTable 6: Ablation study on proposed auxiliary head.\\nModel Size APvalAPval\\n50APval\\n75\\nbase (v7-E6) 1280 55.6% 73.2% 60.7%\\nindependent 1280 55.8% 73.4% 60.9%\\nlead guided 1280 55.9% 73.5% 61.0%\\ncoarse-to-ﬁne lead guided 1280 55.9% 73.5% 61.1%\\nimprovement - +0.3 +0.3 +0.4\\nIn Table 7 we further analyze the effect of the proposed\\ncoarse-to-ﬁne lead guided label assignment method on the\\ndecoder of auxiliary head. That is, we compared the results\\nof with/without the introduction of upper bound constraint.\\nJudging from the numbers in the Table, the method of con-\\nstraining the upper bound of objectness by the distance from\\nthe center of the object can achieve better performance.\\nTable 7: Ablation study on constrained auxiliary head.\\nModel Size APvalAPval\\n50APval\\n75\\nbase (v7-E6) 1280 55.6% 73.2% 60.7%\\naux without constraint 1280 55.9% 73.5% 61.0%\\naux with constraint 1280 55.9% 73.5% 61.1%\\nimprovement - +0.3 +0.3 +0.4Since the proposed YOLOv7 uses multiple pyramids to\\njointly predict object detection results, we can directly con-\\nnect auxiliary head to the pyramid in the middle layer for\\ntraining. This type of training can make up for informa-\\ntion that may be lost in the next level pyramid prediction.\\nFor the above reasons, we designed partial auxiliary head\\nin the proposed E-ELAN architecture. Our approach is to\\nconnect auxiliary head after one of the sets of feature map\\nbefore merging cardinality, and this connection can make\\nthe weight of the newly generated set of feature map not\\ndirectly updated by assistant loss. Our design allows each\\npyramid of lead head to still get information from objects\\nwith different sizes. Table 8 shows the results obtained us-\\ning two different methods, i.e., coarse-to-ﬁne lead guided\\nand partial coarse-to-ﬁne lead guided methods. Obviously,\\nthe partial coarse-to-ﬁne lead guided method has a better\\nauxiliary effect.\\nTable 8: Ablation study on partial auxiliary head.\\nModel Size APvalAPval\\n50APval\\n75\\nbase (v7-E6E) 1280 56.3% 74.0% 61.5%\\naux 1280 56.5% 74.0% 61.6%\\npartial aux 1280 56.8% 74.4% 62.1%\\nimprovement - +0.5 +0.4 +0.6\\n6. Conclusions\\nIn this paper we propose a new architecture of real-\\ntime object detector and the corresponding model scaling\\nmethod. Furthermore, we ﬁnd that the evolving process\\nof object detection methods generates new research top-\\nics. During the research process, we found the replace-\\nment problem of re-parameterized module and the alloca-\\ntion problem of dynamic label assignment. To solve the\\nproblem, we propose the trainable bag-of-freebies method\\nto enhance the accuracy of object detection. Based on the\\nabove, we have developed the YOLOv7 series of object de-\\ntection systems, which receives the state-of-the-art results.\\n7. Acknowledgements\\nThe authors wish to thank National Center for High-\\nperformance Computing (NCHC) for providing computa-\\ntional and storage resources.\\n9', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 8}),\n", " Document(page_content='Table 9: More comparison (batch=1, no-TRT, without extra object detection training data)\\nModel #Param. FLOPs Size FPSV100APtest/APvalAPtest\\n50APtest\\n75\\nYOLOv7-tiny-SiLU 6.2M 13.8G 640 286 38.7% /38.7% 56.7% 41.7%\\nPPYOLOE-S [85] 7.9M 17.4G 640 208 43.1% /42.7% 60.5% 46.6%\\nYOLOv7 36.9M 104.7G 640 161 51.4% /51.2% 69.7% 55.9%\\nYOLOv5-N (r6.1) [23] 1.9M 4.5G 640 159 - / 28.0% - -\\nYOLOv5-S (r6.1) [23] 7.2M 16.5G 640 156 - / 37.4% - -\\nPPYOLOE-M [85] 23.4M 49.9G 640 123 48.9% / 48.6% 66.5% 53.0%\\nYOLOv5-N6 (r6.1) [23] 3.2M 18.4G 1280 123 - / 36.0% - -\\nYOLOv5-S6 (r6.1) [23] 12.6M 67.2G 1280 122 - / 44.8% - -\\nYOLOv5-M (r6.1) [23] 21.2M 49.0G 640 122 - / 45.4% - -\\nYOLOv7-X 71.3M 189.9G 640 114 53.1% /52.9% 71.2% 57.8%\\nYOLOR-CSP [81] 52.9M 120.4G 640 106 51.1% / 50.8% 69.6% 55.7%\\nYOLOX-S [21] 9.0M 26.8G 640 102 40.5% / 40.5% - -\\nYOLOv5-L (r6.1) [23] 46.5M 109.1G 640 99 - / 49.0% - -\\nYOLOv5-M6 (r6.1) [23] 35.7M 200.0G 1280 90 - / 51.3% - -\\nYOLOR-CSP-X [81] 96.9M 226.8G 640 87 53.0% / 52.7% 71.4% 57.9%\\nYOLOv7-W6 70.4M 360.0G 1280 84 54.9% /54.6% 72.6% 60.1%\\nYOLOv5-X (r6.1) [23] 86.7M 205.7G 640 83 - / 50.7% - -\\nYOLOX-M [21] 25.3M 73.8G 640 81 47.2% / 46.9% - -\\nPPYOLOE-L [85] 52.2M 110.1G 640 78 51.4% / 50.9% 68.9% 55.6%\\nYOLOR-P6 [81] 37.2M 325.6G 1280 76 53.9% / 53.5% 71.4% 58.9%\\nYOLOX-L [21] 54.2M 155.6G 640 69 50.1% / 49.7% - -\\nYOLOR-W6 [81] 79.8G 453.2G 1280 66 55.2% /54.8% 72.7% 60.5%\\nYOLOv5-L6 (r6.1) [23] 76.8M 445.6G 1280 63 - / 53.7% - -\\nYOLOX-X [21] 99.1M 281.9G 640 58 51.5% / 51.1% - -\\nYOLOv7-E6 97.2M 515.2G 1280 56 56.0% /55.9% 73.5% 61.2%\\nYOLOR-E6 [81] 115.8M 683.2G 1280 45 55.8% / 55.7% 73.4% 61.1%\\nPPYOLOE-X [85] 98.4M 206.6G 640 45 52.2% / 51.9% 69.9% 56.5%\\nYOLOv7-D6 154.7M 806.8G 1280 44 56.6% /56.3% 74.0% 61.8%\\nYOLOv5-X6 (r6.1) [23] 140.7M 839.2G 1280 38 - / 55.0% - -\\nYOLOv7-E6E 151.7M 843.2G 1280 36 56.8% /56.8% 74.4% 62.1%\\nYOLOR-D6 [81] 151.7M 935.6G 1280 34 56.5% / 56.1% 74.1% 61.9%\\nF-RCNN-R101-FPN+ [5] 60.0M 246.0G 1333 20 - / 44.0% - -\\nDeformable DETR [100] 40.0M 173.0G - 19 - / 46.2% - -\\nSwin-B (C-M-RCNN) [52] 145.0M 982.0G 1333 11.6 - / 51.9% - -\\nDETR DC5-R101 [5] 60.0M 253.0G 1333 10 - / 44.9% - -\\nEfﬁcientDet-D7x [74] 77.0M 410.0G 1536 6.5 55.1% / 54.4% 72.4% 58.4%\\nDual-Swin-T (C-M-RCNN) [47] 113.8M 836.0G 1333 6.5 - / 53.6% - -\\nViT-Adapter-B [7] 122.0M 997.0G - 4.4 - / 50.8% - -\\nDual-Swin-B (HTC) [47] 235.0M - 1600 2.5 58.7% /58.4% - -\\nDual-Swin-L (HTC) [47] 453.0M - 1600 1.5 59.4% /59.1% - -\\nModel #Param. FLOPs Size FPSA100APtest/APvalAPtest\\n50APtest\\n75\\nDN-Deformable-DETR [41] 48.0M 265.0G 1333 23.0 - / 48.6% - -\\nConvNeXt-B (C-M-RCNN) [53] - 964.0G 1280 11.5 - / 54.0% 73.1% 58.8%\\nSwin-B (C-M-RCNN) [52] - 982.0G 1280 10.7 - / 53.0% 71.8% 57.5%\\nDINO-5scale (R50) [89] 47.0M 860.0G 1333 10.0 - / 51.0% - -\\nConvNeXt-L (C-M-RCNN) [53] - 1354.0G 1280 10.0 - / 54.8% 73.8% 59.8%\\nSwin-L (C-M-RCNN) [52] - 1382.0G 1280 9.2 - / 53.9% 72.4% 58.8%\\nConvNeXt-XL (C-M-RCNN) [53] - 1898.0G 1280 8.6 - / 55.2% 74.2% 59.9%\\n8. More comparison\\nYOLOv7 surpasses all known object detectors in both\\nspeed and accuracy in the range from 5 FPS to 160 FPS and\\nhas the highest accuracy 56.8% AP test-dev / 56.8% AP\\nmin-val among all known real-time object detectors with 30\\nFPS or higher on GPU V100. YOLOv7-E6 object detector\\n(56 FPS V100, 55.9% AP) outperforms both transformer-\\nbased detector SWIN-L Cascade-Mask R-CNN (9.2 FPS\\nA100, 53.9% AP) by 509% in speed and 2% in accuracy,and convolutional-based detector ConvNeXt-XL Cascade-\\nMask R-CNN (8.6 FPS A100, 55.2% AP) by 551% in speed\\nand 0.7% AP in accuracy, as well as YOLOv7 outperforms:\\nYOLOR, YOLOX, Scaled-YOLOv4, YOLOv5, DETR, De-\\nformable DETR, DINO-5scale-R50, ViT-Adapter-B and\\nmany other object detectors in speed and accuracy. More\\nover, we train YOLOv7 only on MS COCO dataset from\\nscratch without using any other datasets or pre-trained\\nweights.\\n10', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 9}),\n", " Document(page_content='Figure 9: Comparison with other object detectors.\\nFigure 10: Comparison with other real-time object detectors.\\nTable 10: Comparison of different setting.\\nModel Presicion IoU threshold APval\\nYOLOv7-X FP16 (default) 0.65 (default) 52.9%\\nYOLOv7-X FP32 0.65 53.0%\\nYOLOv7-X FP16 0.70 53.0%\\nYOLOv7-X FP32 0.70 53.1%\\nimprovement - - +0.2%\\n*Similar to meituan/YOLOv6 and PPYOLOE, our model could\\nget higher AP when set higher IoU threshold.\\nThe maximum accuracy of the YOLOv7-E6E (56.8%\\nAP) real-time model is +13.7% AP higher than the cur-\\nrent most accurate meituan/YOLOv6-s model (43.1% AP)\\non COCO dataset. Our YOLOv7-tiny (35.2% AP, 0.4\\nms) model is +25% faster and +0.2% AP higher than\\nmeituan/YOLOv6-n (35.0% AP, 0.5 ms) under identical\\nconditions on COCO dataset and V100 GPU with batch=32.\\nFigure 11: Comparison with other real-time object detectors.\\n11', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 10}),\n", " Document(page_content='References\\n[1] anonymous. Designing network design strategies. anony-\\nmous submission , 2022. 3\\n[2] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>\\<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,\\nand Barret Zoph. Revisiting ResNets: Improved training\\nand scaling strategies. Advances in Neural Information Pro-\\ncessing Systems (NeurIPS) , 34, 2021. 2\\n[3] <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>\\n<PERSON><PERSON>. YOLOv4: Optimal speed and accuracy of\\nobject detection. arXiv preprint arXiv:2004.10934 , 2020.\\n2, 6, 7\\n[4] <PERSON><PERSON>, <PERSON>, <PERSON>,\\nand <PERSON><PERSON><PERSON>. Ensemble deep learning in bioinformat-\\nics.Nature Machine Intelligence , 2(9):500–508, 2020. 2\\n[5] <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>.\\nEnd-to-end object detection with transformers. In Pro-\\nceedings of the European Conference on Computer Vision\\n(ECCV) , pages 213–229, 2020. 10\\n[6] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and\\nJunni Zou. AP-loss for accurate one-stage object detection.\\nIEEE Transactions on Pattern Analysis and Machine Intel-\\nligence (TPAMI) , 43(11):3782–3798, 2020. 2\\n[7] Zhe Chen, Yuchen Duan, Wenhai Wang, Junjun He, Tong\\nLu, Jifeng Dai, and Yu Qiao. Vision transformer adapter for\\ndense predictions. arXiv preprint arXiv:2205.08534 , 2022.\\n10\\n[8] Jiwoong Choi, Dayoung Chun, Hyun Kim, and Hyuk-Jae\\nLee. Gaussian YOLOv3: An accurate and fast object detec-\\ntor using localization uncertainty for autonomous driving.\\nInProceedings of the IEEE/CVF International Conference\\non Computer Vision (ICCV) , pages 502–511, 2019. 5\\n[9] Xiyang Dai, Yinpeng Chen, Bin Xiao, Dongdong Chen,\\nMengchen Liu, Lu Yuan, and Lei Zhang. Dynamic head:\\nUnifying object detection heads with attentions. In Pro-\\nceedings of the IEEE/CVF Conference on Computer Vision\\nand Pattern Recognition (CVPR) , pages 7373–7382, 2021.\\n2\\n[10] Xiaohan Ding, Honghao Chen, Xiangyu Zhang, Kaiqi\\nHuang, Jungong Han, and Guiguang Ding. Re-\\nparameterizing your optimizers rather than architectures.\\narXiv preprint arXiv:2205.15242 , 2022. 2\\n[11] Xiaohan Ding, Yuchen Guo, Guiguang Ding, and Jungong\\nHan. ACNet: Strengthening the kernel skeletons for pow-\\nerful CNN via asymmetric convolution blocks. In Proceed-\\nings of the IEEE/CVF International Conference on Com-\\nputer Vision (ICCV) , pages 1911–1920, 2019. 2\\n[12] Xiaohan Ding, Xiangyu Zhang, Jungong Han, and\\nGuiguang Ding. Diverse branch block: Building a con-\\nvolution as an inception-like unit. In Proceedings of the\\nIEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , pages 10886–10895, 2021. 2\\n[13] Xiaohan Ding, Xiangyu Zhang, Ningning Ma, Jungong\\nHan, Guiguang Ding, and Jian Sun. RepVGG: Making\\nVGG-style convnets great again. In Proceedings of theIEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , pages 13733–13742, 2021. 2, 4\\n[14] Xiaohan Ding, Xiangyu Zhang, Yizhuang Zhou, Jungong\\nHan, Guiguang Ding, and Jian Sun. Scaling up your ker-\\nnels to 31x31: Revisiting large kernel design in CNNs. In\\nProceedings of the IEEE/CVF Conference on Computer Vi-\\nsion and Pattern Recognition (CVPR) , 2022. 2\\n[15] Piotr Doll ´ar, Mannat Singh, and Ross Girshick. Fast and\\naccurate model scaling. In Proceedings of the IEEE/CVF\\nConference on Computer Vision and Pattern Recognition\\n(CVPR) , pages 924–932, 2021. 2, 3\\n[16] Xianzhi Du, Barret Zoph, Wei-Chih Hung, and Tsung-Yi\\nLin. Simple training strategies and model scaling for object\\ndetection. arXiv preprint arXiv:2107.00057 , 2021. 2\\n[17] Chengjian Feng, Yujie Zhong, Yu Gao, Matthew R Scott,\\nand Weilin Huang. TOOD: Task-aligned one-stage object\\ndetection. In Proceedings of the IEEE/CVF International\\nConference on Computer Vision (ICCV) , pages 3490–3499,\\n2021. 2, 5\\n[18] Di Feng, Christian Haase-Sch ¨utz, Lars Rosenbaum, Heinz\\nHertlein, Claudius Glaeser, Fabian Timm, Werner Wies-\\nbeck, and Klaus Dietmayer. Deep multi-modal object de-\\ntection and semantic segmentation for autonomous driv-\\ning: Datasets, methods, and challenges. IEEE Transac-\\ntions on Intelligent Transportation Systems , 22(3):1341–\\n1360, 2020. 1\\n[19] Timur Garipov, Pavel Izmailov, Dmitrii Podoprikhin,\\nDmitry P Vetrov, and Andrew G Wilson. Loss sur-\\nfaces, mode connectivity, and fast ensembling of DNNs.\\nAdvances in Neural Information Processing Systems\\n(NeurIPS) , 31, 2018. 2\\n[20] Zheng Ge, Songtao Liu, Zeming Li, Osamu Yoshie, and\\nJian Sun. OTA: Optimal transport assignment for object\\ndetection. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n303–312, 2021. 2, 5\\n[21] Zheng Ge, Songtao Liu, Feng Wang, Zeming Li, and Jian\\nSun. YOLOX: Exceeding YOLO series in 2021. arXiv\\npreprint arXiv:2107.08430 , 2021. 1, 2, 7, 10\\n[22] Golnaz Ghiasi, Tsung-Yi Lin, and Quoc V Le. NAS-FPN:\\nLearning scalable feature pyramid architecture for object\\ndetection. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n7036–7045, 2019. 2\\n[23] Jocher Glenn. YOLOv5 release v6.1. https://github.com/\\nultralytics/yolov5/releases/tag/v6.1, 2022. 2, 7, 10\\n[24] Shuxuan Guo, Jose M Alvarez, and Mathieu Salzmann. Ex-\\npandNets: Linear over-parameterization to train compact\\nconvolutional networks. Advances in Neural Information\\nProcessing Systems (NeurIPS) , 33:1298–1310, 2020. 2\\n[25] Kai Han, Yunhe Wang, Qi Tian, Jianyuan Guo, Chunjing\\nXu, and Chang Xu. GhostNet: More features from cheap\\noperations. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n1580–1589, 2020. 1\\n[26] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun.\\nDeep residual learning for image recognition. In Proceed-\\n12', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 11}),\n", " Document(page_content='ings of the IEEE/CVF Conference on Computer Vision and\\nPattern Recognition (CVPR) , pages 770–778, 2016. 1, 4, 5\\n[27] <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>\\<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, et al. Searching for Mo-\\nbileNetV3. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n1314–1324, 2019. 1\\n[28] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>\\<PERSON><PERSON><PERSON>, and <PERSON><PERSON>. MobileNets: Efﬁcient con-\\nvolutional neural networks for mobile vision applications.\\narXiv preprint arXiv:1704.04861 , 2017. 1\\n[29] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. On-\\nline convolutional re-parameterization. In Proceedings of\\nthe IEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , 2022. 2\\n[30] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. A2-FPN:\\nAttention aggregation based feature pyramid network for\\ninstance segmentation. In Proceedings of the IEEE/CVF\\nConference on Computer Vision and Pattern Recognition\\n(CVPR) , pages 15343–15352, 2021. 2\\n[31] Gao Huang, Yixuan Li, Geoff Pleiss, Zhuang Liu, John E\\nHopcroft, and Kilian Q Weinberger. Snapshot ensembles:\\nTrain 1, get m for free. International Conference on Learn-\\ning Representations (ICLR) , 2017. 2\\n[32] Gao Huang, Zhuang Liu, Laurens Van Der Maaten, and Kil-\\nian Q Weinberger. Densely connected convolutional net-\\nworks. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n4700–4708, 2017. 2, 4, 5\\n[33] Pavel Izmailov, Dmitrii Podoprikhin, Timur Garipov,\\nDmitry Vetrov, and Andrew Gordon Wilson. Averaging\\nweights leads to wider optima and better generalization. In\\nConference on Uncertainty in Artiﬁcial Intelligence (UAI) ,\\n2018. 2\\n[34] Paul F Jaeger, Simon AA Kohl, Sebastian Bickel-\\nhaupt, Fabian Isensee, Tristan Anselm Kuder, Heinz-Peter\\nSchlemmer, and Klaus H Maier-Hein. Retina U-Net: Em-\\nbarrassingly simple exploitation of segmentation supervi-\\nsion for medical object detection. In Machine Learning for\\nHealth Workshop , pages 171–183, 2020. 1\\n[35] Hakan Karaoguz and Patric Jensfelt. Object detection ap-\\nproach for robot grasp detection. In IEEE International\\nConference on Robotics and Automation (ICRA) , pages\\n4953–4959, 2019. 1\\n[36] Kang Kim and Hee Seok Lee. Probabilistic anchor as-\\nsignment with iou prediction for object detection. In Pro-\\nceedings of the European conference on computer vision\\n(ECCV) , pages 355–371, 2020. 5\\n[37] Alexander Kirillov, Ross Girshick, Kaiming He, and Piotr\\nDoll´ar. Panoptic feature pyramid networks. In Proceed-\\nings of the IEEE/CVF Conference on Computer Vision and\\nPattern Recognition (CVPR) , pages 6399–6408, 2019. 2\\n[38] Chen-Yu Lee, Saining Xie, Patrick Gallagher, Zhengyou\\nZhang, and Zhuowen Tu. Deeply-supervised nets. In Arti-\\nﬁcial Intelligence and Statistics , pages 562–570, 2015. 5[39] Youngwan Lee, Joong-won Hwang, Sangrok Lee, Yuseok\\nBae, and Jongyoul Park. An energy and GPU-computation\\nefﬁcient backbone network for real-time object detection.\\nInProceedings of the IEEE/CVF Conference on Com-\\nputer Vision and Pattern Recognition Workshops (CVPRW) ,\\npages 0–0, 2019. 2, 3\\n[40] Buyu Li, Wanli Ouyang, Lu Sheng, Xingyu Zeng, and\\nXiaogang Wang. GS3D: An efﬁcient 3d object detection\\nframework for autonomous driving. In Proceedings of the\\nIEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , pages 1019–1028, 2019. 1\\n[41] Feng Li, Hao Zhang, Shilong Liu, Jian Guo, Lionel M\\nNi, and Lei Zhang. DN-DETR: Accelerate detr training\\nby introducing query denoising. In Proceedings of the\\nIEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , pages 13619–13627, 2022. 10\\n[42] Shuai Li, Chenhang He, Ruihuang Li, and Lei Zhang. A\\ndual weighting label assignment scheme for object detec-\\ntion. In Proceedings of the IEEE/CVF Conference on Com-\\nputer Vision and Pattern Recognition (CVPR) , pages 9387–\\n9396, 2022. 2, 5\\n[43] Xiang Li, Wenhai Wang, Xiaolin Hu, Jun Li, Jinhui Tang,\\nand Jian Yang. Generalized focal loss v2: Learning reliable\\nlocalization quality estimation for dense object detection. In\\nProceedings of the IEEE/CVF Conference on Computer Vi-\\nsion and Pattern Recognition (CVPR) , pages 11632–11641,\\n2021. 5\\n[44] Xiang Li, Wenhai Wang, Lijun Wu, Shuo Chen, Xiaolin\\nHu, Jun Li, Jinhui Tang, and Jian Yang. Generalized focal\\nloss: Learning qualiﬁed and distributed bounding boxes for\\ndense object detection. Advances in Neural Information\\nProcessing Systems (NeurIPS) , 33:21002–21012, 2020. 5\\n[45] Yanghao Li, Hanzi Mao, Ross Girshick, and Kaiming He.\\nExploring plain vision transformer backbones for object de-\\ntection. arXiv preprint arXiv:2203.16527 , 2022. 2\\n[46] Zhuoling Li, Minghui Dong, Shiping Wen, Xiang Hu, Pan\\nZhou, and Zhigang Zeng. CLU-CNNs: Object detection for\\nmedical images. Neurocomputing , 350:53–59, 2019. 1\\n[47] Tingting Liang, Xiaojie Chu, Yudong Liu, Yongtao Wang,\\nZhi Tang, Wei Chu, Jingdong Chen, and Haibin Ling. CB-\\nNetV2: A composite backbone network architecture for ob-\\nject detection. arXiv preprint arXiv:2107.00420 , 2021. 5,\\n10\\n[48] Ji Lin, Wei-Ming Chen, Han Cai, Chuang Gan, and Song\\nHan. Memory-efﬁcient patch-based inference for tiny deep\\nlearning. Advances in Neural Information Processing Sys-\\ntems (NeurIPS) , 34:2346–2358, 2021. 1\\n[49] Ji Lin, Wei-Ming Chen, Yujun Lin, Chuang Gan, Song\\nHan, et al. MCUNet: Tiny deep learning on IoT de-\\nvices. Advances in Neural Information Processing Systems\\n(NeurIPS) , 33:11711–11722, 2020. 1\\n[50] Yuxuan Liu, Lujia Wang, and Ming Liu. YOLOStereo3D:\\nA step back to 2D for efﬁcient stereo 3D detection. In\\nIEEE International Conference on Robotics and Automa-\\ntion (ICRA) , pages 13018–13024, 2021. 5\\n[51] Ze Liu, Han Hu, Yutong Lin, Zhuliang Yao, Zhenda Xie,\\nYixuan Wei, Jia Ning, Yue Cao, Zheng Zhang, Li Dong,\\n13', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 12}),\n", " Document(page_content='et al. Swin transformer v2: Scaling up capacity and res-\\nolution. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , 2022. 2\\n[52] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Swin transformer:\\nHierarchical vision transformer using shifted windows. In\\nProceedings of the IEEE/CVF International Conference on\\nComputer Vision (ICCV) , pages 10012–10022, 2021. 10\\n[53] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>-<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. A ConvNet for\\nthe 2020s. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n11976–11986, 2022. 10\\n[54] Rangi Lyu. NanoDet-Plus. https://github.com/RangiLyu/\\nnanodet/releases/tag/v1.0.0-alpha-1, 2021. 1, 2\\n[55] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\\nSun. ShufﬂeNet V2: Practical guidelines for efﬁcient CNN\\narchitecture design. In Proceedings of the European Con-\\nference on Computer Vision (ECCV) , pages 116–131, 2018.\\n1, 3\\n[56] Kemal Oksuz, Baris Can Cam, Emre Akbas, and Sinan\\nKalkan. A ranking-based, balanced loss function unifying\\nclassiﬁcation and localisation in object detection. Advances\\nin Neural Information Processing Systems (NeurIPS) ,\\n33:15534–15545, 2020. 2\\n[57] Kemal Oksuz, Baris Can Cam, Emre Akbas, and Sinan\\nKalkan. Rank & sort loss for object detection and in-\\nstance segmentation. In Proceedings of the IEEE/CVF In-\\nternational Conference on Computer Vision (ICCV) , pages\\n3009–3018, 2021. 2\\n[58] Shuvo Kumar Paul, Muhammed Tawﬁq Chowdhury,\\nMircea Nicolescu, Monica Nicolescu, and David Feil-\\nSeifer. Object detection and pose estimation from rgb and\\ndepth data for real-time, adaptive robotic grasping. In Ad-\\nvances in Computer Vision and Computational Biology ,\\npages 121–142. 2021. 1\\n[59] Siyuan Qiao, Liang-Chieh Chen, and Alan Yuille. De-\\ntectoRS: Detecting objects with recursive feature pyramid\\nand switchable atrous convolution. In Proceedings of the\\nIEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , pages 10213–10224, 2021. 2\\n[60] Ilija Radosavovic, Raj Prateek Kosaraju, Ross Girshick,\\nKaiming He, and Piotr Doll ´ar. Designing network design\\nspaces. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n10428–10436, 2020. 2\\n[61] Joseph Redmon, Santosh Divvala, Ross Girshick, and Ali\\nFarhadi. You only look once: Uniﬁed, real-time object de-\\ntection. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n779–788, 2016. 2, 5\\n[62] Joseph Redmon and Ali Farhadi. YOLO9000: better, faster,\\nstronger. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n7263–7271, 2017. 2\\n[63] Joseph Redmon and Ali Farhadi. YOLOv3: An incremental\\nimprovement. arXiv preprint arXiv:1804.02767 , 2018. 1, 2[64] Hamid Rezatoﬁghi, Nathan Tsoi, JunYoung Gwak, Amir\\nSadeghian, Ian Reid, and Silvio Savarese. Generalized in-\\ntersection over union: A metric and a loss for bounding\\nbox regression. In Proceedings of the IEEE/CVF Confer-\\nence on Computer Vision and Pattern Recognition (CVPR) ,\\npages 658–666, 2019. 2\\n[65] Byungseok Roh, JaeWoong Shin, Wuhyun Shin, and\\nSaehoon Kim. Sparse DETR: Efﬁcient end-to-end ob-\\nject detection with learnable sparsity. arXiv preprint\\narXiv:2111.14330 , 2021. 5\\n[66] Mark Sandler, Andrew Howard, Menglong Zhu, Andrey\\nZhmoginov, and Liang-Chieh Chen. MobileNetV2: In-\\nverted residuals and linear bottlenecks. In Proceedings of\\nthe IEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , pages 4510–4520, 2018. 1\\n[67] Zhiqiang Shen, Zhuang Liu, Jianguo Li, Yu-Gang Jiang,\\nYurong Chen, and Xiangyang Xue. Object detection\\nfrom scratch with deep supervision. IEEE Transactions\\non Pattern Analysis and Machine Intelligence (TPAMI) ,\\n42(2):398–412, 2019. 5\\n[68] Karen Simonyan and Andrew Zisserman. Very deep convo-\\nlutional networks for large-scale image recognition. arXiv\\npreprint arXiv:1409.1556 , 2014. 4\\n[69] Peize Sun, Rufeng Zhang, Yi Jiang, Tao Kong, Chenfeng\\nXu, Wei Zhan, Masayoshi Tomizuka, Lei Li, Zehuan Yuan,\\nChanghu Wang, et al. Sparse R-CNN: End-to-end ob-\\nject detection with learnable proposals. In Proceedings of\\nthe IEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , pages 14454–14463, 2021. 2\\n[70] Christian Szegedy, Wei Liu, Yangqing Jia, Pierre Sermanet,\\nScott Reed, Dragomir Anguelov, Dumitru Erhan, Vincent\\nVanhoucke, and Andrew Rabinovich. Going deeper with\\nconvolutions. In Proceedings of the IEEE/CVF Confer-\\nence on Computer Vision and Pattern Recognition (CVPR) ,\\npages 1–9, 2015. 5\\n[71] Christian Szegedy, Vincent Vanhoucke, Sergey Ioffe, Jon\\nShlens, and Zbigniew Wojna. Rethinking the inception\\narchitecture for computer vision. In Proceedings of the\\nIEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , pages 2818–2826, 2016. 2\\n[72] Mingxing Tan and Quoc Le. EfﬁcientNet: Rethinking\\nmodel scaling for convolutional neural networks. In Inter-\\nnational Conference on Machine Learning (ICML) , pages\\n6105–6114, 2019. 2, 3\\n[73] Mingxing Tan and Quoc Le. EfﬁcientNetv2: Smaller mod-\\nels and faster training. In International Conference on Ma-\\nchine Learning (ICML) , pages 10096–10106, 2021. 2\\n[74] Mingxing Tan, Ruoming Pang, and Quoc V Le. Efﬁcient-\\nDet: Scalable and efﬁcient object detection. In Proceedings\\nof the IEEE/CVF Conference on Computer Vision and Pat-\\ntern Recognition (CVPR) , pages 10781–10790, 2020. 2, 10\\n[75] Antti Tarvainen and Harri Valpola. Mean teachers are better\\nrole models: Weight-averaged consistency targets improve\\nsemi-supervised deep learning results. Advances in Neural\\nInformation Processing Systems (NeurIPS) , 30, 2017. 2, 6\\n[76] Zhi Tian, Chunhua Shen, Hao Chen, and Tong He. FCOS:\\nFully convolutional one-stage object detection. In Proceed-\\n14', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 13}),\n", " Document(page_content='ings of the IEEE/CVF International Conference on Com-\\nputer Vision (ICCV) , pages 9627–9636, 2019. 2\\n[77] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. FCOS:\\nA simple and strong anchor-free object detector. IEEE\\nTransactions on Pattern Analysis and Machine Intelligence\\n(TPAMI) , 44(4):1922–1933, 2022. 2\\n[78] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. An im-\\nproved one millisecond mobile backbone. arXiv preprint\\narXiv:2206.04040 , 2022. 2\\n[79] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>-\\n<PERSON><PERSON>. Scaled-YOLOv4: Scaling cross stage\\npartial network. In Proceedings of the IEEE/CVF Confer-\\nence on Computer Vision and Pattern Recognition (CVPR) ,\\npages 13029–13038, 2021. 2, 3, 6, 7\\n[80] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,\\nP<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. CSP-\\nNet: A new backbone that can enhance learning capabil-\\nity of CNN. In Proceedings of the IEEE/CVF Conference\\non Computer Vision and Pattern Recognition Workshops\\n(CVPRW) , pages 390–391, 2020. 1\\n[81] Chien-Yao Wang, I-Hau Yeh, and Hong-Yuan Mark Liao.\\nYou only learn one representation: Uniﬁed network for\\nmultiple tasks. arXiv preprint arXiv:2105.04206 , 2021. 1,\\n2, 6, 7, 10\\n[82] Jianfeng Wang, Lin Song, Zeming Li, Hongbin Sun, Jian\\nSun, and Nanning Zheng. End-to-end object detection\\nwith fully convolutional network. In Proceedings of the\\nIEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , pages 15849–15858, 2021. 2, 5\\n[83] Bichen Wu, Chaojian Li, Hang Zhang, Xiaoliang Dai,\\nPeizhao Zhang, Matthew Yu, Jialiang Wang, Yingyan Lin,\\nand Peter Vajda. FBNetv5: Neural architecture search for\\nmultiple tasks in one run. arXiv preprint arXiv:2111.10007 ,\\n2021. 1\\n[84] Yunyang Xiong, Hanxiao Liu, Suyog Gupta, Berkin Akin,\\nGabriel Bender, Yongzhe Wang, Pieter-Jan Kindermans,\\nMingxing Tan, Vikas Singh, and Bo Chen. MobileDets:\\nSearching for object detection architectures for mobile ac-\\ncelerators. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n3825–3834, 2021. 1\\n[85] Shangliang Xu, Xinxin Wang, Wenyu Lv, Qinyao\\nChang, Cheng Cui, Kaipeng Deng, Guanzhong Wang,\\nQingqing Dang, Shengyu Wei, Yuning Du, et al. PP-\\nYOLOE: An evolved version of YOLO. arXiv preprint\\narXiv:2203.16250 , 2022. 2, 7, 8, 10\\n[86] Zetong Yang, Yin Zhou, Zhifeng Chen, and Jiquan Ngiam.\\n3D-MAN: 3D multi-frame attention network for object de-\\ntection. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n1863–1872, 2021. 5\\n[87] Fisher Yu, Dequan Wang, Evan Shelhamer, and Trevor\\nDarrell. Deep layer aggregation. In Proceedings of the\\nIEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , pages 2403–2412, 2018. 1\\n[88] Guanghua Yu, Qinyao Chang, Wenyu Lv, Chang Xu, Cheng\\nCui, Wei Ji, Qingqing Dang, Kaipeng Deng, GuanzhongWang, Yuning Du, et al. PP-PicoDet: A better real-\\ntime object detector on mobile devices. arXiv preprint\\narXiv:2111.00902 , 2021. 1\\n[89] Hao Zhang, Feng Li, Shilong Liu, Lei Zhang, Hang Su, Jun\\nZhu, Lionel M Ni, and Heung-Yeung Shum. DINO: DETR\\nwith improved denoising anchor boxes for end-to-end ob-\\nject detection. arXiv preprint arXiv:2203.03605 , 2022. 10\\n[90] Haoyang Zhang, Ying Wang, Feras Dayoub, and Niko Sun-\\nderhauf. VarifocalNet: An IoU-aware dense object detector.\\nInProceedings of the IEEE/CVF Conference on Computer\\nVision and Pattern Recognition (CVPR) , pages 8514–8523,\\n2021. 5\\n[91] Shifeng Zhang, Cheng Chi, Yongqiang Yao, Zhen Lei, and\\nStan Z Li. Bridging the gap between anchor-based and\\nanchor-free detection via adaptive training sample selec-\\ntion. In Proceedings of the IEEE/CVF Conference on Com-\\nputer Vision and Pattern Recognition (CVPR) , pages 9759–\\n9768, 2020. 5\\n[92] Xiangyu Zhang, Xinyu Zhou, Mengxiao Lin, and Jian\\nSun. ShufﬂeNet: An extremely efﬁcient convolutional neu-\\nral network for mobile devices. In Proceedings of the\\nIEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , pages 6848–6856, 2018. 1\\n[93] Yifu Zhang, Peize Sun, Yi Jiang, Dongdong Yu, Zehuan\\nYuan, Ping Luo, Wenyu Liu, and Xinggang Wang. BYTE-\\nTrack: Multi-object tracking by associating every detection\\nbox. arXiv preprint arXiv:2110.06864 , 2021. 1\\n[94] Yifu Zhang, Chunyu Wang, Xinggang Wang, Wenjun Zeng,\\nand Wenyu Liu. FAIRMOT: On the fairness of detec-\\ntion and re-identiﬁcation in multiple object tracking. Inter-\\nnational Journal of Computer Vision , 129(11):3069–3087,\\n2021. 1\\n[95] Zhaohui Zheng, Ping Wang, Wei Liu, Jinze Li, Rongguang\\nYe, and Dongwei Ren. Distance-IoU loss: Faster and bet-\\nter learning for bounding box regression. In Proceedings\\nof the AAAI Conference on Artiﬁcial Intelligence (AAAI) ,\\nvolume 34, pages 12993–13000, 2020. 2\\n[96] Dingfu Zhou, Jin Fang, Xibin Song, Chenye Guan, Junbo\\nYin, Yuchao Dai, and Ruigang Yang. IoU loss for 2D/3D\\nobject detection. In International Conference on 3D Vision\\n(3DV) , pages 85–94, 2019. 2\\n[97] Xingyi Zhou, Dequan Wang, and Philipp Kr ¨ahenb ¨uhl. Ob-\\njects as points. arXiv preprint arXiv:1904.07850 , 2019. 1,\\n2\\n[98] Zongwei Zhou, Md Mahfuzur Rahman Siddiquee, Nima\\nTajbakhsh, and Jianming Liang. UNet++: A nested U-\\nNet architecture for medical image segmentation. In\\nDeep Learning in Medical Image Analysis and Multimodal\\nLearning for Clinical Decision Support , 2018. 5\\n[99] Benjin Zhu, Jianfeng Wang, Zhengkai Jiang, Fuhang Zong,\\nSongtao Liu, Zeming Li, and Jian Sun. AutoAssign: Differ-\\nentiable label assignment for dense object detection. arXiv\\npreprint arXiv:2007.03496 , 2020. 2, 5\\n[100] Xizhou Zhu, Weijie Su, Lewei Lu, Bin Li, Xiaogang Wang,\\nand Jifeng Dai. Deformable DETR: Deformable trans-\\nformers for end-to-end object detection. In Proceedings of\\nthe International Conference on Learning Representations\\n(ICLR) , 2021. 10\\n15', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 14}),\n", " Document(page_content='3 grad.illinois.edu/CareerDevelopment Rachel Green  \\n2 1 0  W .  G R E E N  S T . ,  C H A M P A I G N ,  I L  \\n( 2 1 7 )  5 5 5 - 1 2 3 4  •  R S T U D E N T @ I L L I N O I S . E D U  \\nEDUCATION  \\nPhD in English May 20xx \\nUniversity of Illinois at Urbana-Champaign \\nDissertation title:  “Down on the Farm: World War One and the Emergence of Literary  \\nModernism in the American South”  \\nCommittee : <PERSON>, <PERSON>, <PERSON>, <PERSON> (Chair) \\nMA in English  20xx \\nUniversity of Illinois at Urbana-Champaign \\nBA in English and Communications, summa cum laude  20xx \\nButler University, Indianapolis, IN  \\nTEACHING  & A DVISING   \\nComposition Instructor  20xx-present \\nResearch Writing Program, University of Illinois \\n\\uf0b7Facilitator for seven sections of English composition.\\n\\uf0b7Planned and taught a writing-intensive course based upon current events.\\n\\uf0b7Used instructional technology to enhance pedagogical technique.\\n\\uf0b7Taught in part with an innovative, interdisciplinary team-teaching program design.\\nLiterature Instructor 20xx-present \\nDepartment of English, University of Illinois \\n\\uf0b7Instructor of record for two sections of literature, including Major American Authors  and\\nIntroduction to Poetry per semester.\\n\\uf0b7Integrated multimedia and humanities approaches to teaching literature using film and instructional\\ntechnology.\\nCoordinating Group Leader 20xx-20xx \\nResearch Writing Program, University of Illinois \\n\\uf0b7Planned and led required training session for teaching assistants and new composition teachers.\\n\\uf0b7Helped to mentor new hires to the English Department staff to ensure their engagement and\\nprofessional development.\\n\\uf0b7Provided job shadowing and training opportunities to assist new hires in adjusting to the pace of\\nwork and the tone and style of the University.\\nDiscussion Leader  20xx \\nCarolina Summer Reading Program, University of Illinois  \\n\\uf0b7Led group discussion for first-year students on academic topics.\\nTeaching Assistant 20xx-20xx \\nDepartment of English, University of Illinois at Urbana-Champaign \\n\\uf0b7Taught a section on film criticism, including film history, theory and technical vocabulary.\\n\\uf0b7Planned lessons and assignments, led discussion sections, graded papers and exams.\\n\\uf0b7Organized and led group discussions on social and academic issues.CV SAMPLE ', metadata={'source': 'pdfs/rachelgreecv.pdf', 'page': 0}),\n", " Document(page_content='4 grad.illinois.edu/CareerDevelopment RESEARCH  EXPERIENCE   \\nDoctoral Researcher 20xx-20xx \\nDepartment of English, University of Illinois at Urbana-Champaign \\n\\uf0b7Conducted primary source research at numerous archives, examining publication history through\\nmultiple sources.\\n\\uf0b7Examined the literature of <PERSON>, <PERSON>, and <PERSON>, exploring\\ntheir publication records, construction of literary identity, and relationship with modernism.\\nResearch Assistant 20xx \\nDepartment of English, University of Illinois at Urbana-Champaign \\n\\uf0b7Assistant to Professor <PERSON>, conducting primary and secondary source research.\\n\\uf0b7Organized for the “New Directions in the Study of Southern Literature: An Interdisciplinary\\nConference.”\\nPUBLICATIONS   \\nAssociate Editor of North Carolina Slave Narratives. <PERSON>, general editor. Forthcoming \\nfrom University of Illinois Press, 20xx. \\n<PERSON><PERSON>, JM, Lolie, T., and Green, R.  “Lost on the Farm: Popular Beliefs” Somebody Journal, Special \\nIssue, Reflections on the Americas. Vol. 6. Accepted and forthcoming. \\n<PERSON><PERSON>, <PERSON><PERSON> “Fugitives/Agrarians” in A Companion to Twentieth -Century American Poetry. Rutgers \\nPress., 20xx. \\nDavis, D.A. and Green, R.  “Will N. Harben,” “Etheridge Knight,” and “James Wilcox” in Southern \\nWriters: A Biographical Dictionary. Louisiana State University Press, 20xx. \\nCONFERENCE  PRESENTATIONS   \\n“Artistic Colloquialism,” Illinois Graduate College Seminar, speaker and organizer. Urbana, IL, 20xx.  \\n“Transitional Bible Belt,” US Divergence Symposium, Duke University, NC, February 20xx.  \\n“The Ministry of Rev. Thomas H. Jones,” South Atlantic Modern Language Association. Atlanta, GA, \\nMay 20xx. \\n“Shackles and Stripes: The Cinematic Representation of the Southern Chain Gain.” American Literature \\nAssociation. Cambridge, Massachusetts, November 20xx. \\n“Body Place of Sprits in the South,” Queen Mary College, University of London, April 6 -8, 20xx. \\nHONORS  AND  AWARDS  \\nJacob K. Javitz Fellowship, U.S. Department of Education 20xx-present \\nGraduate College Dissertation Completion Award, University of Illinois 20xx \\nCampus Teaching Award based on student evaluations, University of Illinois 20xx-20xx \\nDoctoral Fellowship, Illinois Program for Research in the Humanities,  20xx-20xx \\nUniversity of Illinois \\nSummer Research Grant, Center for Summer Studies, City, ST  20xx \\nGraduate College Conference Travel Grant, University of Illinois 20xx & 20xx \\nMost Outstanding Butler Woman, Butler University, Indianapolis, IN 20xx \\nAcademic Scholarship, Butler University, Indianapolis, IN 20xx-20xx \\nRachel Green, page 2 of 3 ', metadata={'source': 'pdfs/rachelgreecv.pdf', 'page': 1}),\n", " Document(page_content='5 grad.illinois.edu/CareerDevelopment PROFESSIONAL  SERVICE  \\nManaging Editor 20xx-present \\nSouthern Literary Journal  \\n\\uf0b7Process manuscripts submitted for publication\\n\\uf0b7Oversee production and publication procedures.\\n\\uf0b7Maintain editorial correspondence with prospective contributors.\\n\\uf0b7Conduct business transactions including publicity, subscriptions and advertising.\\nPoetry Staff 20xx-present \\nUniversity Quarterly \\n\\uf0b7Review and solicit poems for possible publication.\\nEditorial Assistant 20xx-20xx \\nSouthern Literary Journal \\n\\uf0b7Designed and maintained journal’s internet presence.\\n\\uf0b7Edited copy for publication on a monthly basis.\\nUNIVERSITY  SERVICE  \\nGraduate Mentor 20xx-20xx \\nThe Career Center, University of Illinois \\n\\uf0b7Counsel minority undergraduates on graduate programs, application procedures and funding.\\nCareer Advisory Committee 20xx-20xx \\nGraduate College, University of Illinois \\n\\uf0b7Served on university committee to evaluate and propose career services for graduate students.\\n\\uf0b7Collaborated with faculty and students to prepare final report for submission to the Graduate\\nCollege Dean.\\nUniversity Library Advisory Committee 20xx-20xx \\nUndergraduate Library, University of Illinois \\n\\uf0b7Advised University Librarian on needed services and improvements.\\nPROFESSIONAL  MEMBERSHIPS  \\n\\uf0b7Modern Language Association (MLA)\\n\\uf0b7American Literature Association (ALA)\\n\\uf0b7American Studies Association (ASA)\\n\\uf0b7South Atlantic Modern Language Association\\n(samla)\\uf0b7Society for the Study of Southern Literature\\n\\uf0b7Robert Penn Warren Circle\\n\\uf0b7Southern Research Circle\\n\\uf0b7Fellowship of Southern Writers\\nREFERENCES  \\nJohn Jay , Assoc. Professor of English \\nUniversity of Illinois at Urbana-Champaign \\n**************, <EMAIL> S. Snyder , Assoc. Professor of English \\nUniversity of Illinois at Urbana-Champaign \\n**************, <EMAIL>\\nRobert Roberts , Professor of English \\nUniversity of Illinois at Urbana-Champaign \\n(*************, <EMAIL> Briscoe, Assoc. Professor of English \\nButler University, Indianapolis, IN \\n**************, <EMAIL>\\nRachel Green, page 3 of 3 ', metadata={'source': 'pdfs/rachelgreecv.pdf', 'page': 2})]"]}, "metadata": {}, "execution_count": 7}]}, {"cell_type": "markdown", "source": ["## Split the Extracted Data into Text Chunks"], "metadata": {"id": "dhCeKVOs6AKH"}}, {"cell_type": "code", "source": ["text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=20)"], "metadata": {"id": "8TZQWLG8595f"}, "execution_count": 8, "outputs": []}, {"cell_type": "code", "source": ["text_chunks = text_splitter.split_documents(data)"], "metadata": {"id": "J8ugXa2M6IRG"}, "execution_count": 9, "outputs": []}, {"cell_type": "code", "source": ["text_chunks"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Fe-19krB6KqF", "outputId": "19d03a23-b630-4d38-abf5-916d0345979a"}, "execution_count": 10, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='YOLOv7: Trainable bag-of-freebies sets new state-of-the-art for real-time object\\ndetectors\\n<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>\\n1Institute of Information Science, Academia Sinica, Taiwan\\<EMAIL>, <EMAIL>, and <EMAIL>\\nAbstract\\nYOLOv7 surpasses all known object detectors in both\\nspeed and accuracy in the range from 5 FPS to 160 FPS\\nand has the highest accuracy 56.8% AP among all known', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 0}),\n", " Document(page_content='real-time object detectors with 30 FPS or higher on GPU\\nV100. YOLOv7-E6 object detector (56 FPS V100, 55.9%\\nAP) outperforms both transformer-based detector SWIN-\\nL Cascade-Mask R-CNN (9.2 FPS A100, 53.9% AP) by\\n509% in speed and 2% in accuracy, and convolutional-\\nbased detector ConvNeXt-XL Cascade-Mask R-CNN (8.6\\nFPS A100, 55.2% AP) by 551% in speed and 0.7% AP\\nin accuracy, as well as YOLOv7 outperforms: YOL<PERSON>,\\nYOLOX, Scaled-YOLOv4, YOLOv5, DETR, Deformable', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 0}),\n", " Document(page_content='DETR, DINO-5scale-R50, ViT-Adapter-B and many other\\nobject detectors in speed and accuracy. Moreover, we train\\nYOLOv7 only on MS COCO dataset from scratch without\\nusing any other datasets or pre-trained weights. Source\\ncode is released in https://github.com/WongKinYiu/yolov7.\\n1. Introduction\\nReal-time object detection is a very important topic in\\ncomputer vision, as it is often a necessary component in\\ncomputer vision systems. For example, multi-object track-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 0}),\n", " Document(page_content='ing [94, 93], autonomous driving [40, 18], robotics [35, 58],\\nmedical image analysis [34, 46], etc. The computing de-\\nvices that execute real-time object detection is usually some\\nmobile CPU or GPU, as well as various neural processing\\nunits (NPU) developed by major manufacturers. For exam-\\nple, the Apple neural engine (Apple), the neural compute\\nstick (Intel), Jetson AI edge devices (Nvidia), the edge TPU\\n(Google), the neural processing engine (Qualcomm), the AI', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 0}),\n", " Document(page_content='processing unit (MediaTek), and the AI SoCs (Kneron), are\\nall NPUs. Some of the above mentioned edge devices focus\\non speeding up different operations such as vanilla convolu-\\ntion, depth-wise convolution, or MLP operations. In this pa-\\nper, the real-time object detector we proposed mainly hopes\\nthat it can support both mobile GPU and GPU devices from\\nthe edge to the cloud.\\nIn recent years, the real-time object detector is still de-\\nveloped for different edge device. For example, the devel-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 0}),\n", " Document(page_content='Figure 1: Comparison with other real-time object detectors, our\\nproposed methods achieve state-of-the-arts performance.\\nopment of MCUNet [49, 48] and NanoDet [54] focused on\\nproducing low-power single-chip and improving the infer-\\nence speed on edge CPU. As for methods such as YOLOX\\n[21] and YOLOR [81], they focus on improving the infer-\\nence speed of various GPUs. More recently, the develop-\\nment of real-time object detector has focused on the de-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 0}),\n", " Document(page_content='sign of efﬁcient architecture. As for real-time object de-\\ntectors that can be used on CPU [54, 88, 84, 83], their de-\\nsign is mostly based on MobileNet [28, 66, 27], ShufﬂeNet\\n[92, 55], or GhostNet [25]. Another mainstream real-time\\nobject detectors are developed for GPU [81, 21, 97], they\\nmostly use ResNet [26], DarkNet [63], or DLA [87], and\\nthen use the CSPNet [80] strategy to optimize the architec-\\nture. The development direction of the proposed methods in', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 0}),\n", " Document(page_content='this paper are different from that of the current mainstream\\nreal-time object detectors. In addition to architecture op-\\ntimization, our proposed methods will focus on the opti-\\nmization of the training process. Our focus will be on some\\noptimized modules and optimization methods which may\\nstrengthen the training cost for improving the accuracy of\\nobject detection, but without increasing the inference cost.\\nWe call the proposed modules and optimization methods\\ntrainable bag-of-freebies.', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 0}),\n", " Document(page_content='1arXiv:2207.02696v1  [cs.CV]  6 Jul 2022', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 0}),\n", " Document(page_content='Recently, model re-parameterization [13, 12, 29] and dy-\\nnamic label assignment [20, 17, 42] have become important\\ntopics in network training and object detection. Mainly af-\\nter the above new concepts are proposed, the training of\\nobject detector evolves many new issues. In this paper, we\\nwill present some of the new issues we have discovered and\\ndevise effective methods to address them. For model re-\\nparameterization, we analyze the model re-parameterization', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 1}),\n", " Document(page_content='strategies applicable to layers in different networks with the\\nconcept of gradient propagation path, and propose planned\\nre-parameterized model. In addition, when we discover that\\nwith dynamic label assignment technology, the training of\\nmodel with multiple output layers will generate new issues.\\nThat is: “How to assign dynamic targets for the outputs of\\ndifferent branches?” For this problem, we propose a new\\nlabel assignment method called coarse-to-ﬁne lead guided\\nlabel assignment.', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 1}),\n", " Document(page_content='label assignment.\\nThe contributions of this paper are summarized as fol-\\nlows: (1) we design several trainable bag-of-freebies meth-\\nods, so that real-time object detection can greatly improve\\nthe detection accuracy without increasing the inference\\ncost; (2) for the evolution of object detection methods, we\\nfound two new issues, namely how re-parameterized mod-\\nule replaces original module, and how dynamic label as-\\nsignment strategy deals with assignment to different output', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 1}),\n", " Document(page_content='layers. In addition, we also propose methods to address the\\ndifﬁculties arising from these issues; (3) we propose “ex-\\ntend” and “compound scaling” methods for the real-time\\nobject detector that can effectively utilize parameters and\\ncomputation; and (4) the method we proposed can effec-\\ntively reduce about 40% parameters and 50% computation\\nof state-of-the-art real-time object detector, and has faster\\ninference speed and higher detection accuracy.\\n2. Related work', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 1}),\n", " Document(page_content='2. Related work\\n2.1. Real-time object detectors\\nCurrently state-of-the-art real-time object detectors are\\nmainly based on YOLO [61, 62, 63] and FCOS [76, 77],\\nwhich are [3, 79, 81, 21, 54, 85, 23]. Being able to become\\na state-of-the-art real-time object detector usually requires\\nthe following characteristics: (1) a faster and stronger net-\\nwork architecture; (2) a more effective feature integration\\nmethod [22, 97, 37, 74, 59, 30, 9, 45]; (3) a more accurate', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 1}),\n", " Document(page_content='detection method [76, 77, 69]; (4) a more robust loss func-\\ntion [96, 64, 6, 56, 95, 57]; (5) a more efﬁcient label assign-\\nment method [99, 20, 17, 82, 42]; and (6) a more efﬁcient\\ntraining method. In this paper, we do not intend to explore\\nself-supervised learning or knowledge distillation methods\\nthat require additional data or large model. Instead, we will\\ndesign new trainable bag-of-freebies method for the issues\\nderived from the state-of-the-art methods associated with', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 1}),\n", " Document(page_content='(4), (5), and (6) mentioned above.2.2. Model re-parameterization\\nModel re-parametrization techniques [71, 31, 75, 19, 33,\\n11, 4, 24, 13, 12, 10, 29, 14, 78] merge multiple compu-\\ntational modules into one at inference stage. The model\\nre-parameterization technique can be regarded as an en-\\nsemble technique, and we can divide it into two cate-\\ngories, i.e., module-level ensemble and model-level ensem-\\nble. There are two common practices for model-level re-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 1}),\n", " Document(page_content='parameterization to obtain the ﬁnal inference model. One\\nis to train multiple identical models with different train-\\ning data, and then average the weights of multiple trained\\nmodels. The other is to perform a weighted average of the\\nweights of models at different iteration number. Module-\\nlevel re-parameterization is a more popular research issue\\nrecently. This type of method splits a module into multi-\\nple identical or different module branches during training', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 1}),\n", " Document(page_content='and integrates multiple branched modules into a completely\\nequivalent module during inference. However, not all pro-\\nposed re-parameterized module can be perfectly applied to\\ndifferent architectures. With this in mind, we have devel-\\noped new re-parameterization module and designed related\\napplication strategies for various architectures.\\n2.3. Model scaling\\nModel scaling [72, 60, 74, 73, 15, 16, 2, 51] is a way\\nto scale up or down an already designed model and make', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 1}),\n", " Document(page_content='it ﬁt in different computing devices. The model scaling\\nmethod usually uses different scaling factors, such as reso-\\nlution (size of input image), depth (number of layer), width\\n(number of channel), and stage (number of feature pyra-\\nmid), so as to achieve a good trade-off for the amount of\\nnetwork parameters, computation, inference speed, and ac-\\ncuracy. Network architecture search (NAS) is one of the\\ncommonly used model scaling methods. NAS can automat-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 1}),\n", " Document(page_content='ically search for suitable scaling factors from search space\\nwithout deﬁning too complicated rules. The disadvantage\\nof NAS is that it requires very expensive computation to\\ncomplete the search for model scaling factors. In [15], the\\nresearcher analyzes the relationship between scaling factors\\nand the amount of parameters and operations, trying to di-\\nrectly estimate some rules, and thereby obtain the scaling\\nfactors required by model scaling. Checking the literature,', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 1}),\n", " Document(page_content='we found that almost all model scaling methods analyze in-\\ndividual scaling factor independently, and even the methods\\nin the compound scaling category also optimized scaling\\nfactor independently. The reason for this is because most\\npopular NAS architectures deal with scaling factors that are\\nnot very correlated. We observed that all concatenation-\\nbased models, such as DenseNet [32] or V oVNet [39], will\\nchange the input width of some layers when the depth of', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 1}),\n", " Document(page_content='such models is scaled. Since the proposed architecture is\\nconcatenation-based, we have to design a new compound\\nscaling method for this model.\\n2', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 1}),\n", " Document(page_content='Figure 2: Extended efﬁcient layer aggregation networks. The proposed extended ELAN (E-ELAN) does not change the gradient transmis-\\nsion path of the original architecture at all, but use group convolution to increase the cardinality of the added features, and combine the\\nfeatures of different groups in a shufﬂe and merge cardinality manner. This way of operation can enhance the features learned by different\\nfeature maps and improve the use of parameters and calculations.\\n3. Architecture', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 2}),\n", " Document(page_content='3. Architecture\\n3.1. Extended efﬁcient layer aggregation networks\\nIn most of the literature on designing the efﬁcient ar-\\nchitectures, the main considerations are no more than the\\nnumber of parameters, the amount of computation, and the\\ncomputational density. Starting from the characteristics of\\nmemory access cost, <PERSON> et al. [55] also analyzed the in-\\nﬂuence of the input/output channel ratio, the number of\\nbranches of the architecture, and the element-wise opera-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 2}),\n", " Document(page_content='tion on the network inference speed. <PERSON> ´aret al. [15] addi-\\ntionally considered activation when performing model scal-\\ning, that is, to put more consideration on the number of el-\\nements in the output tensors of convolutional layers. The\\ndesign of CSPV oVNet [79] in Figure 2 (b) is a variation of\\nV oVNet [39]. In addition to considering the aforementioned\\nbasic designing concerns, the architecture of CSPV oVNet\\n[79] also analyzes the gradient path, in order to enable the', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 2}),\n", " Document(page_content='weights of different layers to learn more diverse features.\\nThe gradient analysis approach described above makes in-\\nferences faster and more accurate. ELAN [1] in Figure 2 (c)\\nconsiders the following design strategy – “How to design an\\nefﬁcient network?.” They came out with a conclusion: By\\ncontrolling the shortest longest gradient path, a deeper net-\\nwork can learn and converge effectively. In this paper, we\\npropose Extended-ELAN (E-ELAN) based on ELAN and', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 2}),\n", " Document(page_content='its main architecture is shown in Figure 2 (d).\\nRegardless of the gradient path length and the stacking\\nnumber of computational blocks in large-scale ELAN, it has\\nreached a stable state. If more computational blocks are\\nstacked unlimitedly, this stable state may be destroyed, and\\nthe parameter utilization rate will decrease. The proposedE-ELAN uses expand, shufﬂe, merge cardinality to achieve\\nthe ability to continuously enhance the learning ability of', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 2}),\n", " Document(page_content='the network without destroying the original gradient path.\\nIn terms of architecture, E-ELAN only changes the archi-\\ntecture in computational block, while the architecture of\\ntransition layer is completely unchanged. Our strategy is\\nto use group convolution to expand the channel and car-\\ndinality of computational blocks. We will apply the same\\ngroup parameter and channel multiplier to all the compu-\\ntational blocks of a computational layer. Then, the feature', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 2}),\n", " Document(page_content='map calculated by each computational block will be shuf-\\nﬂed into ggroups according to the set group parameter g,\\nand then concatenate them together. At this time, the num-\\nber of channels in each group of feature map will be the\\nsame as the number of channels in the original architec-\\nture. Finally, we add ggroups of feature maps to perform\\nmerge cardinality. In addition to maintaining the original\\nELAN design architecture, E-ELAN can also guide differ-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 2}),\n", " Document(page_content='ent groups of computational blocks to learn more diverse\\nfeatures.\\n3.2. Model scaling for concatenation-based models\\nThe main purpose of model scaling is to adjust some at-\\ntributes of the model and generate models of different scales\\nto meet the needs of different inference speeds. For ex-\\nample the scaling model of EfﬁcientNet [72] considers the\\nwidth, depth, and resolution. As for the scaled-YOLOv4\\n[79], its scaling model is to adjust the number of stages. In', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 2}),\n", " Document(page_content='[15], <PERSON> al. analyzed the inﬂuence of vanilla convolu-\\ntion and group convolution on the amount of parameter and\\ncomputation when performing width and depth scaling, and\\nused this to design the corresponding model scaling method.\\n3', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 2}),\n", " Document(page_content='Figure 3: Model scaling for concatenation-based models. From (a) to (b), we observe that when depth scaling is performed on\\nconcatenation-based models, the output width of a computational block also increases. This phenomenon will cause the input width\\nof the subsequent transmission layer to increase. Therefore, we propose (c), that is, when performing model scaling on concatenation-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 3}),\n", " Document(page_content='based models, only the depth in a computational block needs to be scaled, and the remaining of transmission layer is performed with\\ncorresponding width scaling.\\nThe above methods are mainly used in architectures such as\\nPlainNet or ResNet. When these architectures are in execut-\\ning scaling up or scaling down, the in-degree and out-degree\\nof each layer will not change, so we can independently an-\\nalyze the impact of each scaling factor on the amount of', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 3}),\n", " Document(page_content='parameters and computation. However, if these methods\\nare applied to the concatenation-based architecture, we will\\nﬁnd that when scaling up or scaling down is performed on\\ndepth, the in-degree of a translation layer which is immedi-\\nately after a concatenation-based computational block will\\ndecrease or increase, as shown in Figure 3 (a) and (b).\\nIt can be inferred from the above phenomenon that\\nwe cannot analyze different scaling factors separately for', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 3}),\n", " Document(page_content='a concatenation-based model but must be considered to-\\ngether. Take scaling-up depth as an example, such an ac-\\ntion will cause a ratio change between the input channel and\\noutput channel of a transition layer, which may lead to a de-\\ncrease in the hardware usage of the model. Therefore, we\\nmust propose the corresponding compound model scaling\\nmethod for a concatenation-based model. When we scale\\nthe depth factor of a computational block, we must also cal-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 3}),\n", " Document(page_content='culate the change of the output channel of that block. Then,\\nwe will perform width factor scaling with the same amount\\nof change on the transition layers, and the result is shown\\nin Figure 3 (c). Our proposed compound scaling method\\ncan maintain the properties that the model had at the initial\\ndesign and maintains the optimal structure.\\n4. Trainable bag-of-freebies\\n4.1. Planned re-parameterized convolution\\nAlthough RepConv [13] has achieved excellent perfor-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 3}),\n", " Document(page_content='mance on the VGG [68], when we directly apply it to\\nResNet [26] and DenseNet [32] and other architectures,\\nits accuracy will be signiﬁcantly reduced. We use gradi-\\nent ﬂow propagation paths to analyze how re-parameterized\\nconvolution should be combined with different network.\\nWe also designed planned re-parameterized convolution ac-\\ncordingly.\\nFigure 4: Planned re-parameterized model. In the proposed\\nplanned re-parameterized model, we found that a layer with resid-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 3}),\n", " Document(page_content='ual or concatenation connections, its RepConv should not have\\nidentity connection. Under these circumstances, it can be replaced\\nby RepConvN that contains no identity connections.\\nRepConv actually combines 3×3convolution, 1×1\\nconvolution, and identity connection in one convolutional\\nlayer. After analyzing the combination and correspond-\\ning performance of RepConv and different architectures,\\nwe ﬁnd that the identity connection in RepConv destroys', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 3}),\n", " Document(page_content='the residual in ResNet and the concatenation in DenseNet,\\nwhich provides more diversity of gradients for different fea-\\nture maps. For the above reasons, we use RepConv with-\\nout identity connection (RepConvN) to design the architec-\\nture of planned re-parameterized convolution. In our think-\\ning, when a convolutional layer with residual or concate-\\nnation is replaced by re-parameterized convolution, there\\nshould be no identity connection. Figure 4 shows an exam-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 3}),\n", " Document(page_content='ple of our designed “planned re-parameterized convolution”\\nused in PlainNet and ResNet. As for the complete planned\\nre-parameterized convolution experiment in residual-based\\nmodel and concatenation-based model, it will be presented\\nin the ablation study session.\\n4', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 3}),\n", " Document(page_content='Figure 5: Coarse for auxiliary and ﬁne for lead head label assigner. Compare with normal model (a), the schema in (b) has auxiliary head.\\nDifferent from the usual independent label assigner (c), we propose (d) lead head guided label assigner and (e) coarse-to-ﬁne lead head\\nguided label assigner. The proposed label assigner is optimized by lead head prediction and the ground truth to get the labels of training', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 4}),\n", " Document(page_content='lead head and auxiliary head at the same time. The detailed coarse-to-ﬁne implementation method and constraint design details will be\\nelaborated in Apendix.\\n4.2. Coarse for auxiliary and ﬁne for lead loss\\nDeep supervision [38] is a technique that is often used\\nin training deep networks. Its main concept is to add\\nextra auxiliary head in the middle layers of the network,\\nand the shallow network weights with assistant loss as the\\nguide. Even for architectures such as ResNet [26] and', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 4}),\n", " Document(page_content='DenseNet [32] which usually converge well, deep supervi-\\nsion [70, 98, 67, 47, 82, 65, 86, 50] can still signiﬁcantly\\nimprove the performance of the model on many tasks. Fig-\\nure 5 (a) and (b) show, respectively, the object detector ar-\\nchitecture “without” and “with” deep supervision. In this\\npaper, we call the head responsible for the ﬁnal output as\\nthe lead head, and the head used to assist training is called\\nauxiliary head.\\nNext we want to discuss the issue of label assignment. In', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 4}),\n", " Document(page_content='the past, in the training of deep network, label assignment\\nusually refers directly to the ground truth and generate hard\\nlabel according to the given rules. However, in recent years,\\nif we take object detection as an example, researchers often\\nuse the quality and distribution of prediction output by the\\nnetwork, and then consider together with the ground truth to\\nuse some calculation and optimization methods to generate\\na reliable soft label [61, 8, 36, 99, 91, 44, 43, 90, 20, 17, 42].', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 4}),\n", " Document(page_content='For example, YOLO [61] use IoU of prediction of bounding\\nbox regression and ground truth as the soft label of object-\\nness. In this paper, we call the mechanism that considers\\nthe network prediction results together with the ground truth\\nand then assigns soft labels as “label assigner.”\\nDeep supervision needs to be trained on the target ob-\\njectives regardless of the circumstances of auxiliary head or\\nlead head. During the development of soft label assigner re-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 4}),\n", " Document(page_content='lated techniques, we accidentally discovered a new deriva-\\ntive issue, i.e., “How to assign soft label to auxiliary head\\nand lead head ?” To the best of our knowledge, the relevant\\nliterature has not explored this issue so far. The results of\\nthe most popular method at present is as shown in Figure 5\\n(c), which is to separate auxiliary head and lead head, and\\nthen use their own prediction results and the ground truthto execute label assignment. The method proposed in this', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 4}),\n", " Document(page_content='paper is a new label assignment method that guides both\\nauxiliary head and lead head by the lead head prediction.\\nIn other words, we use lead head prediction as guidance to\\ngenerate coarse-to-ﬁne hierarchical labels, which are used\\nfor auxiliary head and lead head learning, respectively. The\\ntwo proposed deep supervision label assignment strategies\\nare shown in Figure 5 (d) and (e), respectively.\\nLead head guided label assigner is mainly calculated', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 4}),\n", " Document(page_content='based on the prediction result of the lead head and the\\nground truth, and generate soft label through the optimiza-\\ntion process. This set of soft labels will be used as the tar-\\nget training model for both auxiliary head and lead head.\\nThe reason to do this is because lead head has a relatively\\nstrong learning capability, so the soft label generated from it\\nshould be more representative of the distribution and corre-\\nlation between the source data and the target. Furthermore,', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 4}),\n", " Document(page_content='we can view such learning as a kind of generalized residual\\nlearning. By letting the shallower auxiliary head directly\\nlearn the information that lead head has learned, lead head\\nwill be more able to focus on learning residual information\\nthat has not yet been learned.\\nCoarse-to-ﬁne lead head guided label assigner also\\nused the predicted result of the lead head and the ground\\ntruth to generate soft label. However, in the process we gen-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 4}),\n", " Document(page_content='erate two different sets of soft label, i.e., coarse label and\\nﬁne label, where ﬁne label is the same as the soft label gen-\\nerated by lead head guided label assigner, and coarse label\\nis generated by allowing more grids to be treated as posi-\\ntive target by relaxing the constraints of the positive sample\\nassignment process. The reason for this is that the learning\\nability of an auxiliary head is not as strong as that of a lead\\nhead, and in order to avoid losing the information that needs', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 4}),\n", " Document(page_content='to be learned, we will focus on optimizing the recall of aux-\\niliary head in the object detection task. As for the output\\nof lead head, we can ﬁlter the high precision results from\\nthe high recall results as the ﬁnal output. However, we must\\nnote that if the additional weight of coarse label is close to\\n5', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 4}),\n", " Document(page_content='Table 1: Comparison of baseline object detectors.\\nModel #Param. FLOPs Size APvalAPval\\n50APval\\n75APval\\nSAPval\\nMAPval\\nL\\nYOLOv4 [3] 64.4M 142.8G 640 49.7% 68.2% 54.3% 32.9% 54.8% 63.7%\\nYOLOR-u5 (r6.1) [81] 46.5M 109.1G 640 50.2% 68.7% 54.6% 33.2% 55.5% 63.7%\\nYOLOv4-CSP [79] 52.9M 120.4G 640 50.3% 68.6% 54.9% 34.2% 55.6% 65.1%\\nYOLOR-CSP [81] 52.9M 120.4G 640 50.8% 69.5% 55.3% 33.7% 56.0% 65.4%\\nYOLOv7 36.9M 104.7G 640 51.2% 69.7% 55.5% 35.2% 56.0% 66.7%', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 5}),\n", " Document(page_content='improvement -43% -15% - +0.4 +0.2 +0.2 +1.5 = +1.3\\nYOLOR-CSP-X [81] 96.9M 226.8G 640 52.7% 71.3% 57.4% 36.3% 57.5% 68.3%\\nYOLOv7-X 71.3M 189.9G 640 52.9% 71.1% 57.5% 36.9% 57.7% 68.6%\\nimprovement -36% -19% - +0.2 -0.2 +0.1 +0.6 +0.2 +0.3\\nYOLOv4-tiny [79] 6.1 6.9 416 24.9% 42.1% 25.7% 8.7% 28.4% 39.2%\\nYOLOv7-tiny 6.2 5.8 416 35.2% 52.8% 37.3% 15.7% 38.0% 53.4%\\nimprovement +2% -19% - +10.3 +10.7 +11.6 +7.0 +9.6 +14.2\\nYOLOv4-tiny-3l [79] 8.7 5.2 320 30.8% 47.3% 32.2% 10.9% 31.9% 51.5%', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 5}),\n", " Document(page_content='YOLOv7-tiny 6.2 3.5 320 30.8% 47.3% 32.2% 10.0% 31.9% 52.2%\\nimprovement -39% -49% - = = = -0.9 = +0.7\\nYOLOR-E6 [81] 115.8M 683.2G 1280 55.7% 73.2% 60.7% 40.1% 60.4% 69.2%\\nYOLOv7-E6 97.2M 515.2G 1280 55.9% 73.5% 61.1% 40.6% 60.3% 70.0%\\nimprovement -19% -33% - +0.2 +0.3 +0.4 +0.5 -0.1 +0.8\\nYOLOR-D6 [81] 151.7M 935.6G 1280 56.1% 73.9% 61.2% 42.4% 60.5% 69.9%\\nYOLOv7-D6 154.7M 806.8G 1280 56.3% 73.8% 61.4% 41.3% 60.6% 70.1%\\nYOLOv7-E6E 151.7M 843.2G 1280 56.8% 74.4% 62.1% 40.8% 62.1% 70.6%', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 5}),\n", " Document(page_content='improvement = -11% - +0.7 +0.5 +0.9 -1.6 +1.6 +0.7\\nthat of ﬁne label, it may produce bad prior at ﬁnal predic-\\ntion. Therefore, in order to make those extra coarse positive\\ngrids have less impact, we put restrictions in the decoder,\\nso that the extra coarse positive grids cannot produce soft\\nlabel perfectly. The mechanism mentioned above allows\\nthe importance of ﬁne label and coarse label to be dynam-\\nically adjusted during the learning process, and makes the', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 5}),\n", " Document(page_content='optimizable upper bound of ﬁne label always higher than\\ncoarse label.\\n4.3. Other trainable bag-of-freebies\\nIn this section we will list some trainable bag-of-\\nfreebies. These freebies are some of the tricks we used\\nin training, but the original concepts were not proposed\\nby us. The training details of these freebies will be elab-\\norated in the Appendix, including (1) Batch normalization\\nin conv-bn-activation topology: This part mainly connects', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 5}),\n", " Document(page_content='batch normalization layer directly to convolutional layer.\\nThe purpose of this is to integrate the mean and variance\\nof batch normalization into the bias and weight of convolu-\\ntional layer at the inference stage. (2) Implicit knowledge\\nin YOLOR [81] combined with convolution feature map in\\naddition and multiplication manner: Implicit knowledge in\\nYOLOR can be simpliﬁed to a vector by pre-computing at\\nthe inference stage. This vector can be combined with the', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 5}),\n", " Document(page_content='bias and weight of the previous or subsequent convolutional\\nlayer. (3) EMA model: EMA is a technique used in mean\\nteacher [75], and in our system we use EMA model purely\\nas the ﬁnal inference model.5. Experiments\\n5.1. Experimental setup\\nWe use Microsoft COCO dataset to conduct experiments\\nand validate our object detection method. All our experi-\\nments did not use pre-trained models. That is, all models\\nwere trained from scratch. During the development pro-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 5}),\n", " Document(page_content='cess, we used train 2017 set for training, and then used val\\n2017 set for veriﬁcation and choosing hyperparameters. Fi-\\nnally, we show the performance of object detection on the\\ntest 2017 set and compare it with the state-of-the-art object\\ndetection algorithms. Detailed training parameter settings\\nare described in Appendix.\\nWe designed basic model for edge GPU, normal GPU,\\nand cloud GPU, and they are respectively called YOLOv7-\\ntiny, YOLOv7, and YOLOv7-W6. At the same time, we', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 5}),\n", " Document(page_content='also use basic model for model scaling for different ser-\\nvice requirements and get different types of models. For\\nYOLOv7, we do stack scaling on neck, and use the pro-\\nposed compound scaling method to perform scaling-up of\\nthe depth and width of the entire model, and use this to ob-\\ntain YOLOv7-X. As for YOLOv7-W6, we use the newly\\nproposed compound scaling method to obtain YOLOv7-E6\\nand YOLOv7-D6. In addition, we use the proposed E-\\nELAN for YOLOv7-E6, and thereby complete YOLOv7-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 5}),\n", " Document(page_content='E6E. Since YOLOv7-tiny is an edge GPU-oriented archi-\\ntecture, it will use leaky ReLU as activation function. As\\nfor other models we use SiLU as activation function. We\\nwill describe the scaling factor of each model in detail in\\nAppendix.\\n6', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 5}),\n", " Document(page_content='Table 2: Comparison of state-of-the-art real-time object detectors.\\nModel #Param. FLOPs Size FPS APtest/APvalAPtest\\n50APtest\\n75APtest\\nSAPtest\\nMAPtest\\nL\\nYOLOX-S [21] 9.0M 26.8G 640 102 40.5% / 40.5% - - - - -\\nYOLOX-M [21] 25.3M 73.8G 640 81 47.2% / 46.9% - - - - -\\nYOLOX-L [21] 54.2M 155.6G 640 69 50.1% / 49.7% - - - - -\\nYOLOX-X [21] 99.1M 281.9G 640 58 51.5% / 51.1% - - - - -\\nPPYOLOE-S [85] 7.9M 17.4G 640 208 43.1% / 42.7% 60.5% 46.6% 23.2% 46.4% 56.9%', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 6}),\n", " Document(page_content='PPYOLOE-M [85] 23.4M 49.9G 640 123 48.9% / 48.6% 66.5% 53.0% 28.6% 52.9% 63.8%\\nPPYOLOE-L [85] 52.2M 110.1G 640 78 51.4% / 50.9% 68.9% 55.6% 31.4% 55.3% 66.1%\\nPPYOLOE-X [85] 98.4M 206.6G 640 45 52.2% / 51.9% 69.9% 56.5% 33.3% 56.3% 66.4%\\nYOLOv5-N (r6.1) [23] 1.9M 4.5G 640 159 - / 28.0% - - - - -\\nYOLOv5-S (r6.1) [23] 7.2M 16.5G 640 156 - / 37.4% - - - - -\\nYOLOv5-M (r6.1) [23] 21.2M 49.0G 640 122 - / 45.4% - - - - -\\nYOLOv5-L (r6.1) [23] 46.5M 109.1G 640 99 - / 49.0% - - - - -', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 6}),\n", " Document(page_content='YOLOv5-X (r6.1) [23] 86.7M 205.7G 640 83 - / 50.7% - - - - -\\nYOLOR-CSP [81] 52.9M 120.4G 640 106 51.1% / 50.8% 69.6% 55.7% 31.7% 55.3% 64.7%\\nYOLOR-CSP-X [81] 96.9M 226.8G 640 87 53.0% / 52.7% 71.4% 57.9% 33.7% 57.1% 66.8%\\nYOLOv7-tiny-SiLU 6.2M 13.8G 640 286 38.7% / 38.7% 56.7% 41.7% 18.8% 42.4% 51.9%\\nYOLOv7 36.9M 104.7G 640 161 51.4% / 51.2% 69.7% 55.9% 31.8% 55.5% 65.0%\\nYOLOv7-X 71.3M 189.9G 640 114 53.1% / 52.9% 71.2% 57.8% 33.8% 57.1% 67.4%', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 6}),\n", " Document(page_content='YOLOv5-N6 (r6.1) [23] 3.2M 18.4G 1280 123 - / 36.0% - - - - -\\nYOLOv5-S6 (r6.1) [23] 12.6M 67.2G 1280 122 - / 44.8% - - - - -\\nYOLOv5-M6 (r6.1) [23] 35.7M 200.0G 1280 90 - / 51.3% - - - - -\\nYOLOv5-L6 (r6.1) [23] 76.8M 445.6G 1280 63 - / 53.7% - - - - -\\nYOLOv5-X6 (r6.1) [23] 140.7M 839.2G 1280 38 - / 55.0% - - - - -\\nYOLOR-P6 [81] 37.2M 325.6G 1280 76 53.9% / 53.5% 71.4% 58.9% 36.1% 57.7% 65.6%\\nYOLOR-W6 [81] 79.8G 453.2G 1280 66 55.2% / 54.8% 72.7% 60.5% 37.7% 59.1% 67.1%', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 6}),\n", " Document(page_content='YOLOR-E6 [81] 115.8M 683.2G 1280 45 55.8% / 55.7% 73.4% 61.1% 38.4% 59.7% 67.7%\\nYOLOR-D6 [81] 151.7M 935.6G 1280 34 56.5% / 56.1% 74.1% 61.9% 38.9% 60.4% 68.7%\\nYOLOv7-W6 70.4M 360.0G 1280 84 54.9% / 54.6% 72.6% 60.1% 37.3% 58.7% 67.1%\\nYOLOv7-E6 97.2M 515.2G 1280 56 56.0% / 55.9% 73.5% 61.2% 38.0% 59.9% 68.4%\\nYOLOv7-D6 154.7M 806.8G 1280 44 56.6% / 56.3% 74.0% 61.8% 38.8% 60.1% 69.5%\\nYOLOv7-E6E 151.7M 843.2G 1280 36 56.8% / 56.8% 74.4% 62.1% 39.3% 60.5% 69.0%', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 6}),\n", " Document(page_content='1Our FLOPs is calaculated by rectangle input resolution like 640 ×640 or 1280 ×1280.\\n2Our inference time is estimated by using letterbox resize input image to make its long side equals to 640 or 1280.\\n5.2. Baselines\\nWe choose previous version of YOLO [3, 79] and state-\\nof-the-art object detector YOLOR [81] as our baselines. Ta-\\nble 1 shows the comparison of our proposed YOLOv7 mod-\\nels and those baseline that are trained with the same settings.', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 6}),\n", " Document(page_content='From the results we see that if compared with YOLOv4,\\nYOLOv7 has 75% less parameters, 36% less computation,\\nand brings 1.5% higher AP. If compared with state-of-the-\\nart YOLOR-CSP, YOLOv7 has 43% fewer parameters, 15%\\nless computation, and 0.4% higher AP. In the performance\\nof tiny model, compared with YOLOv4-tiny-31, YOLOv7-\\ntiny reduces the number of parameters by 39% and the\\namount of computation by 49%, but maintains the same AP.\\nOn the cloud GPU model, our model can still have a higher', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 6}),\n", " Document(page_content='AP while reducing the number of parameters by 19% and\\nthe amount of computation by 33%.5.3. Comparison with state-of-the-arts\\nWe compare the proposed method with state-of-the-art\\nobject detectors for general GPUs and Mobile GPUs, and\\nthe results are shown in Table 2. From the results in\\nTable 2 we know that the proposed method has the best\\nspeed-accuracy trade-off comprehensively. If we compare\\nYOLOv7-tiny-SiLU with YOLOv5-N (r6.1), our method', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 6}),\n", " Document(page_content='is 127 fps faster and 10.7% more accurate on AP. In ad-\\ndition, YOLOv7 has 51.4% AP at frame rate of 161 fps,\\nwhile PPYOLOE-L with the same AP has only 78 fps frame\\nrate. In terms of parameter usage, YOLOv7 is 41% less than\\nPPYOLOE-L. If we compare YOLOv7-X with 114 fps in-\\nference speed to YOLOv5-L (r6.1) with 99 fps inference\\nspeed, YOLOv7-X can improve AP by 3.9%. If YOLOv7-\\nX is compared with YOLOv5-X (r6.1) of similar scale, the\\ninference speed of YOLOv7-X is 31 fps faster. In addi-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 6}),\n", " Document(page_content='tion, in terms of the amount of parameters and computation,\\nYOLOv7-X reduces 22% of parameters and 8% of compu-\\ntation compared to YOLOv5-X (r6.1), but improves AP by\\n2.2%.\\n7', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 6}),\n", " Document(page_content='If we compare YOLOv7 with YOLOR using the input\\nresolution 1280, the inference speed of YOLOv7-W6 is 8\\nfps faster than that of YOLOR-P6, and the detection rate is\\nalso increased by 1% AP. As for the comparison between\\nYOLOv7-E6 and YOLOv5-X6 (r6.1), the former has 0.9%\\nAP gain than the latter, 45% less parameters and 63% less\\ncomputation, and the inference speed is increased by 47%.\\nYOLOv7-D6 has close inference speed to YOLOR-E6, but\\nimproves AP by 0.8%. YOLOv7-E6E has close inference', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 7}),\n", " Document(page_content='speed to YOLOR-D6, but improves AP by 0.3%.\\n5.4. Ablation study\\n5.4.1 Proposed compound scaling method\\nTable 3 shows the results obtained when using different\\nmodel scaling strategies for scaling up. Among them, our\\nproposed compound scaling method is to scale up the depth\\nof computational block by 1.5 times and the width of tran-\\nsition block by 1.25 times. If our method is compared with\\nthe method that only scaled up the width, our method can', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 7}),\n", " Document(page_content='improve the AP by 0.5% with less parameters and amount\\nof computation. If our method is compared with the method\\nthat only scales up the depth, our method only needs to in-\\ncrease the number of parameters by 2.9% and the amount of\\ncomputation by 1.2%, which can improve the AP by 0.2%.\\nIt can be seen from the results of Table 3 that our proposed\\ncompound scaling strategy can utilize parameters and com-\\nputation more efﬁciently.\\nTable 3: Ablation study on proposed model scaling.', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 7}),\n", " Document(page_content='Model #Param. FLOPs Size APvalAPval\\n50APval\\n75\\nbase (v7-X light) 47.0M 125.5G 640 51.7% 70.1% 56.0%\\nwidth only (1.25 w)73.4M 195.5G 640 52.4% 70.9% 57.1%\\ndepth only (2.0 d) 69.3M 187.6G 640 52.7% 70.8% 57.3%\\ncompound (v7-X) 71.3M 189.9G 640 52.9% 71.1% 57.5%\\nimprovement - - - +1.2 +1.0 +1.5\\n5.4.2 Proposed planned re-parameterized model\\nIn order to verify the generality of our proposed planed\\nre-parameterized model, we use it on concatenation-based', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 7}),\n", " Document(page_content='model and residual-based model respectively for veriﬁca-\\ntion. The concatenation-based model and residual-based\\nmodel we chose for veriﬁcation are 3-stacked ELAN and\\nCSPDarknet, respectively.\\nIn the experiment of concatenation-based model, we re-\\nplace the 3×3convolutional layers in different positions in\\n3-stacked ELAN with RepConv, and the detailed conﬁgura-\\ntion is shown in Figure 6. From the results shown in Table 4\\nwe see that all higher AP values are present on our proposed', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 7}),\n", " Document(page_content='planned re-parameterized model.\\nIn the experiment dealing with residual-based model,\\nsince the original dark block does not have a 3×3con-\\nFigure 6: Planned RepConv 3-stacked ELAN. Blue circles are the\\nposition we replace Conv by RepConv.\\nTable 4: Ablation study on planned RepConcatenation model.\\nModel APvalAPval\\n50APval\\n75APval\\nSAPval\\nMAPval\\nL\\nbase (3-S ELAN) 52.26% 70.41% 56.77% 35.81% 57.00% 67.59%\\nFigure 6 (a) 52.18% 70.34% 56.90% 35.71% 56.83% 67.51%', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 7}),\n", " Document(page_content='Figure 6 (b) 52.30% 70.30% 56.92% 35.76% 56.95% 67.74%\\nFigure 6 (c) 52.33% 70.56% 56.91% 35.90% 57.06% 67.50%\\nFigure 6 (d) 52.17% 70.32% 56.82% 35.33% 57.06% 68.09%\\nFigure 6 (e) 52.23% 70.20% 56.81% 35.34% 56.97% 66.88%\\nvolution block that conforms to our design strategy, we ad-\\nditionally design a reversed dark block for the experiment,\\nwhose architecture is shown in Figure 7. Since the CSP-\\nDarknet with dark block and reversed dark block has exactly', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 7}),\n", " Document(page_content='the same amount of parameters and operations, it is fair to\\ncompare. The experiment results illustrated in Table 5 fully\\nconﬁrm that the proposed planned re-parameterized model\\nis equally effective on residual-based model. We ﬁnd that\\nthe design of RepCSPResNet [85] also ﬁt our design pat-\\ntern.\\nFigure 7: Reversed CSPDarknet. We reverse the position of 1×1\\nand3×3convolutional layer in dark block to ﬁt our planned re-\\nparameterized model design strategy.', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 7}),\n", " Document(page_content='Table 5: Ablation study on planned RepResidual model.\\nModel APvalAPval\\n50APval\\n75APval\\nSAPval\\nMAPval\\nL\\nbase (YOLOR-W6) 54.82% 72.39% 59.95% 39.68% 59.38% 68.30%\\nRepCSP 54.67% 72.50% 59.58% 40.22% 59.61% 67.87%\\nRCSP 54.36% 71.95% 59.54% 40.15% 59.02% 67.44%\\nRepRCSP 54.85% 72.51% 60.08% 40.53% 59.52% 68.06%\\nbase (YOLOR-CSP) 50.81% 69.47% 55.28% 33.74% 56.01% 65.38%\\nRepRCSP 50.91% 69.54% 55.55% 34.44% 55.74% 65.46%\\n8', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 7}),\n", " Document(page_content='Figure 8: Objectness map predicted by different methods at auxiliary head and lead head.\\n5.4.3 Proposed assistant loss for auxiliary head\\nIn the assistant loss for auxiliary head experiments, we com-\\npare the general independent label assignment for lead head\\nand auxiliary head methods, and we also compare the two\\nproposed lead guided label assignment methods. We show\\nall comparison results in Table 6. From the results listed in\\nTable 6, it is clear that any model that increases assistant', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 8}),\n", " Document(page_content='loss can signiﬁcantly improve the overall performance. In\\naddition, our proposed lead guided label assignment strat-\\negy receives better performance than the general indepen-\\ndent label assignment strategy in AP, AP 50, and AP 75. As\\nfor our proposed coarse for assistant and ﬁne for lead label\\nassignment strategy, it results in best results in all cases. In\\nFigure 8 we show the objectness map predicted by different\\nmethods at auxiliary head and lead head. From Figure 8 we', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 8}),\n", " Document(page_content='ﬁnd that if auxiliary head learns lead guided soft label, it\\nwill indeed help lead head to extract the residual informa-\\ntion from the consistant targets.\\nTable 6: Ablation study on proposed auxiliary head.\\nModel Size APvalAPval\\n50APval\\n75\\nbase (v7-E6) 1280 55.6% 73.2% 60.7%\\nindependent 1280 55.8% 73.4% 60.9%\\nlead guided 1280 55.9% 73.5% 61.0%\\ncoarse-to-ﬁne lead guided 1280 55.9% 73.5% 61.1%\\nimprovement - +0.3 +0.3 +0.4\\nIn Table 7 we further analyze the effect of the proposed', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 8}),\n", " Document(page_content='coarse-to-ﬁne lead guided label assignment method on the\\ndecoder of auxiliary head. That is, we compared the results\\nof with/without the introduction of upper bound constraint.\\nJudging from the numbers in the Table, the method of con-\\nstraining the upper bound of objectness by the distance from\\nthe center of the object can achieve better performance.\\nTable 7: Ablation study on constrained auxiliary head.\\nModel Size APvalAPval\\n50APval\\n75\\nbase (v7-E6) 1280 55.6% 73.2% 60.7%', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 8}),\n", " Document(page_content='aux without constraint 1280 55.9% 73.5% 61.0%\\naux with constraint 1280 55.9% 73.5% 61.1%\\nimprovement - +0.3 +0.3 +0.4Since the proposed YOLOv7 uses multiple pyramids to\\njointly predict object detection results, we can directly con-\\nnect auxiliary head to the pyramid in the middle layer for\\ntraining. This type of training can make up for informa-\\ntion that may be lost in the next level pyramid prediction.\\nFor the above reasons, we designed partial auxiliary head', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 8}),\n", " Document(page_content='in the proposed E-ELAN architecture. Our approach is to\\nconnect auxiliary head after one of the sets of feature map\\nbefore merging cardinality, and this connection can make\\nthe weight of the newly generated set of feature map not\\ndirectly updated by assistant loss. Our design allows each\\npyramid of lead head to still get information from objects\\nwith different sizes. Table 8 shows the results obtained us-\\ning two different methods, i.e., coarse-to-ﬁne lead guided', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 8}),\n", " Document(page_content='and partial coarse-to-ﬁne lead guided methods. Obviously,\\nthe partial coarse-to-ﬁne lead guided method has a better\\nauxiliary effect.\\nTable 8: Ablation study on partial auxiliary head.\\nModel Size APvalAPval\\n50APval\\n75\\nbase (v7-E6E) 1280 56.3% 74.0% 61.5%\\naux 1280 56.5% 74.0% 61.6%\\npartial aux 1280 56.8% 74.4% 62.1%\\nimprovement - +0.5 +0.4 +0.6\\n6. Conclusions\\nIn this paper we propose a new architecture of real-\\ntime object detector and the corresponding model scaling', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 8}),\n", " Document(page_content='method. Furthermore, we ﬁnd that the evolving process\\nof object detection methods generates new research top-\\nics. During the research process, we found the replace-\\nment problem of re-parameterized module and the alloca-\\ntion problem of dynamic label assignment. To solve the\\nproblem, we propose the trainable bag-of-freebies method\\nto enhance the accuracy of object detection. Based on the\\nabove, we have developed the YOLOv7 series of object de-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 8}),\n", " Document(page_content='tection systems, which receives the state-of-the-art results.\\n7. Acknowledgements\\nThe authors wish to thank National Center for High-\\nperformance Computing (NCHC) for providing computa-\\ntional and storage resources.\\n9', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 8}),\n", " Document(page_content='Table 9: More comparison (batch=1, no-TRT, without extra object detection training data)\\nModel #Param. FLOPs Size FPSV100APtest/APvalAPtest\\n50APtest\\n75\\nYOLOv7-tiny-SiLU 6.2M 13.8G 640 286 38.7% /38.7% 56.7% 41.7%\\nPPYOLOE-S [85] 7.9M 17.4G 640 208 43.1% /42.7% 60.5% 46.6%\\nYOLOv7 36.9M 104.7G 640 161 51.4% /51.2% 69.7% 55.9%\\nYOLOv5-N (r6.1) [23] 1.9M 4.5G 640 159 - / 28.0% - -\\nYOLOv5-S (r6.1) [23] 7.2M 16.5G 640 156 - / 37.4% - -\\nPPYOLOE-M [85] 23.4M 49.9G 640 123 48.9% / 48.6% 66.5% 53.0%', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 9}),\n", " Document(page_content='YOLOv5-N6 (r6.1) [23] 3.2M 18.4G 1280 123 - / 36.0% - -\\nYOLOv5-S6 (r6.1) [23] 12.6M 67.2G 1280 122 - / 44.8% - -\\nYOLOv5-M (r6.1) [23] 21.2M 49.0G 640 122 - / 45.4% - -\\nYOLOv7-X 71.3M 189.9G 640 114 53.1% /52.9% 71.2% 57.8%\\nYOLOR-CSP [81] 52.9M 120.4G 640 106 51.1% / 50.8% 69.6% 55.7%\\nYOLOX-S [21] 9.0M 26.8G 640 102 40.5% / 40.5% - -\\nYOLOv5-L (r6.1) [23] 46.5M 109.1G 640 99 - / 49.0% - -\\nYOLOv5-M6 (r6.1) [23] 35.7M 200.0G 1280 90 - / 51.3% - -', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 9}),\n", " Document(page_content='YOLOR-CSP-X [81] 96.9M 226.8G 640 87 53.0% / 52.7% 71.4% 57.9%\\nYOLOv7-W6 70.4M 360.0G 1280 84 54.9% /54.6% 72.6% 60.1%\\nYOLOv5-X (r6.1) [23] 86.7M 205.7G 640 83 - / 50.7% - -\\nYOLOX-M [21] 25.3M 73.8G 640 81 47.2% / 46.9% - -\\nPPYOLOE-L [85] 52.2M 110.1G 640 78 51.4% / 50.9% 68.9% 55.6%\\nYOLOR-P6 [81] 37.2M 325.6G 1280 76 53.9% / 53.5% 71.4% 58.9%\\nYOLOX-L [21] 54.2M 155.6G 640 69 50.1% / 49.7% - -\\nYOLOR-W6 [81] 79.8G 453.2G 1280 66 55.2% /54.8% 72.7% 60.5%', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 9}),\n", " Document(page_content='YOLOv5-L6 (r6.1) [23] 76.8M 445.6G 1280 63 - / 53.7% - -\\nYOLOX-X [21] 99.1M 281.9G 640 58 51.5% / 51.1% - -\\nYOLOv7-E6 97.2M 515.2G 1280 56 56.0% /55.9% 73.5% 61.2%\\nYOLOR-E6 [81] 115.8M 683.2G 1280 45 55.8% / 55.7% 73.4% 61.1%\\nPPYOLOE-X [85] 98.4M 206.6G 640 45 52.2% / 51.9% 69.9% 56.5%\\nYOLOv7-D6 154.7M 806.8G 1280 44 56.6% /56.3% 74.0% 61.8%\\nYOLOv5-X6 (r6.1) [23] 140.7M 839.2G 1280 38 - / 55.0% - -\\nYOLOv7-E6E 151.7M 843.2G 1280 36 56.8% /56.8% 74.4% 62.1%', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 9}),\n", " Document(page_content='YOLOR-D6 [81] 151.7M 935.6G 1280 34 56.5% / 56.1% 74.1% 61.9%\\nF-RCNN-R101-FPN+ [5] 60.0M 246.0G 1333 20 - / 44.0% - -\\nDeformable DETR [100] 40.0M 173.0G - 19 - / 46.2% - -\\nSwin-B (C-M-RCNN) [52] 145.0M 982.0G 1333 11.6 - / 51.9% - -\\nDETR DC5-R101 [5] 60.0M 253.0G 1333 10 - / 44.9% - -\\nEfﬁcientDet-D7x [74] 77.0M 410.0G 1536 6.5 55.1% / 54.4% 72.4% 58.4%\\nDual-Swin-T (C-M-RCNN) [47] 113.8M 836.0G 1333 6.5 - / 53.6% - -\\nViT-Adapter-B [7] 122.0M 997.0G - 4.4 - / 50.8% - -', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 9}),\n", " Document(page_content='Dual-Swin-B (HTC) [47] 235.0M - 1600 2.5 58.7% /58.4% - -\\nDual-Swin-L (HTC) [47] 453.0M - 1600 1.5 59.4% /59.1% - -\\nModel #Param. FLOPs Size FPSA100APtest/APvalAPtest\\n50APtest\\n75\\nDN-Deformable-DETR [41] 48.0M 265.0G 1333 23.0 - / 48.6% - -\\nConvNeXt-B (C-M-RCNN) [53] - 964.0G 1280 11.5 - / 54.0% 73.1% 58.8%\\nSwin-B (C-M-RCNN) [52] - 982.0G 1280 10.7 - / 53.0% 71.8% 57.5%\\nDINO-5scale (R50) [89] 47.0M 860.0G 1333 10.0 - / 51.0% - -', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 9}),\n", " Document(page_content='ConvNeXt-L (C-M-RCNN) [53] - 1354.0G 1280 10.0 - / 54.8% 73.8% 59.8%\\nSwin-L (C-M-RCNN) [52] - 1382.0G 1280 9.2 - / 53.9% 72.4% 58.8%\\nConvNeXt-XL (C-M-RCNN) [53] - 1898.0G 1280 8.6 - / 55.2% 74.2% 59.9%\\n8. More comparison\\nYOLOv7 surpasses all known object detectors in both\\nspeed and accuracy in the range from 5 FPS to 160 FPS and\\nhas the highest accuracy 56.8% AP test-dev / 56.8% AP\\nmin-val among all known real-time object detectors with 30\\nFPS or higher on GPU V100. YOLOv7-E6 object detector', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 9}),\n", " Document(page_content='(56 FPS V100, 55.9% AP) outperforms both transformer-\\nbased detector SWIN-L Cascade-Mask R-CNN (9.2 FPS\\nA100, 53.9% AP) by 509% in speed and 2% in accuracy,and convolutional-based detector ConvNeXt-XL Cascade-\\nMask R-CNN (8.6 FPS A100, 55.2% AP) by 551% in speed\\nand 0.7% AP in accuracy, as well as YOLOv7 outperforms:\\nYOL<PERSON>, YOLOX, Scaled-YOLOv4, YOLOv5, DETR, De-\\nformable DETR, DINO-5scale-R50, ViT-Adapter-B and\\nmany other object detectors in speed and accuracy. More', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 9}),\n", " Document(page_content='over, we train YOLOv7 only on MS COCO dataset from\\nscratch without using any other datasets or pre-trained\\nweights.\\n10', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 9}),\n", " Document(page_content='Figure 9: Comparison with other object detectors.\\nFigure 10: Comparison with other real-time object detectors.\\nTable 10: Comparison of different setting.\\nModel Presicion IoU threshold APval\\nYOLOv7-X FP16 (default) 0.65 (default) 52.9%\\nYOLOv7-X FP32 0.65 53.0%\\nYOLOv7-X FP16 0.70 53.0%\\nYOLOv7-X FP32 0.70 53.1%\\nimprovement - - +0.2%\\n*Similar to meituan/YOLOv6 and PPYOLOE, our model could\\nget higher AP when set higher IoU threshold.\\nThe maximum accuracy of the YOLOv7-E6E (56.8%', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 10}),\n", " Document(page_content='AP) real-time model is +13.7% AP higher than the cur-\\nrent most accurate meituan/YOLOv6-s model (43.1% AP)\\non COCO dataset. Our YOLOv7-tiny (35.2% AP, 0.4\\nms) model is +25% faster and +0.2% AP higher than\\nmeituan/YOLOv6-n (35.0% AP, 0.5 ms) under identical\\nconditions on COCO dataset and V100 GPU with batch=32.\\nFigure 11: Comparison with other real-time object detectors.\\n11', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 10}),\n", " Document(page_content='References\\n[1] anonymous. Designing network design strategies. anony-\\nmous submission , 2022. 3\\n[2] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>\\<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,\\nand Barret Zoph. Revisiting ResNets: Improved training\\nand scaling strategies. Advances in Neural Information Pro-\\ncessing Systems (NeurIPS) , 34, 2021. 2\\n[3] <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>\\nYuan <PERSON>. YOLOv4: Optimal speed and accuracy of', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 11}),\n", " Document(page_content='object detection. arXiv preprint arXiv:2004.10934 , 2020.\\n2, 6, 7\\n[4] <PERSON><PERSON>, <PERSON>, <PERSON>,\\nand <PERSON><PERSON><PERSON>. Ensemble deep learning in bioinformat-\\nics.Nature Machine Intelligence , 2(9):500–508, 2020. 2\\n[5] <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>.\\nEnd-to-end object detection with transformers. In Pro-\\nceedings of the European Conference on Computer Vision\\n(ECCV) , pages 213–229, 2020. 10', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 11}),\n", " Document(page_content='[6] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and\\n<PERSON><PERSON><PERSON>. AP-loss for accurate one-stage object detection.\\nIEEE Transactions on Pattern Analysis and Machine Intel-\\nligence (TPAMI) , 43(11):3782–3798, 2020. 2\\n[7] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Vision transformer adapter for\\ndense predictions. arXiv preprint arXiv:2205.08534 , 2022.\\n10\\n[8] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 11}),\n", " Document(page_content='Lee. Gaussian YOLOv3: An accurate and fast object detec-\\ntor using localization uncertainty for autonomous driving.\\nInProceedings of the IEEE/CVF International Conference\\non Computer Vision (ICCV) , pages 502–511, 2019. 5\\n[9] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Dynamic head:\\nUnifying object detection heads with attentions. In Pro-\\nceedings of the IEEE/CVF Conference on Computer Vision\\nand Pattern Recognition (CVPR) , pages 7373–7382, 2021.\\n2', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 11}),\n", " Document(page_content='2\\n[10] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Re-\\nparameterizing your optimizers rather than architectures.\\narXiv preprint arXiv:2205.15242 , 2022. 2\\n[11] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>\\n<PERSON><PERSON>. ACNet: Strengthening the kernel skeletons for pow-\\nerful CNN via asymmetric convolution blocks. In Proceed-\\nings of the IEEE/CVF International Conference on Com-\\nputer Vision (ICCV) , pages 1911–1920, 2019. 2', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 11}),\n", " Document(page_content='[12] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and\\nG<PERSON><PERSON><PERSON>. Diverse branch block: Building a con-\\nvolution as an inception-like unit. In Proceedings of the\\nIEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , pages 10886–10895, 2021. 2\\n[13] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>n<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>. RepVGG: Making\\nVGG-style convnets great again. In Proceedings of theIEEE/CVF Conference on Computer Vision and Pattern', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 11}),\n", " Document(page_content='Recognition (CVPR) , pages 13733–13742, 2021. 2, 4\\n[14] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Scaling up your ker-\\nnels to 31x31: Revisiting large kernel design in CNNs. In\\nProceedings of the IEEE/CVF Conference on Computer Vi-\\nsion and Pattern Recognition (CVPR) , 2022. 2\\n[15] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. Fast and\\naccurate model scaling. In Proceedings of the IEEE/CVF\\nConference on Computer Vision and Pattern Recognition', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 11}),\n", " Document(page_content='(CVPR) , pages 924–932, 2021. 2, 3\\n[16] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>\\nLin. Simple training strategies and model scaling for object\\ndetection. arXiv preprint arXiv:2107.00057 , 2021. 2\\n[17] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>,\\nand <PERSON><PERSON>. TOOD: Task-aligned one-stage object\\ndetection. In Proceedings of the IEEE/CVF International\\nConference on Computer Vision (ICCV) , pages 3490–3499,\\n2021. 2, 5', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 11}),\n", " Document(page_content='2021. 2, 5\\n[18] <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>\\<PERSON><PERSON>, and <PERSON>. Deep multi-modal object de-\\ntection and semantic segmentation for autonomous driv-\\ning: Datasets, methods, and challenges. IEEE Transac-\\ntions on Intelligent Transportation Systems , 22(3):1341–\\n1360, 2020. 1\\n[19] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>,\\n<PERSON><PERSON><PERSON>, and <PERSON>. Loss sur-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 11}),\n", " Document(page_content='faces, mode connectivity, and fast ensembling of DNNs.\\nAdvances in Neural Information Processing Systems\\n(NeurIPS) , 31, 2018. 2\\n[20] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and\\nJian Sun. OTA: Optimal transport assignment for object\\ndetection. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n303–312, 2021. 2, 5\\n[21] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\\nSun. YOLOX: Exceeding YOLO series in 2021. arXiv', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 11}),\n", " Document(page_content='preprint arXiv:2107.08430 , 2021. 1, 2, 7, 10\\n[22] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> <PERSON>. NAS-FPN:\\nLearning scalable feature pyramid architecture for object\\ndetection. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n7036–7045, 2019. 2\\n[23] <PERSON><PERSON>. YOLOv5 release v6.1. https://github.com/\\nultralytics/yolov5/releases/tag/v6.1, 2022. 2, 7, 10\\n[24] <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Ex-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 11}),\n", " Document(page_content='pandNets: Linear over-parameterization to train compact\\nconvolutional networks. Advances in Neural Information\\nProcessing Systems (NeurIPS) , 33:1298–1310, 2020. 2\\n[25] <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>\\n<PERSON>u, and <PERSON>. GhostNet: More features from cheap\\noperations. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n1580–1589, 2020. 1\\n[26] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>.', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 11}),\n", " Document(page_content='Deep residual learning for image recognition. In Proceed-\\n12', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 11}),\n", " Document(page_content='ings of the IEEE/CVF Conference on Computer Vision and\\nPattern Recognition (CVPR) , pages 770–778, 2016. 1, 4, 5\\n[27] <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>\\<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>,\\<PERSON><PERSON><PERSON><PERSON>, <PERSON>, et al. Searching for Mo-\\nbileNetV3. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n1314–1324, 2019. 1\\n[28] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 12}),\n", " Document(page_content=<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>. MobileNets: Efﬁcient con-\\nvolutional neural networks for mobile vision applications.\\narXiv preprint arXiv:1704.04861 , 2017. 1\\n[29] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. On-\\nline convolutional re-parameterization. In Proceedings of\\nthe IEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , 2022. 2', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 12}),\n", " Document(page_content='[30] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. A2-FPN:\\nAttention aggregation based feature pyramid network for\\ninstance segmentation. In Proceedings of the IEEE/CVF\\nConference on Computer Vision and Pattern Recognition\\n(CVPR) , pages 15343–15352, 2021. 2\\n[31] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>\\nHopcroft, and <PERSON><PERSON>. Snapshot ensembles:\\nTrain 1, get m for free. International Conference on Learn-\\ning Representations (ICLR) , 2017. 2', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 12}),\n", " Document(page_content='[32] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. Densely connected convolutional net-\\nworks. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n4700–4708, 2017. 2, 4, 5\\n[33] <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>,\\n<PERSON><PERSON><PERSON>, and <PERSON>. Averaging\\nweights leads to wider optima and better generalization. In\\nConference on Uncertainty in Artiﬁcial Intelligence (UAI) ,\\n2018. 2', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 12}),\n", " Document(page_content='2018. 2\\n[34] <PERSON>, <PERSON>, <PERSON>-\\<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>\\nS<PERSON><PERSON><PERSON>, and <PERSON>. Retina U-Net: Em-\\nbarrassingly simple exploitation of segmentation supervi-\\nsion for medical object detection. In Machine Learning for\\nHealth Workshop , pages 171–183, 2020. 1\\n[35] <PERSON><PERSON> and <PERSON><PERSON>. Object detection ap-\\nproach for robot grasp detection. In IEEE International\\nConference on Robotics and Automation (ICRA) , pages', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 12}),\n", " Document(page_content='4953–4959, 2019. 1\\n[36] <PERSON> and <PERSON><PERSON>. Probabilistic anchor as-\\nsignment with iou prediction for object detection. In Pro-\\nceedings of the European conference on computer vision\\n(ECCV) , pages 355–371, 2020. 5\\n[37] <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>\\nDoll´ar. Panoptic feature pyramid networks. In Proceed-\\nings of the IEEE/CVF Conference on Computer Vision and\\nPattern Recognition (CVPR) , pages 6399–6408, 2019. 2', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 12}),\n", " Document(page_content='[38] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Deeply-supervised nets. In Arti-\\nﬁcial Intelligence and Statistics , pages 562–570, 2015. 5[39] <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>\\nB<PERSON>, and <PERSON><PERSON><PERSON>. An energy and GPU-computation\\nefﬁcient backbone network for real-time object detection.\\nInProceedings of the IEEE/CVF Conference on Com-\\nputer Vision and Pattern Recognition Workshops (CVPRW) ,\\npages 0–0, 2019. 2, 3', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 12}),\n", " Document(page_content='[40] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and\\nXiaogan<PERSON>. GS3D: An efﬁcient 3d object detection\\nframework for autonomous driving. In Proceedings of the\\nIEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , pages 1019–1028, 2019. 1\\n[41] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>\\nNi, and <PERSON><PERSON>. DN-DETR: Accelerate detr training\\nby introducing query denoising. In Proceedings of the\\nIEEE/CVF Conference on Computer Vision and Pattern', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 12}),\n", " Document(page_content='Recognition (CVPR) , pages 13619–13627, 2022. 10\\n[42] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>. A\\ndual weighting label assignment scheme for object detec-\\ntion. In Proceedings of the IEEE/CVF Conference on Com-\\nputer Vision and Pattern Recognition (CVPR) , pages 9387–\\n9396, 2022. 2, 5\\n[43] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>,\\nand <PERSON><PERSON>. Generalized focal loss v2: Learning reliable\\nlocalization quality estimation for dense object detection. In', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 12}),\n", " Document(page_content='Proceedings of the IEEE/CVF Conference on Computer Vi-\\nsion and Pattern Recognition (CVPR) , pages 11632–11641,\\n2021. 5\\n[44] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Generalized focal\\nloss: Learning qualiﬁed and distributed bounding boxes for\\ndense object detection. Advances in Neural Information\\nProcessing Systems (NeurIPS) , 33:21002–21012, 2020. 5\\n[45] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>.', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 12}),\n", " Document(page_content='Exploring plain vision transformer backbones for object de-\\ntection. arXiv preprint arXiv:2203.16527 , 2022. 2\\n[46] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Pan<PERSON>Zhou, and <PERSON><PERSON><PERSON><PERSON>. CLU-CNNs: Object detection for\\nmedical images. Neurocomputing , 350:53–59, 2019. 1\\n[47] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>,\\<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. CB-\\nNetV2: A composite backbone network architecture for ob-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 12}),\n", " Document(page_content='ject detection. arXiv preprint arXiv:2107.00420 , 2021. 5,\\n10\\n[48] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>\\nHan. Memory-efﬁcient patch-based inference for tiny deep\\nlearning. Advances in Neural Information Processing Sys-\\ntems (NeurIPS) , 34:2346–2358, 2021. 1\\n[49] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>\\n<PERSON>, et al. MCUNet: Tiny deep learning on IoT de-\\nvices. Advances in Neural Information Processing Systems\\n(NeurIPS) , 33:11711–11722, 2020. 1', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 12}),\n", " Document(page_content='[50] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. YOLOStereo3D:\\nA step back to 2D for efﬁcient stereo 3D detection. In\\nIEEE International Conference on Robotics and Automa-\\ntion (ICRA) , pages 13018–13024, 2021. 5\\n[51] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,\\<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>,\\n13', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 12}),\n", " Document(page_content='et al. Swin transformer v2: Scaling up capacity and res-\\nolution. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , 2022. 2\\n[52] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Swin transformer:\\nHierarchical vision transformer using shifted windows. In\\nProceedings of the IEEE/CVF International Conference on\\nComputer Vision (ICCV) , pages 10012–10022, 2021. 10', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 13}),\n", " Document(page_content='[53] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. A ConvNet for\\nthe 2020s. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n11976–11986, 2022. 10\\n[54] <PERSON><PERSON><PERSON>. NanoDet-Plus. https://github.com/RangiLyu/\\nnanodet/releases/tag/v1.0.0-alpha-1, 2021. 1, 2\\n[55] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\\nSun. ShufﬂeNet V2: Practical guidelines for efﬁcient CNN', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 13}),\n", " Document(page_content='architecture design. In Proceedings of the European Con-\\nference on Computer Vision (ECCV) , pages 116–131, 2018.\\n1, 3\\n[56] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>\\nKalkan. A ranking-based, balanced loss function unifying\\nclassiﬁcation and localisation in object detection. Advances\\nin Neural Information Processing Systems (NeurIPS) ,\\n33:15534–15545, 2020. 2\\n[57] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>\\nKalkan. Rank & sort loss for object detection and in-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 13}),\n", " Document(page_content='stance segmentation. In Proceedings of the IEEE/CVF In-\\nternational Conference on Computer Vision (ICCV) , pages\\n3009–3018, 2021. 2\\n[58] <PERSON><PERSON>, <PERSON><PERSON><PERSON>,\\<PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>\\n<PERSON><PERSON>. Object detection and pose estimation from rgb and\\ndepth data for real-time, adaptive robotic grasping. In Ad-\\nvances in Computer Vision and Computational Biology ,\\npages 121–142. 2021. 1\\n[59] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. <PERSON>-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 13}),\n", " Document(page_content='tectoRS: Detecting objects with recursive feature pyramid\\nand switchable atrous convolution. In Proceedings of the\\nIEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , pages 10213–10224, 2021. 2\\n[60] <PERSON><PERSON>, <PERSON>, <PERSON>,\\<PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Designing network design\\nspaces. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n10428–10436, 2020. 2', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 13}),\n", " Document(page_content='[61] <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>\\n<PERSON><PERSON><PERSON><PERSON>. You only look once: Uniﬁed, real-time object de-\\ntection. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n779–788, 2016. 2, 5\\n[62] <PERSON> and <PERSON>. YOLO9000: better, faster,\\nstronger. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n7263–7271, 2017. 2\\n[63] <PERSON> and <PERSON>. YOLOv3: An incremental', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 13}),\n", " Document(page_content='improvement. arXiv preprint arXiv:1804.02767 , 2018. 1, 2[64] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Generalized in-\\ntersection over union: A metric and a loss for bounding\\nbox regression. In Proceedings of the IEEE/CVF Confer-\\nence on Computer Vision and Pattern Recognition (CVPR) ,\\npages 658–666, 2019. 2\\n[65] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and\\n<PERSON><PERSON><PERSON><PERSON>. Sparse DETR: Efﬁcient end-to-end ob-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 13}),\n", " Document(page_content='ject detection with learnable sparsity. arXiv preprint\\narXiv:2111.14330 , 2021. 5\\n[66] <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. MobileNetV2: In-\\nverted residuals and linear bottlenecks. In Proceedings of\\nthe IEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , pages 4510–4520, 2018. 1\\n[67] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>,\\nYuron<PERSON>, and <PERSON><PERSON><PERSON>. Object detection', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 13}),\n", " Document(page_content='from scratch with deep supervision. IEEE Transactions\\non Pattern Analysis and Machine Intelligence (TPAMI) ,\\n42(2):398–412, 2019. 5\\n[68] <PERSON> and <PERSON>. Very deep convo-\\nlutional networks for large-scale image recognition. arXiv\\npreprint arXiv:1409.1556 , 2014. 4\\n[69] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>\\<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>,\\n<PERSON><PERSON><PERSON>, et al. Sparse R-CNN: End-to-end ob-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 13}),\n", " Document(page_content='ject detection with learnable proposals. In Proceedings of\\nthe IEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , pages 14454–14463, 2021. 2\\n[70] <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>,\\<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Going deeper with\\nconvolutions. In Proceedings of the IEEE/CVF Confer-\\nence on Computer Vision and Pattern Recognition (CVPR) ,\\npages 1–9, 2015. 5', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 13}),\n", " Document(page_content='pages 1–9, 2015. 5\\n[71] <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Rethinking the inception\\narchitecture for computer vision. In Proceedings of the\\nIEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , pages 2818–2826, 2016. 2\\n[72] Ming<PERSON> Tan and Quoc Le. EfﬁcientNet: Rethinking\\nmodel scaling for convolutional neural networks. In Inter-\\nnational Conference on Machine Learning (ICML) , pages\\n6105–6114, 2019. 2, 3', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 13}),\n", " Document(page_content='[73] <PERSON><PERSON> and <PERSON><PERSON><PERSON>. EfﬁcientNetv2: Smaller mod-\\nels and faster training. In International Conference on Ma-\\nchine Learning (ICML) , pages 10096–10106, 2021. 2\\n[74] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and Quoc V Le. Efﬁcient-\\nDet: Scalable and efﬁcient object detection. In Proceedings\\nof the IEEE/CVF Conference on Computer Vision and Pat-\\ntern Recognition (CVPR) , pages 10781–10790, 2020. 2, 10\\n[75] <PERSON><PERSON> and <PERSON><PERSON>. Mean teachers are better', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 13}),\n", " Document(page_content='role models: Weight-averaged consistency targets improve\\nsemi-supervised deep learning results. Advances in Neural\\nInformation Processing Systems (NeurIPS) , 30, 2017. 2, 6\\n[76] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. FCOS:\\nFully convolutional one-stage object detection. In Proceed-\\n14', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 13}),\n", " Document(page_content='ings of the IEEE/CVF International Conference on Com-\\nputer Vision (ICCV) , pages 9627–9636, 2019. 2\\n[77] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. FCOS:\\nA simple and strong anchor-free object detector. IEEE\\nTransactions on Pattern Analysis and Machine Intelligence\\n(TPAMI) , 44(4):1922–1933, 2022. 2\\n[78] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. An im-\\nproved one millisecond mobile backbone. arXiv preprint\\narXiv:2206.04040 , 2022. 2', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 14}),\n", " Document(page_content='[79] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>\\<PERSON><PERSON><PERSON>. Scaled-YOLOv4: Scaling cross stage\\npartial network. In Proceedings of the IEEE/CVF Confer-\\nence on Computer Vision and Pattern Recognition (CVPR) ,\\npages 13029–13038, 2021. 2, 3, 6, 7\\n[80] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,\\n<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. CSP-\\nNet: A new backbone that can enhance learning capabil-\\nity of CNN. In Proceedings of the IEEE/CVF Conference', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 14}),\n", " Document(page_content='on Computer Vision and Pattern Recognition Workshops\\n(CVPRW) , pages 390–391, 2020. 1\\n[81] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>.\\nYou only learn one representation: Uniﬁed network for\\nmultiple tasks. arXiv preprint arXiv:2105.04206 , 2021. 1,\\n2, 6, 7, 10\\n[82] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>\\<PERSON><PERSON><PERSON>, and <PERSON><PERSON>. End-to-end object detection\\nwith fully convolutional network. In Proceedings of the\\nIEEE/CVF Conference on Computer Vision and Pattern', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 14}),\n", " Document(page_content='Recognition (CVPR) , pages 15849–15858, 2021. 2, 5\\n[83] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,\\<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>,\\nand <PERSON>. FBNetv5: Neural architecture search for\\nmultiple tasks in one run. arXiv preprint arXiv:2111.10007 ,\\n2021. 1\\n[84] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>,\\n<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,\\n<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. MobileDets:', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 14}),\n", " Document(page_content='Searching for object detection architectures for mobile ac-\\ncelerators. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n3825–3834, 2021. 1\\n[85] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al. PP-\\nYOLOE: An evolved version of YOLO. arXiv preprint\\narXiv:2203.16250 , 2022. 2, 7, 8, 10\\n[86] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>.', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 14}),\n", " Document(page_content='3D-MAN: 3D multi-frame attention network for object de-\\ntection. In Proceedings of the IEEE/CVF Conference on\\nComputer Vision and Pattern Recognition (CVPR) , pages\\n1863–1872, 2021. 5\\n[87] <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>\\n<PERSON><PERSON><PERSON>. Deep layer aggregation. In Proceedings of the\\nIEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , pages 2403–2412, 2018. 1\\n[88] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 14}),\n", " Document(page_content=<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al. PP-PicoDet: A better real-\\ntime object detector on mobile devices. arXiv preprint\\narXiv:2111.00902 , 2021. 1\\n[89] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. DINO: DETR\\nwith improved denoising anchor boxes for end-to-end ob-\\nject detection. arXiv preprint arXiv:2203.03605 , 2022. 10\\n[90] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 14}),\n", " Document(page_content='derhauf. VarifocalNet: An IoU-aware dense object detector.\\nInProceedings of the IEEE/CVF Conference on Computer\\nVision and Pattern Recognition (CVPR) , pages 8514–8523,\\n2021. 5\\n[91] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and\\nStan <PERSON>. Bridging the gap between anchor-based and\\nanchor-free detection via adaptive training sample selec-\\ntion. In Proceedings of the IEEE/CVF Conference on Com-\\nputer Vision and Pattern Recognition (CVPR) , pages 9759–\\n9768, 2020. 5', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 14}),\n", " Document(page_content='9768, 2020. 5\\n[92] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>\\nSun. ShufﬂeNet: An extremely efﬁcient convolutional neu-\\nral network for mobile devices. In Proceedings of the\\nIEEE/CVF Conference on Computer Vision and Pattern\\nRecognition (CVPR) , pages 6848–6856, 2018. 1\\n[93] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. BYTE-\\nTrack: Multi-object tracking by associating every detection\\nbox. arXiv preprint arXiv:2110.06864 , 2021. 1', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 14}),\n", " Document(page_content='[94] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>,\\nand <PERSON><PERSON>. FAIRMOT: On the fairness of detec-\\ntion and re-identiﬁcation in multiple object tracking. Inter-\\nnational Journal of Computer Vision , 129(11):3069–3087,\\n2021. 1\\n[95] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>\\n<PERSON><PERSON>, and <PERSON><PERSON>. Distance-IoU loss: Faster and bet-\\nter learning for bounding box regression. In Proceedings\\nof the AAAI Conference on Artiﬁcial Intelligence (AAAI) ,', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 14}),\n", " Document(page_content='volume 34, pages 12993–13000, 2020. 2\\n[96] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. IoU loss for 2D/3D\\nobject detection. In International Conference on 3D Vision\\n(3DV) , pages 85–94, 2019. 2\\n[97] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> ¨uhl. Ob-\\njects as points. arXiv preprint arXiv:1904.07850 , 2019. 1,\\n2\\n[98] <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>\\n<PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. UNet++: A nested U-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 14}),\n", " Document(page_content='Net architecture for medical image segmentation. In\\nDeep Learning in Medical Image Analysis and Multimodal\\nLearning for Clinical Decision Support , 2018. 5\\n[99] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>,\\n<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. AutoAssign: Differ-\\nentiable label assignment for dense object detection. arXiv\\npreprint arXiv:2007.03496 , 2020. 2, 5\\n[100] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,\\nand <PERSON>. Deformable DETR: Deformable trans-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 14}),\n", " Document(page_content='formers for end-to-end object detection. In Proceedings of\\nthe International Conference on Learning Representations\\n(ICLR) , 2021. 10\\n15', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 14}),\n", " Document(page_content='3 grad.illinois.edu/CareerDevelopment Rachel Green  \\n2 1 0  W .  G R E E N  S T . ,  C H A M P A I G N ,  I L  \\n( 2 1 7 )  5 5 5 - 1 2 3 4  •  R S T U D E N T @ I L L I N O I S . E D U  \\nEDUCATION  \\nPhD in English May 20xx \\nUniversity of Illinois at Urbana-Champaign \\nDissertation title:  “Down on the Farm: World War One and the Emergence of Literary  \\nModernism in the American South”  \\nCommittee : <PERSON>, <PERSON>, <PERSON>, <PERSON> (Chair) \\nMA in English  20xx', metadata={'source': 'pdfs/rachelgreecv.pdf', 'page': 0}),\n", " Document(page_content='University of Illinois at Urbana-Champaign \\nBA in English and Communications, summa cum laude  20xx \\nButler University, Indianapolis, IN  \\nTEACHING  & A DVISING   \\nComposition Instructor  20xx-present \\nResearch Writing Program, University of Illinois \\n\\uf0b7Facilitator for seven sections of English composition.\\n\\uf0b7Planned and taught a writing-intensive course based upon current events.\\n\\uf0b7Used instructional technology to enhance pedagogical technique.', metadata={'source': 'pdfs/rachelgreecv.pdf', 'page': 0}),\n", " Document(page_content='\\uf0b7Taught in part with an innovative, interdisciplinary team-teaching program design.\\nLiterature Instructor 20xx-present \\nDepartment of English, University of Illinois \\n\\uf0b7Instructor of record for two sections of literature, including Major American Authors  and\\nIntroduction to Poetry per semester.\\n\\uf0b7Integrated multimedia and humanities approaches to teaching literature using film and instructional\\ntechnology.\\nCoordinating Group Leader 20xx-20xx \\nResearch Writing Program, University of Illinois', metadata={'source': 'pdfs/rachelgreecv.pdf', 'page': 0}),\n", " Document(page_content='\\uf0b7Planned and led required training session for teaching assistants and new composition teachers.\\n\\uf0b7Helped to mentor new hires to the English Department staff to ensure their engagement and\\nprofessional development.\\n\\uf0b7Provided job shadowing and training opportunities to assist new hires in adjusting to the pace of\\nwork and the tone and style of the University.\\nDiscussion Leader  20xx \\nCarolina Summer Reading Program, University of Illinois', metadata={'source': 'pdfs/rachelgreecv.pdf', 'page': 0}),\n", " Document(page_content='\\uf0b7Led group discussion for first-year students on academic topics.\\nTeaching Assistant 20xx-20xx \\nDepartment of English, University of Illinois at Urbana-Champaign \\n\\uf0b7Taught a section on film criticism, including film history, theory and technical vocabulary.\\n\\uf0b7Planned lessons and assignments, led discussion sections, graded papers and exams.\\n\\uf0b7Organized and led group discussions on social and academic issues.CV SAMPLE', metadata={'source': 'pdfs/rachelgreecv.pdf', 'page': 0}),\n", " Document(page_content='4 grad.illinois.edu/CareerDevelopment RESEARCH  EXPERIENCE   \\nDoctoral Researcher 20xx-20xx \\nDepartment of English, University of Illinois at Urbana-Champaign \\n\\uf0b7Conducted primary source research at numerous archives, examining publication history through\\nmultiple sources.\\n\\uf0b7Examined the literature of <PERSON>, <PERSON>, and <PERSON>, exploring\\ntheir publication records, construction of literary identity, and relationship with modernism.\\nResearch Assistant 20xx', metadata={'source': 'pdfs/rachelgreecv.pdf', 'page': 1}),\n", " Document(page_content='Department of English, University of Illinois at Urbana-Champaign \\n\\uf0b7Assistant to Professor <PERSON>, conducting primary and secondary source research.\\n\\uf0b7Organized for the “New Directions in the Study of Southern Literature: An Interdisciplinary\\nConference.”\\nPUBLICATIONS   \\nAssociate Editor of North Carolina Slave Narratives. <PERSON>, general editor. Forthcoming \\nfrom University of Illinois Press, 20xx.', metadata={'source': 'pdfs/rachelgreecv.pdf', 'page': 1}),\n", " Document(page_content='<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON><PERSON>  “Lost on the Farm: Popular Beliefs” Somebody Journal, Special \\nIssue, Reflections on the Americas. Vol. 6. Accepted and forthcoming. \\nGreen, <PERSON><PERSON> “Fugitives/Agrarians” in A Companion to Twentieth -Century American Poetry. Rutgers \\nPress., 20xx. \\<PERSON><PERSON><PERSON><PERSON>, D.A. and <PERSON>, <PERSON><PERSON>  <PERSON>,” <PERSON><PERSON><PERSON><PERSON>,” and <PERSON><PERSON> in Southern \\nWriters: A Biographical Dictionary. Louisiana State University Press, 20xx. \\nCONFERENCE  PRESENTATIONS', metadata={'source': 'pdfs/rachelgreecv.pdf', 'page': 1}),\n", " Document(page_content='“Artistic Colloquialism,” Illinois Graduate College Seminar, speaker and organizer. Urbana, IL, 20xx.  \\n“Transitional Bible Belt,” US Divergence Symposium, Duke University, NC, February 20xx.  \\n“The Ministry of Rev<PERSON>,” South Atlantic Modern Language Association. Atlanta, GA, \\nMay 20xx. \\n“Shackles and Stripes: The Cinematic Representation of the Southern Chain Gain.” American Literature \\nAssociation. Cambridge, Massachusetts, November 20xx.', metadata={'source': 'pdfs/rachelgreecv.pdf', 'page': 1}),\n", " Document(page_content='“Body Place of Sprits in the South,” Queen Mary College, University of London, April 6 -8, 20xx. \\nHONORS  AND  AWARDS  \\nJacob <PERSON>, U.S. Department of Education 20xx-present \\nGraduate College Dissertation Completion Award, University of Illinois 20xx \\nCampus Teaching Award based on student evaluations, University of Illinois 20xx-20xx \\nDoctoral Fellowship, Illinois Program for Research in the Humanities,  20xx-20xx \\nUniversity of Illinois', metadata={'source': 'pdfs/rachelgreecv.pdf', 'page': 1}),\n", " Document(page_content='Summer Research Grant, Center for Summer Studies, City, ST  20xx \\nGraduate College Conference Travel Grant, University of Illinois 20xx & 20xx \\nMost Outstanding Butler Woman, Butler University, Indianapolis, IN 20xx \\nAcademic Scholarship, Butler University, Indianapolis, IN 20xx-20xx \\nRachel Green, page 2 of 3', metadata={'source': 'pdfs/rachelgreecv.pdf', 'page': 1}),\n", " Document(page_content='5 grad.illinois.edu/CareerDevelopment PROFESSIONAL  SERVICE  \\nManaging Editor 20xx-present \\nSouthern Literary Journal  \\n\\uf0b7Process manuscripts submitted for publication\\n\\uf0b7Oversee production and publication procedures.\\n\\uf0b7Maintain editorial correspondence with prospective contributors.\\n\\uf0b7Conduct business transactions including publicity, subscriptions and advertising.\\nPoetry Staff 20xx-present \\nUniversity Quarterly \\n\\uf0b7Review and solicit poems for possible publication.\\nEditorial Assistant 20xx-20xx', metadata={'source': 'pdfs/rachelgreecv.pdf', 'page': 2}),\n", " Document(page_content='Southern Literary Journal \\n\\uf0b7Designed and maintained journal’s internet presence.\\n\\uf0b7Edited copy for publication on a monthly basis.\\nUNIVERSITY  SERVICE  \\nGraduate Mentor 20xx-20xx \\nThe Career Center, University of Illinois \\n\\uf0b7Counsel minority undergraduates on graduate programs, application procedures and funding.\\nCareer Advisory Committee 20xx-20xx \\nGraduate College, University of Illinois \\n\\uf0b7Served on university committee to evaluate and propose career services for graduate students.', metadata={'source': 'pdfs/rachelgreecv.pdf', 'page': 2}),\n", " Document(page_content='\\uf0b7Collaborated with faculty and students to prepare final report for submission to the Graduate\\nCollege Dean.\\nUniversity Library Advisory Committee 20xx-20xx \\nUndergraduate Library, University of Illinois \\n\\uf0b7Advised University Librarian on needed services and improvements.\\nPROFESSIONAL  MEMBERSHIPS  \\n\\uf0b7Modern Language Association (MLA)\\n\\uf0b7American Literature Association (ALA)\\n\\uf0b7American Studies Association (ASA)\\n\\uf0b7South Atlantic Modern Language Association', metadata={'source': 'pdfs/rachelgreecv.pdf', 'page': 2}),\n", " Document(page_content='(samla)\\uf0b7Society for the Study of Southern Literature\\n\\uf0b7Robert Penn Warren Circle\\n\\uf0b7Southern Research Circle\\n\\uf0b7Fellowship of Southern Writers\\nREFERENCES  \\n<PERSON><PERSON><PERSON> , <PERSON><PERSON><PERSON>. Professor of English \\nUniversity of Illinois at Urbana-Champaign \\n**************, <EMAIL><PERSON><PERSON><PERSON> , Assoc. Professor of English \\nUniversity of Illinois at Urbana-Champaign \\n**************, <EMAIL>\\nR<PERSON><PERSON> , Professor of English \\nUniversity of Illinois at Urbana-Champaign', metadata={'source': 'pdfs/rachelgreecv.pdf', 'page': 2}),\n", " Document(page_content='(*************, <EMAIL><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Professor of English \\nButler University, Indianapolis, IN \\n**************, <EMAIL>\\nR<PERSON><PERSON>, page 3 of 3', metadata={'source': 'pdfs/rachelgreecv.pdf', 'page': 2})]"]}, "metadata": {}, "execution_count": 10}]}, {"cell_type": "code", "source": ["len(text_chunks)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aXnTz5sa6L7d", "outputId": "bd9d375a-d450-4bf4-d469-1fa5ebe01122"}, "execution_count": 11, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["168"]}, "metadata": {}, "execution_count": 11}]}, {"cell_type": "code", "source": ["text_chunks[1]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AGgV2iQz6PI1", "outputId": "87202fa1-e717-4196-b511-e22252cec0a0"}, "execution_count": 12, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Document(page_content='real-time object detectors with 30 FPS or higher on GPU\\nV100. YOLOv7-E6 object detector (56 FPS V100, 55.9%\\nAP) outperforms both transformer-based detector SWIN-\\nL Cascade-Mask R-CNN (9.2 FPS A100, 53.9% AP) by\\n509% in speed and 2% in accuracy, and convolutional-\\nbased detector ConvNeXt-XL Cascade-Mask R-CNN (8.6\\nFPS A100, 55.2% AP) by 551% in speed and 0.7% AP\\nin accuracy, as well as YOLOv7 outperforms: YOL<PERSON>,\\nYOLOX, Scaled-YOLOv4, YOLOv5, DETR, Deformable', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 0})"]}, "metadata": {}, "execution_count": 12}]}, {"cell_type": "code", "source": ["text_chunks[2]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "CoxHsdvH6Ra9", "outputId": "e64fad1d-4958-4d62-da19-4da0b32d877f"}, "execution_count": 13, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Document(page_content='DETR, DINO-5scale-R50, ViT-Adapter-B and many other\\nobject detectors in speed and accuracy. Moreover, we train\\nYOLOv7 only on MS COCO dataset from scratch without\\nusing any other datasets or pre-trained weights. Source\\ncode is released in https://github.com/WongKinYiu/yolov7.\\n1. Introduction\\nReal-time object detection is a very important topic in\\ncomputer vision, as it is often a necessary component in\\ncomputer vision systems. For example, multi-object track-', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 0})"]}, "metadata": {}, "execution_count": 13}]}, {"cell_type": "code", "source": ["text_chunks[3]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "KeyBH-Km6TM9", "outputId": "d0b634fb-dd2e-4a2f-f3c7-17aefb1f0472"}, "execution_count": 14, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Document(page_content='ing [94, 93], autonomous driving [40, 18], robotics [35, 58],\\nmedical image analysis [34, 46], etc. The computing de-\\nvices that execute real-time object detection is usually some\\nmobile CPU or GPU, as well as various neural processing\\nunits (NPU) developed by major manufacturers. For exam-\\nple, the Apple neural engine (Apple), the neural compute\\nstick (Intel), Jetson AI edge devices (Nvidia), the edge TPU\\n(Google), the neural processing engine (Qualcomm), the AI', metadata={'source': 'pdfs/yolov7paper.pdf', 'page': 0})"]}, "metadata": {}, "execution_count": 14}]}, {"cell_type": "markdown", "source": ["## <PERSON><PERSON>od the Embeddings"], "metadata": {"id": "UJzcQCM96ZS-"}}, {"cell_type": "code", "source": ["import os\n", "\n", "os.environ['OPENAI_API_KEY'] = \"***************************************************\""], "metadata": {"id": "G0BmUL5c6XQ2"}, "execution_count": 15, "outputs": []}, {"cell_type": "code", "source": ["embeddings = OpenAIEmbeddings()"], "metadata": {"id": "4zPQYiNn6otd"}, "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["result = embeddings.embed_query(\"How are you!\")"], "metadata": {"id": "Gl4ZrxZm6orf"}, "execution_count": 22, "outputs": []}, {"cell_type": "code", "source": ["len(result)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "O0_dlP6c6yDm", "outputId": "c09ebb03-2457-4a2e-fb6a-ed05e88ba2a9"}, "execution_count": 23, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["1536"]}, "metadata": {}, "execution_count": 23}]}, {"cell_type": "markdown", "source": ["## Initializing the Pinecone"], "metadata": {"id": "nSuEdaLP7dvV"}}, {"cell_type": "code", "source": ["PINECONE_API_KEY = os.environ.get('PINECONE_API_KEY', 're45re12r-et45e-4965-9035-e09c00ad18a5')\n", "PINECONE_API_ENV = os.environ.get('PINECONE_API_ENV', 'gcp-starter')"], "metadata": {"id": "OoCbquCg65Q1"}, "execution_count": 24, "outputs": []}, {"cell_type": "code", "source": ["import pinecone\n", "# initialize pinecone\n", "pinecone.init(\n", "    api_key=PINECONE_API_KEY,  # find at app.pinecone.io\n", "    environment=PINECONE_API_ENV  # next to api key in console\n", ")\n", "index_name = \"test\" # put in the name of your pinecone index here\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ZxX3o89_8F-W", "outputId": "499c6ce5-90ab-44d3-a5c7-01e2340335d0"}, "execution_count": 26, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/pinecone/index.py:4: TqdmExperimentalWarning: Using `tqdm.autonotebook.tqdm` in notebook mode. Use `tqdm.tqdm` instead to force console mode (e.g. in jupyter console)\n", "  from tqdm.autonotebook import tqdm\n"]}]}, {"cell_type": "markdown", "source": ["## Create Embeddings for each of the Text Chunk"], "metadata": {"id": "ZKwc0Cr78pUl"}}, {"cell_type": "code", "source": ["docsearch = Pinecone.from_texts([t.page_content for t in text_chunks], embeddings, index_name=index_name)"], "metadata": {"id": "dmGWBzkA8n72"}, "execution_count": 27, "outputs": []}, {"cell_type": "markdown", "source": ["## If you already have an index, you can load it like this"], "metadata": {"id": "l7ebhRfl9ueQ"}}, {"cell_type": "code", "source": ["docsearch = Pinecone.from_existing_index(index_name, embeddings)\n", "docsearch"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ZuSHyhCM9vOl", "outputId": "337c3bcb-0f8e-49f9-b5da-9ee36f19e118"}, "execution_count": 28, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<langchain.vectorstores.pinecone.Pinecone at 0x78f9e0e67460>"]}, "metadata": {}, "execution_count": 28}]}, {"cell_type": "markdown", "source": ["## Similarity Search"], "metadata": {"id": "PvS-Qi8l919d"}}, {"cell_type": "code", "source": ["query = \"YOLOv7 outperforms which models\""], "metadata": {"id": "BpN4A4uO90yN"}, "execution_count": 30, "outputs": []}, {"cell_type": "code", "source": ["docs = docsearch.similarity_search(query, k=3)"], "metadata": {"id": "i6LnkQgz-CpO"}, "execution_count": 31, "outputs": []}, {"cell_type": "code", "source": ["docs"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "L_qpKI5A-Ob1", "outputId": "6dd23265-d638-410a-b644-581f58fd90e3"}, "execution_count": 32, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='YOLOv7-tiny 6.2 3.5 320 30.8% 47.3% 32.2% 10.0% 31.9% 52.2%\\nimprovement -39% -49% - = = = -0.9 = +0.7\\nYOLOR-E6 [81] 115.8M 683.2G 1280 55.7% 73.2% 60.7% 40.1% 60.4% 69.2%\\nYOLOv7-E6 97.2M 515.2G 1280 55.9% 73.5% 61.1% 40.6% 60.3% 70.0%\\nimprovement -19% -33% - +0.2 +0.3 +0.4 +0.5 -0.1 +0.8\\nYOLOR-D6 [81] 151.7M 935.6G 1280 56.1% 73.9% 61.2% 42.4% 60.5% 69.9%\\nYOLOv7-D6 154.7M 806.8G 1280 56.3% 73.8% 61.4% 41.3% 60.6% 70.1%\\nYOLOv7-E6E 151.7M 843.2G 1280 56.8% 74.4% 62.1% 40.8% 62.1% 70.6%'),\n", " Document(page_content='YOLOv5-L6 (r6.1) [23] 76.8M 445.6G 1280 63 - / 53.7% - -\\nYOLOX-X [21] 99.1M 281.9G 640 58 51.5% / 51.1% - -\\nYOLOv7-E6 97.2M 515.2G 1280 56 56.0% /55.9% 73.5% 61.2%\\nYOLOR-E6 [81] 115.8M 683.2G 1280 45 55.8% / 55.7% 73.4% 61.1%\\nPPYOLOE-X [85] 98.4M 206.6G 640 45 52.2% / 51.9% 69.9% 56.5%\\nYOLOv7-D6 154.7M 806.8G 1280 44 56.6% /56.3% 74.0% 61.8%\\nYOLOv5-X6 (r6.1) [23] 140.7M 839.2G 1280 38 - / 55.0% - -\\nYOLOv7-E6E 151.7M 843.2G 1280 36 56.8% /56.8% 74.4% 62.1%'),\n", " Document(page_content='YOLOv7: Trainable bag-of-freebies sets new state-of-the-art for real-time object\\ndetectors\\nC<PERSON><PERSON>-<PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>\\n1Institute of Information Science, Academia Sinica, Taiwan\\<EMAIL>, <EMAIL>, and <EMAIL>\\nAbstract\\nYOLOv7 surpasses all known object detectors in both\\nspeed and accuracy in the range from 5 FPS to 160 FPS\\nand has the highest accuracy 56.8% AP among all known')]"]}, "metadata": {}, "execution_count": 32}]}, {"cell_type": "markdown", "source": ["## Creating a LLM Model Wrapper"], "metadata": {"id": "s979yBCI-U7g"}}, {"cell_type": "code", "source": ["llm = OpenAI()"], "metadata": {"id": "zDoLPwtX-QLm"}, "execution_count": 33, "outputs": []}, {"cell_type": "code", "source": ["qa = RetrievalQA.from_chain_type(llm=llm, chain_type=\"stuff\", retriever=docsearch.as_retriever())\n"], "metadata": {"id": "f8VAKerY-Wq-"}, "execution_count": 34, "outputs": []}, {"cell_type": "markdown", "source": ["## Q/A"], "metadata": {"id": "omexyHti-de2"}}, {"cell_type": "code", "source": ["query = \"YOLOv7 outperforms which models\""], "metadata": {"id": "DJ2oUzhp-eFe"}, "execution_count": 35, "outputs": []}, {"cell_type": "code", "source": ["qa.run(query)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 53}, "id": "ZP3jsXJK-gR2", "outputId": "b8fecacc-544b-4c63-97cd-c600a33a36e1"}, "execution_count": 36, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["' YOLOv7 outperforms Y<PERSON>Ov5-L6 (r6.1), <PERSON><PERSON><PERSON>-X, <PERSON><PERSON><PERSON>-E6, <PERSON><PERSON><PERSON><PERSON>-X, <PERSON><PERSON><PERSON>v7-D6, <PERSON><PERSON>Ov5-X6 (r6.1), <PERSON><PERSON>Ov7-E6E, YOLOv5-X (r6.1), <PERSON><PERSON><PERSON>-CSP, YOLOR-CSP-X, YOLOv7-tiny-<PERSON>L<PERSON>, YOLOv7, and YOLOv7-X.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 36}]}, {"cell_type": "code", "source": ["query = \"Rachel Green Experience\""], "metadata": {"id": "0GB2q2JL-hvW"}, "execution_count": 37, "outputs": []}, {"cell_type": "code", "source": ["qa.run(query)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 105}, "id": "iG6rokPz-ryf", "outputId": "72168228-5071-4ac3-d3ec-d52a981b3d54"}, "execution_count": 38, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["' <PERSON> has a PhD in English from the University of Illinois at Urbana-Champaign. Her dissertation title was \"Down on the Farm: World War One and the Emergence of Literary Modernism in the American South\". She also holds an MA in English from Butler University, and has received a Summer Research Grant from the Center for Summer Studies, a Graduate College Conference Travel Grant from the University of Illinois, the Most Outstanding Butler Woman award from Butler University, and an Academic Scholarship from Butler University. She has published multiple works, and has presented at conferences.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 38}]}, {"cell_type": "code", "source": ["import sys"], "metadata": {"id": "JzvcwuZB-0T-"}, "execution_count": 39, "outputs": []}, {"cell_type": "code", "source": ["while True:\n", "  user_input = input(f\"Input Prompt: \")\n", "  if user_input == 'exit':\n", "    print('Exiting')\n", "    sys.exit()\n", "  if user_input == '':\n", "    continue\n", "  result = qa({'query': user_input})\n", "  print(f\"Answer: {result['result']}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 283}, "id": "mdnCaS9o-swH", "outputId": "c4d2230c-f6a6-4c14-b85d-3e3e622a8663"}, "execution_count": 40, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Input Prompt: what is yolo v7\n", "Answer:  YOLOv7 is a real-time object detector which surpasses all known object detectors in both speed and accuracy. It has the highest accuracy of 56.8% AP among all known detectors and can run from 5 FPS to 160 FPS.\n", "Input Prompt: tell me about <PERSON><PERSON>\n", "Answer:  <PERSON> is a PhD in English from the University of Illinois at Urbana-Champaign. Her dissertation title was “Down on the Farm: World War One and the Emergence of Literary Modernism in the American South.” She also has a MA in English and was awarded a Summer Research Grant, a Graduate College Conference Travel Grant, Most Outstanding Butler Woman, and an Academic Scholarship. She has published extensively and has given multiple conference presentations.\n", "Input Prompt: exit\n", "Exiting\n"]}, {"output_type": "error", "ename": "SystemExit", "evalue": "ignored", "traceback": ["An exception has occurred, use %tb to see the full traceback.\n", "\u001b[0;31mSystemExit\u001b[0m\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/IPython/core/interactiveshell.py:3561: UserWarning: To exit: use 'exit', 'quit', or Ctrl-D.\n", "  warn(\"To exit: use 'exit', 'quit', or Ctrl-D.\", stacklevel=1)\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "n8_W97tQ-6-W"}, "execution_count": null, "outputs": []}]}