{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "source": ["#**01: Install All the Required Packages**"], "metadata": {"id": "TDpDZghqj2Kz"}}, {"cell_type": "code", "execution_count": 23, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aoyqVPTQjvo1", "outputId": "1c5c8194-0bcf-4f58-ed49-5748a1713785"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: langchain in /usr/local/lib/python3.10/dist-packages (0.3.0)\n", "Requirement already satisfied: langchain_community in /usr/local/lib/python3.10/dist-packages (0.3.0)\n", "Requirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (6.0.2)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.0.34)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (3.10.5)\n", "Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (4.0.3)\n", "Requirement already satisfied: langchain-core<0.4.0,>=0.3.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (0.3.0)\n", "Requirement already satisfied: langchain-text-splitters<0.4.0,>=0.3.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (0.3.0)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.17 in /usr/local/lib/python3.10/dist-packages (from langchain) (0.1.120)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.26.4)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.9.1)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.32.3)\n", "Requirement already satisfied: tenacity!=8.4.0,<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (8.5.0)\n", "Requirement already satisfied: dataclasses-json<0.7,>=0.5.7 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (0.6.7)\n", "Requirement already satisfied: pydantic-settings<3.0.0,>=2.4.0 in /usr/local/lib/python3.10/dist-packages (from langchain_community) (2.5.2)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (2.4.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (24.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (6.1.0)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.11.1)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json<0.7,>=0.5.7->langchain_community) (3.22.0)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json<0.7,>=0.5.7->langchain_community) (0.9.0)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4.0,>=0.3.0->langchain) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4.0,>=0.3.0->langchain) (24.1)\n", "Requirement already satisfied: typing-extensions>=4.7 in /usr/local/lib/python3.10/dist-packages (from langchain-core<0.4.0,>=0.3.0->langchain) (4.12.2)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.17->langchain) (0.27.2)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /usr/local/lib/python3.10/dist-packages (from langsmith<0.2.0,>=0.1.17->langchain) (3.10.7)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.23.3 in /usr/local/lib/python3.10/dist-packages (from pydantic<3.0.0,>=2.7.4->langchain) (2.23.3)\n", "Requirement already satisfied: python-dotenv>=0.21.0 in /usr/local/lib/python3.10/dist-packages (from pydantic-settings<3.0.0,>=2.4.0->langchain_community) (1.0.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2024.8.30)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.1.0)\n", "Requirement already satisfied: anyio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (3.7.1)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (1.0.5)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (1.3.1)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /usr/local/lib/python3.10/dist-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.4.0,>=0.3.0->langchain) (3.0.0)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /usr/local/lib/python3.10/dist-packages (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain_community) (1.0.0)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.2.0,>=0.1.17->langchain) (1.2.2)\n", "Requirement already satisfied: huggingface_hub in /usr/local/lib/python3.10/dist-packages (0.24.6)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (3.16.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (2024.6.1)\n", "Requirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (24.1)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (6.0.2)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (2.32.3)\n", "Requirement already satisfied: tqdm>=4.42.1 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (4.66.5)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.10/dist-packages (from huggingface_hub) (4.12.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface_hub) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface_hub) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface_hub) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface_hub) (2024.8.30)\n", "Requirement already satisfied: transformers in /usr/local/lib/python3.10/dist-packages (4.44.2)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from transformers) (3.16.0)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.23.2 in /usr/local/lib/python3.10/dist-packages (from transformers) (0.24.6)\n", "Requirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.10/dist-packages (from transformers) (1.26.4)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.10/dist-packages (from transformers) (24.1)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from transformers) (6.0.2)\n", "Requirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.10/dist-packages (from transformers) (2024.5.15)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from transformers) (2.32.3)\n", "Requirement already satisfied: safetensors>=0.4.1 in /usr/local/lib/python3.10/dist-packages (from transformers) (0.4.5)\n", "Requirement already satisfied: tokenizers<0.20,>=0.19 in /usr/local/lib/python3.10/dist-packages (from transformers) (0.19.1)\n", "Requirement already satisfied: tqdm>=4.27 in /usr/local/lib/python3.10/dist-packages (from transformers) (4.66.5)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.23.2->transformers) (2024.6.1)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.23.2->transformers) (4.12.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->transformers) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->transformers) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->transformers) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->transformers) (2024.8.30)\n", "Requirement already satisfied: accelerate in /usr/local/lib/python3.10/dist-packages (0.34.2)\n", "Requirement already satisfied: numpy<3.0.0,>=1.17 in /usr/local/lib/python3.10/dist-packages (from accelerate) (1.26.4)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.10/dist-packages (from accelerate) (24.1)\n", "Requirement already satisfied: psutil in /usr/local/lib/python3.10/dist-packages (from accelerate) (5.9.5)\n", "Requirement already satisfied: pyyaml in /usr/local/lib/python3.10/dist-packages (from accelerate) (6.0.2)\n", "Requirement already satisfied: torch>=1.10.0 in /usr/local/lib/python3.10/dist-packages (from accelerate) (2.4.0+cu121)\n", "Requirement already satisfied: huggingface-hub>=0.21.0 in /usr/local/lib/python3.10/dist-packages (from accelerate) (0.24.6)\n", "Requirement already satisfied: safetensors>=0.4.3 in /usr/local/lib/python3.10/dist-packages (from accelerate) (0.4.5)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.21.0->accelerate) (3.16.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.21.0->accelerate) (2024.6.1)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.21.0->accelerate) (2.32.3)\n", "Requirement already satisfied: tqdm>=4.42.1 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.21.0->accelerate) (4.66.5)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.21.0->accelerate) (4.12.2)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (1.13.2)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (3.3)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch>=1.10.0->accelerate) (3.1.4)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch>=1.10.0->accelerate) (2.1.5)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.21.0->accelerate) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.21.0->accelerate) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.21.0->accelerate) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub>=0.21.0->accelerate) (2024.8.30)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.10/dist-packages (from sympy->torch>=1.10.0->accelerate) (1.3.0)\n", "Requirement already satisfied: bitsandbytes in /usr/local/lib/python3.10/dist-packages (0.43.3)\n", "Requirement already satisfied: torch in /usr/local/lib/python3.10/dist-packages (from bitsandbytes) (2.4.0+cu121)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from bitsandbytes) (1.26.4)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (3.16.0)\n", "Requirement already satisfied: typing-extensions>=4.8.0 in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (4.12.2)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (1.13.2)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (3.3)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (3.1.4)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.10/dist-packages (from torch->bitsandbytes) (2024.6.1)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch->bitsandbytes) (2.1.5)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.10/dist-packages (from sympy->torch->bitsandbytes) (1.3.0)\n"]}], "source": ["!pip install langchain langchain_community\n", "#For API Calls\n", "!pip install huggingface_hub\n", "!pip install transformers\n", "!pip install accelerate\n", "!pip install  bitsandbytes"]}, {"cell_type": "markdown", "source": ["#**02: Import All the Required Libraries**"], "metadata": {"id": "pf_D51IAkM-8"}}, {"cell_type": "code", "source": ["from langchain import PromptTemplate, HuggingFaceHub, LLMChain\n", "import os"], "metadata": {"id": "ov9BEk8AkHdf"}, "execution_count": 24, "outputs": []}, {"cell_type": "markdown", "source": ["#**03: Setting the Environment**"], "metadata": {"id": "_rPhI_KCkc5M"}}, {"cell_type": "code", "source": ["os.environ['HUGGINGFACEHUB_API_TOKEN'] = '*************************************'"], "metadata": {"id": "TIfnwcBckb-E"}, "execution_count": 25, "outputs": []}, {"cell_type": "markdown", "source": ["#**04: Approach 1:  Access Models Hosted on Hugging Face Through API**"], "metadata": {"id": "BnN91vC4ktxi"}}, {"cell_type": "markdown", "source": ["#**Text2Text Generation Models | Seq2Seq Models | Encoder-Decoder Models**"], "metadata": {"id": "M4DBMmqkk2tT"}}, {"cell_type": "code", "source": ["prompt = PromptTemplate(\n", "    input_variables=[\"product\"],\n", "    template=\"What is a good name for a company that makes {product}\"\n", ")"], "metadata": {"id": "YX18o-uQkrzW"}, "execution_count": 26, "outputs": []}, {"cell_type": "code", "source": ["chain = LLMChain(llm=HuggingFaceHub(repo_id='google/flan-t5-large', model_kwargs={'temperature':0, 'max_length':64}),prompt = prompt)"], "metadata": {"id": "aJ6MY786lTxF"}, "execution_count": 27, "outputs": []}, {"cell_type": "code", "source": ["chain.run(\"colorful socks\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "lWQMUhjimBVQ", "outputId": "8cb0bd66-99d6-41f7-83c4-97027f866a5f"}, "execution_count": 28, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'sock mania'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 28}]}, {"cell_type": "code", "source": ["prompt = PromptTemplate(\n", "    input_variables=[\"name\"],\n", "    template=\"Can you tell me about famous footballer {name}\"\n", ")"], "metadata": {"id": "4RKXR2atmOBc"}, "execution_count": 29, "outputs": []}, {"cell_type": "code", "source": ["chain = LLMChain(llm=HuggingFaceHub(repo_id='google/flan-t5-large', model_kwargs={'temperature':0, 'max_length':64}),prompt = prompt)"], "metadata": {"id": "M8dO5jPWmj-T"}, "execution_count": 30, "outputs": []}, {"cell_type": "code", "source": ["chain.run(\"<PERSON><PERSON>\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "Lq0sdPsRml4_", "outputId": "93daada7-da77-4f42-cd99-ceca7c23db6c"}, "execution_count": 31, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'<PERSON><PERSON> is a footballer who plays for Argentina.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 31}]}, {"cell_type": "code", "source": ["prompt = PromptTemplate(\n", "    input_variables=[\"cusine\"],\n", "    template=\"Can you tell me food items for a  {cusine} restuarant\"\n", ")"], "metadata": {"id": "otqGB_agmpDG"}, "execution_count": 32, "outputs": []}, {"cell_type": "code", "source": ["chain = LLMChain(llm=HuggingFaceHub(repo_id='google/flan-t5-large', model_kwargs={'temperature':0, 'max_length':64}),prompt = prompt)"], "metadata": {"id": "p7HObZZ4m2Ty"}, "execution_count": 33, "outputs": []}, {"cell_type": "code", "source": ["chain.run(\"indian\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "fMxnWaKnm3Ow", "outputId": "48dee80c-103c-4b6b-dac8-fa60688c665f"}, "execution_count": 35, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Vegetables'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 35}]}, {"cell_type": "markdown", "source": ["#**Apprach 01: Text Generation Models | Decoder Only Models**"], "metadata": {"id": "8LsOv7lXnJ3s"}}, {"cell_type": "code", "source": ["prompt = PromptTemplate(\n", "    input_variables=[\"name\"],\n", "    template=\"Can you tell me about famous footballer {name}\"\n", ")"], "metadata": {"id": "iq9IKY0Tm5ic"}, "execution_count": 36, "outputs": []}, {"cell_type": "code", "source": ["chain = LLMChain(llm=HuggingFaceHub(repo_id='tiiuae/falcon-7b', model_kwargs={'temperature':0.1, 'max_length':64}),prompt = prompt)"], "metadata": {"id": "9uRHMY4WnVyv"}, "execution_count": 37, "outputs": []}, {"cell_type": "code", "source": ["chain.run(\"<PERSON><PERSON>\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 87}, "id": "C0nJMJdOnXWK", "outputId": "57ff1fbc-770e-4071-df0f-2a53cfb26aed"}, "execution_count": 38, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Can you tell me about famous footballer <PERSON><PERSON>?\\<PERSON><PERSON><PERSON><PERSON> is a famous footballer. He plays for the Spanish football club FC Barcelona. He is the captain of the Spanish national football team. He is the best footballer in the world. He is the best player in the world. He is the best player in the world. He is the best player in the world. He is the best player in the world. He is the best player in the world. He is the best player in the world. He is the best player'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 38}]}, {"cell_type": "markdown", "source": ["#**05: Approach 02: Download Model Locally (Create Pipelines)**"], "metadata": {"id": "r096XoCCpjxg"}}, {"cell_type": "markdown", "source": ["#**Import All the Required Libraries**"], "metadata": {"id": "X2RLXGx8puf8"}}, {"cell_type": "code", "source": ["from langchain.llms import HuggingFacePipeline\n", "import torch\n", "from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline, AutoModelForSeq2SeqLM\n"], "metadata": {"id": "9Fnh5cRfnfPX"}, "execution_count": 39, "outputs": []}, {"cell_type": "code", "source": ["model_id = 'google/flan-t5-large'"], "metadata": {"id": "GKDnwbOUqM_r"}, "execution_count": 40, "outputs": []}, {"cell_type": "code", "source": ["tokenizer = AutoTokenizer.from_pretrained(model_id)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "poWSklZcqfXV", "outputId": "51b734f8-15ca-4aa7-df86-d566666b2c81"}, "execution_count": 41, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/transformers/tokenization_utils_base.py:1601: FutureWarning: `clean_up_tokenization_spaces` was not set. It will be set to `True` by default. This behavior will be depracted in transformers v4.45, and will be then set to `False` by default. For more details check this issue: https://github.com/huggingface/transformers/issues/31884\n", "  warnings.warn(\n"]}]}, {"cell_type": "code", "source": ["model = AutoModelForSeq2SeqLM.from_pretrained(model_id, load_in_8bit=True, device_map='auto')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "rdnmlzzZqjpV", "outputId": "fba3e9f5-d92b-4d6b-c6ee-c9fc33e73753"}, "execution_count": 42, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["The `load_in_4bit` and `load_in_8bit` arguments are deprecated and will be removed in the future versions. Please, pass a `BitsAndBytesConfig` object in `quantization_config` argument instead.\n"]}]}, {"cell_type": "code", "source": ["pipeline = pipeline(\"text2text-generation\", model=model, tokenizer=tokenizer, max_length=128)"], "metadata": {"id": "0Fe8pXetqx1b"}, "execution_count": 43, "outputs": []}, {"cell_type": "code", "source": ["local_llm = HuggingFacePipeline(pipeline=pipeline)"], "metadata": {"id": "IXxtAvQ_rMjn"}, "execution_count": 44, "outputs": []}, {"cell_type": "code", "source": ["prompt = PromptTemplate(\n", "    input_variables=[\"product\"],\n", "    template=\"What is a good name for a company that makes {product}\"\n", ")"], "metadata": {"id": "hJCxaxE-rRD_"}, "execution_count": 45, "outputs": []}, {"cell_type": "code", "source": ["chain = LLMChain(llm=local_llm,prompt = prompt)"], "metadata": {"id": "rugggGfZrni9"}, "execution_count": 46, "outputs": []}, {"cell_type": "code", "source": ["chain.run(\"colorful socks\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "qzu67lSJrqQR", "outputId": "b9e84972-e286-4239-9dbb-3571e2913b84"}, "execution_count": 47, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'sock mania'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 47}]}, {"cell_type": "code", "source": ["prompt = PromptTemplate(\n", "    input_variables=[\"name\"],\n", "    template=\"Can you tell me about famous footballer {name}\"\n", ")"], "metadata": {"id": "AK2uMsk-ru-t"}, "execution_count": 48, "outputs": []}, {"cell_type": "code", "source": ["chain = LLMChain(llm=local_llm,prompt = prompt)"], "metadata": {"id": "zlAgPGEWsK6X"}, "execution_count": 49, "outputs": []}, {"cell_type": "code", "source": ["chain.run(\"<PERSON><PERSON>\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "J9esm823sMu9", "outputId": "bce73a2e-a9cf-41f3-91a8-3db8a4c7b0b6"}, "execution_count": 50, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'<PERSON><PERSON> is a footballer who plays for Argentina.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 50}]}]}