{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "source": ["https://openai.com/sitemap.xml"], "metadata": {"id": "63NDakO5x8MZ"}}, {"cell_type": "markdown", "source": ["#**1: Install All the Required Packages**"], "metadata": {"id": "LVzGbRsVYXfS"}}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "0xs5mGElXeRW", "outputId": "6454e2bd-4fe4-40d6-b147-237f725ec557"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[?25l   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/2.3 MB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K   \u001b[91m━━━━━━━━━━━━━━━\u001b[0m\u001b[91m╸\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.9/2.3 MB\u001b[0m \u001b[31m27.4 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.3/2.3 MB\u001b[0m \u001b[31m36.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: openai in /usr/local/lib/python3.10/dist-packages (1.45.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.10/dist-packages (from openai) (3.7.1)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from openai) (1.7.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /usr/local/lib/python3.10/dist-packages (from openai) (0.27.2)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from openai) (0.5.0)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /usr/local/lib/python3.10/dist-packages (from openai) (2.9.1)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from openai) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /usr/local/lib/python3.10/dist-packages (from openai) (4.66.5)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in /usr/local/lib/python3.10/dist-packages (from openai) (4.12.2)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->openai) (3.8)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->openai) (1.2.2)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->openai) (2024.8.30)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->openai) (1.0.5)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.10/dist-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai) (0.14.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1.9.0->openai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.23.3 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1.9.0->openai) (2.23.3)\n", "Requirement already satisfied: tiktoken in /usr/local/lib/python3.10/dist-packages (0.7.0)\n", "Requirement already satisfied: regex>=2022.1.18 in /usr/local/lib/python3.10/dist-packages (from tiktoken) (2024.5.15)\n", "Requirement already satisfied: requests>=2.26.0 in /usr/local/lib/python3.10/dist-packages (from tiktoken) (2.32.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests>=2.26.0->tiktoken) (2024.8.30)\n"]}], "source": ["!pip -q install langchain langchain-community\n", "!pip -q install pypdf\n", "!pip -q install sentence_transformers\n", "!pip install openai\n", "!pip install tiktoken"]}, {"cell_type": "code", "source": ["!pip install tokenizers\n", "!pip install faiss-cpu\n", "!pip -q install unstructured"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2wTAq_eEdYXJ", "outputId": "c174a53d-216d-4849-ef6e-320fbd38c2dd"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: tokenizers in /usr/local/lib/python3.10/dist-packages (0.19.1)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.16.4 in /usr/local/lib/python3.10/dist-packages (from tokenizers) (0.24.6)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers) (3.16.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers) (2024.6.1)\n", "Requirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers) (24.1)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers) (6.0.2)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers) (2.32.3)\n", "Requirement already satisfied: tqdm>=4.42.1 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers) (4.66.5)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers) (4.12.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub<1.0,>=0.16.4->tokenizers) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub<1.0,>=0.16.4->tokenizers) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub<1.0,>=0.16.4->tokenizers) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub<1.0,>=0.16.4->tokenizers) (2024.8.30)\n", "Collecting faiss-cpu\n", "  Downloading faiss_cpu-1.8.0.post1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.7 kB)\n", "Requirement already satisfied: numpy<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from faiss-cpu) (1.26.4)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.10/dist-packages (from faiss-cpu) (24.1)\n", "Downloading faiss_cpu-1.8.0.post1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (27.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m27.0/27.0 MB\u001b[0m \u001b[31m43.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: faiss-cpu\n", "Successfully installed faiss-cpu-1.8.0.post1\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m981.5/981.5 kB\u001b[0m \u001b[31m24.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.1/2.1 MB\u001b[0m \u001b[31m37.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m431.4/431.4 kB\u001b[0m \u001b[31m26.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m274.7/274.7 kB\u001b[0m \u001b[31m17.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.4/3.4 MB\u001b[0m \u001b[31m69.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m45.3/45.3 kB\u001b[0m \u001b[31m3.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m82.7/82.7 kB\u001b[0m \u001b[31m5.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.3/49.3 kB\u001b[0m \u001b[31m3.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m54.5/54.5 kB\u001b[0m \u001b[31m3.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m114.6/114.6 kB\u001b[0m \u001b[31m7.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Building wheel for langdetect (setup.py) ... \u001b[?25l\u001b[?25hdone\n"]}]}, {"cell_type": "code", "source": ["!pip install numpy==1.24.4\n", "!pip install nltk==3.9.1"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_SHyZ27I3Wmz", "outputId": "bd5e8a24-58a6-43e7-8ed4-312c8d48cab7"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: numpy==1.24.4 in /usr/local/lib/python3.10/dist-packages (1.24.4)\n"]}]}, {"cell_type": "markdown", "source": ["#**2: <PERSON><PERSON>rt All the Required Libraries**"], "metadata": {"id": "qmOAmLPKaBxX"}}, {"cell_type": "code", "source": ["import sys\n", "import os\n", "import torch\n", "import textwrap\n", "from langchain.document_loaders import UnstructuredURLLoader\n", "from langchain.text_splitter import CharacterTextSplitter\n", "from langchain.embeddings import OpenAIEmbeddings\n", "from langchain.chat_models import ChatOpenAI\n", "from langchain.vectorstores import FAISS\n", "from langchain.chains import RetrievalQAWithSourcesChain\n", "from langchain.embeddings import HuggingFaceEmbeddings"], "metadata": {"id": "XqJ9GkdXZ0qm", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "8ef80812-e463-4f5e-b715-1557736e9396"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/pydantic/_internal/_fields.py:132: UserWarning: Field \"model_name\" in HuggingFaceInferenceAPIEmbeddings has conflict with protected namespace \"model_\".\n", "\n", "You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.\n", "  warnings.warn(\n"]}]}, {"cell_type": "code", "source": ["import nltk\n", "nltk.download('punkt')\n", "nltk.download('averaged_perceptron_tagger')"], "metadata": {"id": "HaR-PGkn-GlD", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "fc90da59-960b-4575-c366-43fb0523194b"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["[nltk_data] Downloading package punkt to /root/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /root/nltk_data...\n", "[nltk_data]   Package averaged_perceptron_tagger is already up-to-\n", "[nltk_data]       date!\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["True"]}, "metadata": {}, "execution_count": 2}]}, {"cell_type": "markdown", "source": ["#Create an Environment"], "metadata": {"id": "zGm8wNIslUpw"}}, {"cell_type": "code", "source": ["os.environ['OPENAI_API_KEY'] = \"************************************************************************************************************************************\""], "metadata": {"id": "t2YBjadKlXd1"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["#**3: Pass the URLs and extract the data from these URLs**"], "metadata": {"id": "3XtIoO7kayKZ"}}, {"cell_type": "code", "source": ["URLs=[\n", "    'https://blog.gopenai.com/paper-review-llama-2-open-foundation-and-fine-tuned-chat-models-23e539522acb',\n", "    'https://www.mosaicml.com/blog/mpt-7b',\n", "    'https://stability.ai/blog/stability-ai-launches-the-first-of-its-stablelm-suite-of-language-models',\n", "    'https://lmsys.org/blog/2023-03-30-vicuna/'\n", "\n", "]"], "metadata": {"id": "5zz-5MxbavYJ"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["loaders = UnstructuredURLLoader(urls=URLs)\n", "data = loaders.load()"], "metadata": {"id": "FMwp2mEHbjfI"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["data"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ieqoWS2wbv0g", "outputId": "bc26c65b-7b32-4d51-b6ad-41ffb7ecd39b"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(metadata={'source': 'https://blog.gopenai.com/paper-review-llama-2-open-foundation-and-fine-tuned-chat-models-23e539522acb'}, page_content='Open in app\\n\\nSign up\\n\\nSign in\\n\\nWrite\\n\\nSign up\\n\\nSign in\\n\\nPaper Review\\n\\nPaper Review: Llama 2: Open Foundation and Fine-Tuned Chat Models\\n\\nLlama 2: one of the best open source models\\n\\nAndrew <PERSON>\\n\\nFollow\\n\\nPublished in\\n\\nGoPenAI\\n\\n15 min read\\n\\nJul 20, 2023\\n\\n--\\n\\nProject link\\n\\nModel link\\n\\nPaper link\\n\\nThe authors of the work present Llama 2, an assortment of pretrained and fine-tuned large language models (LLMs) with sizes varying from 7 billion to 70 billion parameters. The fine-tuned versions, named Llama 2-Chat, are specifically designed for dialogue applications. These models surpass the performance of existing open-source chat models on most benchmarks, and according to human evaluations for usefulness and safety, they could potentially replace closed-source models. The authors also detail their approach to fine-tuning and safety enhancements for Llama 2-Chat to support the community in further developing and responsibly handling LLMs.\\n\\nPretraining\\n\\nThe authors developed the Llama 2 model family starting from the pretraining methodology of Llama, which utilizes an optimized auto-regressive transformer. They implemented several modifications for improved performance, including enhanced data cleaning, updated data mixes, training on 40% more total tokens, and doubling the context length. They also incorporated grouped-query attention (GQA) to enhance the inference scalability for their larger models.\\n\\nPretraining Data\\n\\nThe authors utilized a novel mix of data from publicly accessible sources to train the Llama 2 models, excluding any data from Meta’s products or services. They made efforts to erase data from certain sites known for harboring large amounts of personal information about private individuals. They trained the models on 2 trillion tokens of data, believing this amount provided a beneficial performance-cost balance. They also up-sampled the most factual sources to boost knowledge and reduce instances of false information generation or “hallucinations”.\\n\\nLlama 2 Pretrained Model Evaluation\\n\\nLlama 2 models significantly outperform their Llama 1 counterparts:\\n\\nThe 70 billion-parameter Llama 2 model notably improves results on the MMLU and BBH benchmarks by roughly 5 and 8 points, respectively, when compared to the 65 billion-parameter Llama 1 model.\\n\\nLlama 2 models with 7 billion and 30 billion parameters outdo MPT models of similar size in all categories except code benchmarks.\\n\\nIn comparison with Falcon models, Llama 2’s 7 billion and 34 billion parameter models outperform the 7 billion and 40 billion parameter Falcon models in all benchmark categories.\\n\\nMoreover, the Llama 2 70B model surpasses all open-source models.\\n\\nComparatively, the Llama 2 70B model performs similarly to the closed-source GPT-3.5 (OpenAI, 2023) on the MMLU and GSM8K benchmarks but shows a significant deficit on coding benchmarks. It matches or exceeds the performance of PaLM (540 billion parameters) on nearly all benchmarks. However, there remains a substantial performance gap between the Llama 2 70B model and both GPT-4 and PaLM-2-L.\\n\\nFine-tuning\\n\\nSupervised Fine-Tuning (SFT)\\n\\nThe authors initiated the Supervised Fine-Tuning (SFT) phase using publicly available instruction tuning data like in Llama. However, they observed that many third-party SFT data sources lacked diversity and quality, particularly for aligning Large Language Models (LLMs) towards dialogue-style instructions. Therefore, they prioritized collecting several thousand high-quality SFT data examples, and found that using fewer but better-quality examples led to notable performance improvements.\\n\\nThe authors discovered that tens of thousands of SFT annotations were enough to achieve high-quality results, and ceased after collecting 27,540 annotations. They highlighted the significant impact of different annotation platforms and vendors on model performance, emphasizing the need for data checks even when sourcing annotations from vendors. A manual examination of a set of 180 examples showed that model outputs were often competitive with those handwritten by human annotators, suggesting the value in shifting more annotation efforts to preference-based annotation for Reinforcement Learning from Human Feedback (RLHF).\\n\\nIn fine-tuning, each sample consisted of a prompt and an answer, concatenated together with a special token used to separate the segments. The authors used an autoregressive objective and zeroed-out the loss on tokens from the user prompt, meaning they only backpropagated on answer tokens.\\n\\nHuman Preference Data Collection\\n\\nThe authors used a binary comparison protocol to collect human preference data for reward modeling in order to maximize the diversity of prompts. Annotators were tasked with writing a prompt and choosing between two model responses based on set criteria. These responses were sampled from two different model variants and varied by temperature hyperparameter. Annotators also had to rate their preference for the chosen response over the alternative (significantly better, better, slightly better, or negligibly better/ unsure.).\\n\\nThe focus of these preference annotations was on “helpfulness” and “safety”. The authors define “helpfulness” as how well Llama 2-Chat responses fulfil users’ requests, and “safety” as whether responses comply with their safety guidelines. Separate guidelines were provided for each focus area.\\n\\nDuring the safety stage, model responses were categorized into three groups: 1) the preferred response is safe and the other is not, 2) both responses are safe, and 3) both responses are unsafe. No examples where the chosen response was unsafe and the other safe were included.\\n\\nHuman annotations were collected in weekly batches. As more preference data was collected, their reward models improved, allowing the authors to train progressively better versions of Llama 2-Chat. As improvements shifted the model’s data distribution, the authors collected new preference data using the latest Llama 2-Chat iterations to keep the reward model up-to-date and accurate.\\n\\nThe authors collected over 1 million binary comparisons, referred to as Meta reward modeling data. Compared to existing open-source datasets, their preference data has more conversation turns and is, on average, longer.\\n\\nReward Modeling\\n\\nThe authors developed a reward model that inputs a model response and corresponding prompt and outputs a score indicating the quality (e.g., helpfulness, safety) of the generated response. These scores can then be used as rewards to optimize the Llama 2-Chat model for better alignment with human preferences.\\n\\nThey trained two separate reward models: one optimized for helpfulness (Helpfulness RM) and another for safety (Safety RM). These models were initialized from pre-trained chat model checkpoints, ensuring knowledge transfer and preventing discrepancies such as favoring hallucinations.\\n\\nTo train the reward model, pairwise human preference data was converted into a binary ranking label format. The model was trained to ensure that the chosen response scored higher than its counterpart. To leverage the four-point preference rating scale, they added a margin component to the loss to help the model assign more distinct scores to responses that have more differences.\\n\\nThe authors combined their new data with existing open-source preference datasets to form a larger training dataset.\\n\\nReward Model Results\\n\\nThe authors evaluated their reward models on a test set held out from each batch of human preference annotation data. They compared their models with publicly available alternatives, including SteamSHP-XL, the Open Assistant reward model, and GPT-4. Their models performed the best, particularly on the corresponding internal test sets.\\n\\nThe authors noted a tension between the goals of helpfulness and safety, and suggested this might be why their separate models performed best on their own domain. A single model that aims to perform well on both aspects would need to differentiate between better responses and distinguish safe prompts from adversarial ones.\\n\\nWhen scoring by preference rating, accuracy was superior for “significantly better” test sets, and it degraded as comparisons became more similar. The authors pointed out that accuracy on more distinct responses is key to improving Llama 2-Chat’s performance.\\n\\nIn terms of scaling trends, the authors found that larger models provided better performance for similar volumes of data, and performance had not plateaued with the current volume of annotated data. The authors concluded that improving the reward model’s accuracy could directly improve Llama 2-Chat’s performance, as the ranking task of the reward is unambiguous.\\n\\nIterative Fine-Tuning\\n\\nTwo main algorithms were used for RLHF fine-tuning: Proximal Policy Optimization (PPO), standard in RLHF literature, and Rejection Sampling fine-tuning, where they selected the best output from sampled model responses for a gradient update. The differences between the two algorithms lie in breadth (K samples for a given prompt in Rejection Sampling) and depth (in PPO the sample is a function of the updated model policy from the previous step, in RS all outputs are sampled. In iterative training the differences are less pronounced).\\n\\nRejection Sampling fine-tuning was performed only with the largest 70B Llama 2-Chat, with smaller models fine-tuned on the sampled data from the larger model. Over iterations, the authors adjusted their strategy to include top-performing samples from all prior iterations, leading to significant performance improvements.\\n\\nThe authors illustrate the benefit of Rejection Sampling in two ways. They show the delta between the maximum and median curves can be interpreted as the potential gain of fine-tuning on the best output. They also found that the optimal temperature for generating diverse samples isn’t constant during iterative model updates.\\n\\nAfter RLHF (V4), the authors sequentially combined Rejection Sampling and PPO fine-tuning. For PPO, they iteratively improved the policy by sampling prompts and generations from the policy and used the PPO algorithm to achieve the objective. They also added a penalty term for diverging from the original policy, as it’s helpful for training stability and to reduce reward hacking.\\n\\nSystem Message for Multi-Turn Consistency\\n\\nThe authors proposed Ghost Attention (GAtt), a technique designed to help AI remember initial instructions throughout a dialogue. This method, which builds on the concept of Context Distillation, introduces an instruction that needs to be followed throughout the conversation and is appended to all user messages in a synthetic dialogue dataset. During training, the instruction is only kept in the first turn and the loss is set to zero for all tokens from prior turns. This strategy was applied to a range of synthetic constraints including hobbies, language, and public figures. The implementation of GAtt helped maintain attention towards initial instructions over a larger part of the dialogue.\\n\\nGAtt managed to ensure consistency even over 20+ turns, until the maximum context length was reached. While this initial implementation has been beneficial, the authors believe there is potential for further enhancement and iteration on this technique.\\n\\nRLHF Results\\n\\nModel-Based Evaluation\\n\\nEvaluating large language models (LLMs) like Llama 2-Chat is a complex problem. While human evaluation is considered the gold standard, it is not always scalable and may present complications. As a solution, the authors first used reward models to measure improvement in iterations of their Reinforcement Learning from Human Feedback (RLHF) model versions, and later confirmed these findings with human evaluations.\\n\\nTo test the reward model’s reliability, the authors collected a test set of prompts and had them judged by human annotators. The results indicated that the reward models were generally well-aligned with human preferences, validating their use as a point-wise metric.\\n\\nHowever, to prevent possible divergence from human preferences, the authors also utilized a more general reward model trained on diverse open-source Reward Modeling datasets. They hypothesize that iterative model updates may help maintain alignment with human preferences.\\n\\nIn a final check to ensure no regression between new and old models, both models were used in the next annotation iteration for comparison.\\n\\nThe authors’ models were shown to outperform ChatGPT in both safety and helpfulness after RLHF-V3. For fair comparison, final results were also assessed using GPT-4. This resulted in Llama 2-Chat still showing a win-rate of over 60% against ChatGPT, although the advantage was less pronounced.\\n\\nHuman Evaluation\\n\\nHuman evaluations are often considered the gold standard for evaluating dialogue models, and the researchers used this method to assess the Llama 2-Chat models’ helpfulness and safety. The models were compared to open-source models like Falcon and MPT MosaicML as well as closed-source models like ChatGPT and PaLM using over 4,000 single and multi-turn prompts.\\n\\nThe results showed that Llama 2-Chat models significantly outperformed open-source models on both single turn and multi-turn prompts, with the Llama 2-Chat 34B model winning over 75% against comparably sized models. The largest Llama 2-Chat model was also competitive with ChatGPT.\\n\\nThree different annotators independently evaluated each model generation comparison to ensure inter-rater reliability (IRR), which was measured using Gwet’s AC1/2 statistic. Depending on the model comparison, the AC2 score varied between 0.37 and 0.55.\\n\\nHowever, the authors acknowledge that human evaluations have certain limitations. For instance, while the 4,000 prompt set is large by research standards, it doesn’t cover all possible real-world usage scenarios. The prompt set lacked diversity and didn’t include any coding- or reasoning-related prompts. Evaluations focused on the final generation of a multi-turn conversation, not the entire conversation experience. Finally, the subjective and noisy nature of human evaluations means results could vary with different prompts or instructions.\\n\\nSafety\\n\\nSafety in Pretraining\\n\\nThe authors discuss the pretraining data used for the Llama 2-Chat model and the steps taken to pretrain it responsibly. They didn’t use any user data and excluded certain sites that contain large amounts of personal information. They also aimed to minimize their carbon footprint and avoided additional filtering that could result in demographic erasure. However, the authors warn the model should be deployed only after significant safety tuning.\\n\\nThe demographic representation in the training data was analyzed, revealing an overrepresentation of “he” pronouns compared to “she” pronouns, which might lead to more frequent usage of “he” in the model’s outputs. The top demographic identity terms related to religion, gender, nationality, race and ethnicity, and sexual orientation all showed a Western skew.\\n\\nThe authors found a small amount of toxicity in the pretraining data, which may affect the output of the model. They also identified English as the dominant language in the training data, suggesting the model might not work as effectively with other languages.\\n\\nThe safety capabilities of Llama 2 were tested using three automatic benchmarks: TruthfulQA for truthfulness, ToxiGen for toxicity, and BOLD for bias. Compared to its predecessor, Llama 2 demonstrated increased truthfulness and decreased toxicity. However, the 13B and 70B versions of Llama 2 exhibited increased toxicity, potentially due to larger pretraining data or different dataset mixes. While the authors noted an increase in positive sentiment for many demographic groups, they emphasized the need for additional safety mitigations before deployment and more comprehensive studies of the model’s real-world impact.\\n\\nSafety Fine-Tuning\\n\\nThe authors discuss fine-tuning approach of a language model called Llama 2-Chat, outlining its techniques, safety categories, annotation guidelines, and methods to mitigate safety risks.\\n\\nSupervised Safety Fine-Tuning: Here, the team starts with adversarial prompts and safe demonstrations, included in the general supervised fine-tuning process. This helps align the model with safety guidelines early on.\\n\\nSafety RLHF (Reinforcement Learning from Human Feedback): This method integrates safety into the general RLHF pipeline, which involves training a safety-specific reward model and gathering more adversarial prompts for better fine-tuning.\\n\\nSafety Context Distillation: In this step, the model is refined by generating safer responses and distilling the safety context into the model. This is done using a targeted approach to choose if context distillation should be used for each sample.\\n\\nSafety categories identified are illicit and criminal activities, hateful and harmful activities, and unqualified advice. To cover different varieties of prompts, they use risk categories and attack vectors, such as psychological manipulation, logic manipulation, syntactic manipulation, semantic manipulation, and others.\\n\\nFor fine-tuning, prompts and demonstrations of safe model responses are gathered and used following the established guidelines. The model’s ability to write nuanced responses improves through RLHF.\\n\\nThe research team also found that adding an additional stage of safety mitigation does not negatively impact model performance on helpfulness. However, with more safety data mixed in model tuning, the model does answer certain questions in a more conservative manner, leading to an increase in the rate of false refusals (where the model refuses to answer legitimate prompts due to irrelevant safety concerns).\\n\\nLastly, context distillation is used to encourage the model to associate adversarial prompts with safer responses, and this context distillation only occurs on adversarial prompts to prevent the degradation of model performance. The safety reward model decides whether to use safety context distillation or not.\\n\\nRed Teaming\\n\\nThe researches discusses the application of red teaming as a proactive method of identifying potential risks and vulnerabilities in Language Learning Models (LLMs). These efforts involve more than 350 professionals from diverse fields such as cybersecurity, election fraud, legal, civil rights, software engineering, machine learning, and creative writing. The red teaming exercises focused on various risk categories, like criminal planning, human trafficking, privacy violations, etc., as well as different attack vectors. Some findings indicated that early models often failed to recognize and handle problematic content appropriately, but iterative improvements helped mitigate these issues.\\n\\nPost-exercise, the data collected was analyzed thoroughly, considering factors like dialogue length, risk area distribution, and the degree of risk. This information was used for model fine-tuning and safety training. The effectiveness of these red teaming exercises was measured using a robustness factor, defined as the average number of prompts that would trigger a violating response from the model per person per hour. For instance, on a 7B model, the robustness improved significantly over several red teaming iterations and model refinements.\\n\\nThe red teaming efforts continue to be a valuable tool in improving model safety and robustness, with new candidate releases consistently reducing the rate of prompts triggering violating responses. As a result, on average, there was a 90% rejection rate model over model.\\n\\nSafety Evaluation of Llama 2-Chat\\n\\nThe authors used human evaluation method to assess the safety of Language Learning Models (LLMs), specifically involving around 2,000 adversarial prompts. The responses to these prompts were assessed by raters on a five-point Likert scale, with 5 being the safest and most helpful, and 1 indicating severe safety violations. A rating of 1 or 2 was considered as a violation.\\n\\nThe violation percentage served as the primary evaluation metric, with mean rating as supplementary. Three annotators assessed each example, with a majority vote determining if a response was violating safety guidelines. Inter-rater reliability (IRR), measured using Gwet’s AC1/2 statistic, indicated a high degree of agreement among annotators. The IRR scores varied depending on the model being evaluated.\\n\\nThe overall violation percentage and safety rating of various LLMs showed that Llama 2-Chat performed comparably or better than others. It is important to note that the evaluations are influenced by factors such as prompt set limitations, review guidelines’ subjectivity, content standards, and individual raters’ subjectivity.\\n\\nThere was a trend observed that multi-turn conversations were more likely to induce unsafe responses across all models. However, Llama 2-Chat still performed well, particularly in multi-turn conversations.\\n\\nIn terms of truthfulness, toxicity, and bias, fine-tuned Llama 2-Chat showed great improvements over the pre-trained model. It showed the lowest level of toxicity among all compared models. Moreover, Llama 2-Chat showed increased positive sentiment for many demographic groups after fine-tuning. In-depth analyses and results of truthfulness and bias were provided in the appendix.\\n\\nDiscussions\\n\\nLearnings and Observations\\n\\nThe findings suggest that reinforcement learning was particularly effective in the tuning process due to its cost and time efficiency. The success of RLHF (Reinforcement Learning from Human Feedback) hinges on the synergistic relationship it creates between humans and LLMs during the annotation process. Notably, RLHF helps overcome the limitations of supervised fine-tuning and can lead to superior writing abilities in LLMs.\\n\\nAn interesting phenomenon related to RLHF was observed — dynamic re-scaling of temperature contingent upon the context. For creative prompts, increased temperature continues to generate diversity across RLHF iterations. However, for factual prompts, despite the rising temperature, the model learns to provide consistent responses.\\n\\nThe Llama 2-Chat model also demonstrated robust temporal organization abilities, which suggests LLMs might have a more advanced concept of time than previously thought.\\n\\nAn intriguing finding is the emergence of tool use in LLMs, which emerged spontaneously in a zero-shot context. Even though tool-use was not explicitly annotated, the model demonstrated the capability to utilize a sequence of tools in a zero-shot context. While promising, LLM tool use can also pose safety concerns and requires further research and testing.\\n\\nPaper Review\\n\\nDeep Learning\\n\\nNLP\\n\\nLarge Language Models\\n\\nOpen Source\\n\\n--\\n\\n--\\n\\nFollow\\n\\nWritten by Andrew Lukyanenko\\n\\n2.5K Followers\\n\\nWriter for\\n\\nGoPenAI\\n\\nEconomist by education. Polyglot as a hobby. DS as a calling. https://andlukyane.com/\\n\\nFollow\\n\\nHelp\\n\\nStatus\\n\\nAbout\\n\\nCareers\\n\\nPress\\n\\nBlog\\n\\nPrivacy\\n\\nTerms\\n\\nText to speech\\n\\nTeams'),\n", " Document(metadata={'source': 'https://www.mosaicml.com/blog/mpt-7b'}, page_content='Introducing MPT-7B, the first entry in our MosaicML Foundation Series. MPT-7B is a transformer trained from scratch on 1T tokens of text and code. It is open source, available for commercial use, and matches the quality of LLaMA-7B. MPT-7B was trained on the MosaicML platform in 9.5 days with zero human intervention at a cost of ~$200k.\\n\\nLarge language models (LLMs) are changing the world, but for those outside well-resourced industry labs, it can be extremely difficult to train and deploy these models. This has led to a flurry of activity centered on open-source LLMs, such as the LLaMA series from Meta, the Pythia series from EleutherAI, the StableLM series from StabilityAI, and the OpenLLaMA model from Berkeley AI Research.\\n\\nToday, we at MosaicML are releasing a new model series called MPT (MosaicML Pretrained Transformer) to address the limitations of the above models and finally provide a commercially-usable, open-source model that matches (and - in many ways - surpasses) LLaMA-7B. Now you can train, finetune, and deploy your own private MPT models, either starting from one of our checkpoints or training from scratch. For inspiration, we are also releasing three finetuned models in addition to the base MPT-7B: MPT-7B-Instruct, MPT-7B-Chat, and MPT-7B-StoryWriter-65k+, the last of which uses a context length of 65k tokens!\\n\\nOur MPT model series is:\\n\\nLicensed for commercial use (unlike LLaMA).\\n\\nTrained on a large amount of data (1T tokens like LLaMA vs. 300B for Pythia, 300B for OpenLLaMA, and 800B for StableLM).\\n\\nPrepared to handle extremely long inputs thanks to ALiBi (we trained on up to 65k inputs and can handle up to 84k vs. 2k-4k for other open source models).\\n\\nOptimized for fast training and inference (via FlashAttention and FasterTransformer)\\n\\nEquipped with highly efficient open-source training code.\\n\\nWe rigorously evaluated MPT on a range of benchmarks, and MPT met the high quality bar set by LLaMA-7B.\\n\\nToday, we are releasing the base MPT model and three other finetuned variants that demonstrate the many ways of building on this base model:\\n\\nMPT-7B Base:\\n\\nMPT-7B Base is a decoder-style transformer with 6.7B parameters. It was trained on 1T tokens of text and code that was curated by MosaicML\\'s data team. This base model includes FlashAttention for fast training and inference and ALiBi for finetuning and extrapolation to long context lengths.\\n\\nLicense: Apache-2.0\\n\\nHuggingFace Link: https://huggingface.co/mosaicml/mpt-7b\\n\\nMPT-7B-StoryWriter-65k+\\n\\nMPT-7B-StoryWriter-65k+ is a model designed to read and write stories with super long context lengths. It was built by finetuning MPT-7B with a context length of 65k tokens on a filtered fiction subset of the books3 dataset. At inference time, thanks to ALiBi, MPT-7B-StoryWriter-65k+ can extrapolate even beyond 65k tokens, and we have demonstrated generations as long as 84k tokens on a single node of A100-80GB GPUs.\\n\\nLicense: Apache-2.0\\n\\nHuggingFace Link: https://huggingface.co/mosaicml/mpt-7b-storywriter\\n\\nMPT-7B-Instruct\\n\\nMPT-7B-Instruct is a model for short-form instruction following. Built by finetuning MPT-7B on a dataset we also release, derived from Databricks Dolly-15k and Anthropic\\'s Helpful and Harmless datasets.\\n\\nLicense: CC-By-SA-3.0\\n\\nHuggingFace Link: https://huggingface.co/mosaicml/mpt-7b-instruct\\n\\nMPT-7B-Chat\\n\\nMPT-7B-Chat is a chatbot-like model for dialogue generation. Built by finetuning MPT-7B on the ShareGPT-Vicuna, HC3, Alpaca, Helpful and Harmless, and Evol-Instruct datasets.\\n\\nLicense: CC-By-NC-SA-4.0 (non-commercial use only)\\n\\nHuggingFace Link: https://huggingface.co/mosaicml/mpt-7b-chat\\n\\nWe hope businesses and the open-source community will build on this effort: alongside the model checkpoints, we have open-sourced the entire codebase for pretraining, finetuning, and evaluating MPT via our new MosaicML LLM Foundry!\\n\\nThis release is more than just a model checkpoint: it\\'s an entire framework for building great LLMs with MosaicML\\'s usual emphasis on efficiency, ease-of-use, and rigorous attention to detail. These models were built by MosaicML\\'s NLP team on the MosaicML platform with the exact same tools our customers use (just ask our customers, like Replit!).\\n\\ne trained MPT-7B with ZERO human intervention from start to finish: over 9.5 days on 440 GPUs, the MosaicML platform detected and addressed 4 hardware failures and resumed the training run automatically, and - due to architecture and optimization improvements we made - there were no catastrophic loss spikes. Check out our empty training logbook for MPT-7B!\\n\\nTraining and Deploying Your Own Custom MPT\\n\\nIf you\\'d like to start building and deploying your own custom MPT models on the MosaicML platform, sign up here to get started.\\n\\nFor more engineering details on data, training, and inference, skip ahead to the section below.\\n\\nFor more information about our four new models, read on!\\n\\nIntroducing the Mosaic Pretrained Transformers (MPT)\\n\\nMPT models are GPT-style decoder-only transformers with several improvements: performance-optimized layer implementations, architecture changes that provide greater training stability, and the elimination of context length limits by replacing positional embeddings with ALiBi. Thanks to these modifications, customers can train MPT models with efficiency (40-60% MFU) without diverging from loss spikes and can serve MPT models with both standard HuggingFace pipelines and FasterTransformer.\\n\\nMPT-7B (Base Model)\\n\\nMPT-7B matches the quality of LLaMA-7B and outperforms other open source 7B - 20B models on standard academic tasks. To evaluate model quality, we compiled 11 open-source benchmarks commonly used for in-context learning (ICL) and formatted and evaluated them in an industry-standard manner. We also added our own self-curated Jeopardy benchmark to evaluate the model\\'s ability to produce factually correct answers to challenging questions.\\n\\nSee Table 1 for a comparison of zero-shot performance between MPT and other models:\\n\\nTo ensure apples-to-apples comparisons, we fully re-evaluated each model: the model checkpoint was run through our open source LLM Foundry eval framework with the same (empty) prompt strings and no model-specific prompt tuning. For full details on the evaluation, see the Appendix. In previous benchmarks, our setup is 8x faster than other eval frameworks on a single GPU and seamlessly achieves linear scaling with multiple GPUs. Built-in support for FSDP makes it possible to evaluate large models and use larger batch sizes for further acceleration.\\n\\nWe invite the community to use our evaluation suite for their own model evaluations and to submit pull requests with additional datasets and ICL task types so we can ensure the most rigorous possible evaluation.\\n\\nMPT-7B-StoryWriter-65k+\\n\\nMost open-source language models can only handle sequences with up to a few thousand tokens (see Figure 1). But with the MosaicML platform and a single node of 8xA100-80GB, you can easily finetune MPT-7B to handle context lengths up to 65k! The ability to handle such extreme context length adaptation comes from ALiBi, one of the key architectural choices in MPT-7B.\\n\\nTo show off this capability and to get you thinking about what you could do with a 65k context window, we are releasing MPT-7B-StoryWriter-65k+. StoryWriter was finetuned from MPT-7B for 2500 steps on 65k-token excerpts of fiction books contained in the books3 corpus. Like pretraining, this finetuning process used a next-token-prediction objective. Once we prepared the data, all that was needed for training was Composer with FSDP, activation checkpointing, and a microbatch size of 1.\\n\\nAs it turns out, the full text of The Great Gatsby weighs in at just under 68k tokens. So, naturally, we had StoryWriter read The Great Gatsby and generate an epilogue. One of the epilogues we generated is in Figure 2. StoryWriter took in The Great Gatsby in about 20 seconds (about 150k words-per-minute). Due to the long sequence length, its \"typing\" speed is slower than our other MPT-7B models, about 105 words-per-minute.\\n\\nEven though StoryWriter was fine-tuned with a 65k context length, ALiBi makes it possible for the model to extrapolate to even longer inputs than it was trained on: 68k tokens in the case of The Great Gatsby, and up to 84k tokens in our testing.\\n\\nThe longest context length of any other open-source model is 4k. GPT-4 has a context length of 8k, and another variant of the model has a context length of 32k.\\n\\nThe epilogue results from providing the entire text of The Great Gatsby (about 68k tokens) as input to the model followed by the word \"Epilogue\" and allowing the model to continue generating from there.\\n\\nMPT-7B-Instruct\\n\\nThe model properly converts content formatted as YAML into the same content formatted as JSON.\\n\\nLLM pretraining teaches the model to continue generating text based on the input it was provided. But in practice, we expect LLMs to treat the input as instructions to follow. Instruction finetuning is the process of training LLMs to perform instruction-following in this way. By reducing the reliance on clever prompt engineering, instruction finetuning makes LLMs more accessible, intuitive, and immediately usable. The progress of instruction finetuning has been driven by open-source datasets like FLAN, Alpaca, and the Dolly-15k dataset.\\n\\nWe created a commercially-usable instruction-following variant of our model called MPT-7B-Instruct. We liked the commercial license of Dolly, but wanted more data, so we augmented Dolly with a subset of Anthropic\\'s Helpful & Harmless dataset, quadrupling the dataset size while maintaining a commercial license.\\n\\nThis new aggregate dataset, released here, was used to finetune MPT-7B, resulting in MPT-7B-Instruct, which is commercially usable. Anecdotally, we find MPT-7B-Instruct to be an effective instruction-follower. (See Figure 3 for an example interaction.) With its extensive training on 1 trillion tokens, MPT-7B-Instruct should be competitive with the larger dolly-v2-12b, whose base model, Pythia-12B, was only trained on 300 billion tokens.\\n\\nWe are releasing the code, weights, and an online demo of MPT-7B-Instruct. We hope that the small size, competitive performance, and commercial license of MPT-7B-Instruct will make it immediately valuable to the community.\\n\\nMPT-7B-Chat\\n\\nA multi-turn conversation with the chat model in which it suggests high-level approaches to solving a problem (using AI to protect endangered wildlife) and then proposes an implementation of one of them in Python using Keras.\\n\\nWe have also developed MPT-7B-Chat, a conversational version of MPT-7B. MPT-7B-Chat has been finetuned using ShareGPT-Vicuna, HC3, Alpaca, Helpful and Harmless, and Evol-Instruct, ensuring that it is well-equipped for a wide array of conversational tasks and applications. It uses the ChatML format, which provides a convenient and standardized way to pass the model system messages and helps prevent malicious prompt injection.\\n\\nWhile MPT-7B-Instruct focuses on delivering a more natural and intuitive interface for instruction-following, MPT-7B-Chat aims to provide seamless, engaging multi-turn interactions for users. (See Figure 4 for an example interaction.)\\n\\nAs with MPT-7B and MPT-7B-Instruct, we are releasing the code, weights, and an online demo for MPT-7B-Chat.\\n\\nHow we built these models on the MosaicML platform\\n\\nThe models released today were built by the MosaicML NLP team, but the tools we used are the exact same ones available to every customer of MosaicML.\\n\\nThink of MPT-7B as a demonstration – our small team was able to build these models in only a few weeks, including the data preparation, training, finetuning, and deployment (and writing this blog!). Let\\'s take a look at the process of building MPT-7B with MosaicML:\\n\\nData\\n\\nWe wanted MPT-7B to be a high-quality standalone model and a useful jumping off point for diverse downstream uses. Accordingly, our pretraining data came from a MosaicML-curated mix of sources, which we summarize in Table 2 and describe in detail in the Appendix. Text was tokenized using the EleutherAI GPT-NeoX-20B tokenizer and the model was pretrained on 1 trillion tokens. This dataset emphasizes English natural language text and diversity for future uses (e.g., code or scientific models), and includes elements of the recently-released RedPajama dataset so that the web crawl and Wikipedia portions of the dataset contain up-to-date information from 2023.\\n\\nA mix of data from ten different open-source text corpora. Text was tokenized using the EleutherAI GPT-NeoX-20B tokenizer, and the model was pre-trained on 1T tokens sampled according to this mix.\\n\\nTokenizer\\n\\nWe used EleutherAI\\'s GPT-NeoX 20B tokenizer. This BPE tokenizer has a number of desirable characteristics, most of which are relevant for tokenizing code:\\n\\nTrained on a diverse mix of data that includes code (The Pile)\\n\\nApplies consistent space delimitation, unlike the GPT2 tokenizer which tokenizes inconsistently depending on the presence of prefix spaces\\n\\nContains tokens for repeated space characters, which allows superior compression of text with large amounts of repeated space characters.\\n\\nThe tokenizer has a vocabulary size of 50257, but we set the model vocabulary size to 50432. The reasons for this were twofold: First, to make it a multiple of 128 (as in Shoeybi et al.), which we found improved MFU by up to four percentage points in initial experiments. Second, to leave tokens available that can be used in subsequent UL2 training.\\n\\nEfficient Data Streaming\\n\\nWe leveraged MosaicML\\'s StreamingDataset to host our data in a standard cloud object store and efficiently stream it to our compute cluster during training. StreamingDataset provides a number of advantages:\\n\\nObviates the need to download the whole dataset before starting training.\\n\\nAllows instant resumption of training from any point in the dataset. A paused run can be resumed without fast-forwarding the dataloader from the start.\\n\\nIs fully deterministic. Samples are read in the same order regardless of the number of GPUs, nodes, or CPU workers.\\n\\nAllows arbitrary mixing of data sources in: simply enumerate the your data sources and desired proportions of the total training data, and StreamingDataset handles the rest. This made it extremely easy to run preparatory experiments on different data mixes.\\n\\nCheck out the StreamingDataset blog for more details!\\n\\nTraining Compute\\n\\nAll MPT-7B models were trained on the MosaicML platform with the following tools:\\n\\nCompute: A100-40GB and A100-80GB GPUs from Oracle Cloud\\n\\nOrchestration and Fault Tolerance: MCLI and MosaicML platform\\n\\nData: OCI Object Storage and StreamingDataset\\n\\nTraining software: Composer, PyTorch FSDP, and LLM Foundry\\n\\nAs shown in Table 3, nearly all of the training budget was spent on the base MPT-7B model, which took ~9.5 days to train on 440xA100-40GB GPUs, and cost ~$200k. The finetuned models took much less compute and were much cheaper – ranging between a few hundred and few thousand dollars each.\\n\\nTime to Train\\' is the total runtime from job start to finish, including checkpointing, periodic evaluation, restarts, etc. \\'Cost\\' is computed with pricing of $2/A100-40GB/hr and $2.50/A100-80GB/hr for reserved GPUs on the MosaicML platform.\\n\\nEach of these training recipes can be fully customized. For example, if you\\'d like to start from our open source MPT-7B and finetune it on proprietary data with a long context length, you can do that today on the MosaicML platform.\\n\\nAs another example, to train a new model from scratch on a custom domain (e.g. on biomedical text or code), simply reserve short-term large blocks of compute with MosaicML\\'s hero cluster offering. Just pick the desired model size and token budget, upload your data to an object store like S3, and launch an MCLI job. You will have your very own custom LLM in just days!\\n\\nCheck out our earlier LLM blog post for guidance on the times and costs to train different LLMs. Find the latest throughput data for specific model configurations here. In line with our previous work, all MPT-7B models were trained with Pytorch FullyShardedDataParallelism (FSDP) and without tensor- or pipeline- parallelism.\\n\\nTraining Stability\\n\\nAs many teams have documented, training LLMs with billions of parameters on hundreds-to-thousands of GPUs is incredibly challenging. Hardware will fail frequently and in creative and unexpected ways. Loss spikes will derail training. Teams must \"babysit\" the training run 24/7 in case of failures and apply manual interventions when things go wrong. Check out the OPT logbook for a candid example of the many perils awaiting anyone training an LLM.\\n\\nAt MosaicML, our research and engineering teams have worked tirelessly over the last 6 months to eliminate these issues. As a result, our MPT-7B training logbook (Figure 5) is very boring! We trained MPT-7B on 1 trillion tokens from start to finish with no human intervention. No loss spikes, no mid-stream learning rate changes, no data skipping, automatic handling of dead GPUs, etc.\\n\\nMPT-7B was trained on 1T tokens over the course of 9.5 days on 440xA100-40GB. During that time the training job encountered 4 hardware failures, all of which were detected by the MosaicML platform. The run was automatically paused and resumed upon each failure, and no human intervention was required.\\n\\nIf hardware failures occur while a job is running, the MosaicML platform automatically detects the failure, pauses the job, cordons any broken nodes, and resumes the job. During the MPT-7B training run, we encountered 4 such failures, and each time the job was automatically resumed\\n\\nHow did we do this? First, we addressed convergence stability with architecture and optimization improvements. Our MPT models use ALiBi rather than positional embeddings, which we found to improve resilience to loss spikes. We also train our MPT models with the Lion optimizer rather than AdamW, which provides stable update magnitudes and cuts optimizer state memory in half.\\n\\nSecond, we used the MosaicML platform\\'s NodeDoctor feature to monitor for and resolve hardware failures and the JobMonitor feature to resume runs after these failures were resolved. These features enabled us to train MPT-7B with no human intervention from start to finish despite 4 hardware failures during the run. See Figure 6 for a closeup view of what autoresumption looks like on the MosaicML platform.\\n\\nInference\\n\\nMPT is designed to be fast, easy, and cheap to deploy for inference. To begin with, all MPT models are subclassed from the HuggingFace PretrainedModel base class, which means that they are fully compatible with the HuggingFace ecosystem. You can upload MPT models to the HuggingFace Hub, generate outputs with standard pipelines like `model.generate(...)`, build HuggingFace Spaces (see some of ours here!), and more.\\n\\nWhat about performance? With MPT\\'s optimized layers (including FlashAttention and low precision layernorm), the out-of-the-box performance of MPT-7B when using `model.generate(...)` is 1.5x-2x faster than other 7B models like LLaMa-7B. This makes it easy to build fast and flexible inference pipelines with just HuggingFace and PyTorch.\\n\\nBut what if you really need the best performance? In that case, directly port MPT weights to FasterTransformer or ONNX. Check out the LLM Foundry\\'s inference folder for scripts and instructions.\\n\\nFinally, for the best hosting experience, deploy your MPT models directly on MosaicML\\'s Inference service. Start with our managed endpoints for models like MPT-7B-Instruct, and/or deploy your own custom model endpoints for optimal cost and data privacy.\\n\\nWhat\\'s Next?\\n\\nThis MPT-7B release is the culmination of two years of work at MosaicML building and battle-testing open-source software (Composer, StreamingDataset, LLM Foundry) and proprietary infrastructure (MosaicML Training and Inference) that makes it possible for customers to train LLMs on any compute provider, with any data source, with efficiency, privacy and cost transparency - and to have things go right the first time.\\n\\nWe believe MPT, the MosaicML LLM Foundry, and the MosaicML platform are the best starting point for building custom LLMs for private, commercial, and community use, whether you want to finetune our checkpoints or train your own from scratch. We look forward to seeing how the community builds on these tools and artifacts.\\n\\nImportantly, today\\'s MPT-7B models are just the beginning! To help our customers address more challenging tasks and continually improve their products, MosaicML will continue to produce foundation models of higher and higher quality. Exciting follow-on models are already training. Expect to hear more about them soon!\\n\\nAcknowledgements\\n\\nWe are grateful to our friends at AI2 for helping us to curate our pretraining dataset, choose a great tokenizer, and for many other helpful conversations along the way ⚔️\\n\\nAppendix\\n\\nData\\n\\nmC4\\n\\nMultilingual C4 (mC4) 3.1.0 is an update of the original mC4 by Chung et al., which contains sources through August 2022. We selected the English subset, and then applied the following filtering criteria to each document:\\n\\nThe most common character must be alphabetic.\\n\\n≥ 92% of characters must be alphanumeric.\\n\\nIf the document is > 500 words, the most common word cannot constitute > 7.5% of the total word count; If the document is ≤ 500 words, the most common word cannot constitute > 30% of the total word count.\\n\\nThe document must be ≥ 200 words and ≤ 50000 words.\\n\\nThe first three filtering criteria were used to improve sample quality, and the final filtering criterion (documents must be ≥200 words and ≤50000 words) was used to increase the mean sequence length of the pretraining data.\\n\\nmC4 was released as part of the continued effort from Dodge et al..\\n\\nC4\\n\\nColossal Cleaned Common Crawl (C4) is an English Common Crawl corpus introduced by Raffel et al.. We applied Abbas et al.\\'s Semantic Deduplication process to remove the 20% most similar documents within C4, as internal experiments showed that this is a Pareto improvement for models trained on C4.\\n\\nRedPajama\\n\\nWe included a number of subsets of the RedPajama dataset, which is Together\\'s attempt to replicate LLaMA\\'s training data. Specifically, we used the CommonCrawl, arXiv, Wikipedia, Books, and StackExchange subsets.\\n\\nThe Stack\\n\\nWe wanted our model to be capable of code generation, so we turned to The Stack, a 6.4TB corpus of code data. We used The Stack Dedup, a variant of the stack that has been approximately deduplicated (via MinHashLSH) to 2.9TB. We selected a subset of 18 of The Stack\\'s 358 programming languages in order to reduce dataset size and increase relevance:\\n\\nC\\n\\nC-Sharp\\n\\nC++\\n\\nCommon Lisp\\n\\nF-Sharp\\n\\nFortran\\n\\nGo\\n\\nHaskell\\n\\nJava\\n\\nOcaml\\n\\nPerl\\n\\nPython\\n\\nRuby\\n\\nRust\\n\\nScala\\n\\nScheme\\n\\nShell\\n\\nTex\\n\\nWe chose to have code constitute 10% of the pretraining tokens, as internal experiments showed that we could train on up to 20% code (and 80% natural language) with no negative impact on natural language evaluation.\\n\\nWe also extracted the Markdown component of The Stack Dedup and treated this as an independent pretraining data subset (i.e. not counted towards the 10% code tokens). Our motivation for this is that markup language documents are predominantly natural language, and as such should count towards our natural language token budget.\\n\\nSemantic Scholar ORC\\n\\nThe Semantic Scholar Open Research Corpus (S2ORC) is a corpus of English-language academic papers, which we consider to be a high-quality data source. The following quality filtering criteria were applied:\\n\\nThe paper is open access.\\n\\nThe paper has a title and abstract.\\n\\nThe paper is in English (as assessed using cld3).\\n\\nThe paper has at least 500 words and 5 paragraphs.\\n\\nThe paper was published after 1970 and before 2022-12-01.\\n\\nThe most frequent word in the paper consists of alpha characters only, and it appears in less than 7.5% of the document.\\n\\nThis yielded 9.9M papers. Instructions to obtain the latest dataset version are available here, and the original publication is here. The filtered version of the dataset was kindly provided to us by AI2.\\n\\nEvaluation Tasks\\n\\nLambada: 5153 samples of text curated from the books corpus. Consists of a several hundred word paragraph in which the model is expected to predict the next word.\\n\\nPIQA: 1838 samples of physical intuitive binary multiple choice questions, e.g. \"Question: How can I easily carry clothes on hangers when I move?\", \"Answer: \"Take a couple of empty heavy duty clothes hangers, then hook several hangers of clothes on Those hangers and carry them all at once.\"\\n\\nCOPA: 100 sentences of the form XYZ therefore/because TUV. Framed as binary multiple choice questions where the model has a choice of two possible ways to follow the therefore/because. e.g. {\"query\": \"The woman was in a bad mood, therefore\", \"gold\": 1, \"choices\": [\"she engaged in small talk with her friend.\", \"she told her friend to leave her alone.\"]}\\n\\nBoolQ: 3270 yes/no questions based on some passage which contains relevant information. Question topics range from pop culture to science, law, history, etc. e.g. {\"query\": \"Passage: Kermit the Frog is a Muppet character and Jim Henson\\'s most well-known creation. Introduced in 1955, Kermit serves as the straight man protagonist of numerous Muppet productions, most notably Sesame Street and The Muppet Show, as well as in other television series, films, specials, and public service announcements through the years. Henson originally performed Kermit until his death in 1990; Steve Whitmire performed Kermit from that time up until his dismissal from the role in 2016. Kermit is currently performed by Matt Vogel. He was also voiced by Frank Welker in Muppet Babies and occasionally in other animation projects, and is voiced by Matt Danner in the 2018 reboot of Muppet Babies.\\\\nQuestion: has kermit the frog been on sesame street?\\\\n\", \"choices\": [\"no\", \"yes\"], \"gold\": 1}\\n\\nArc-Challenge: 1172 challenging four-choice multiple choice questions about science\\n\\nArc-Easy: 2376 easy four choice multiple choice science questions\\n\\nHellaSwag: 10042 four choice multiple choice questions in which a real life scenario is presented and the model must choose the most likely conclusion to the scenario.\\n\\nJeopardy: 2117 Jeopardy questions from five categories: science, world history, us history, word origins, and literature. The model must provide the exact correct answer\\n\\nMMLU: 14,042 multiple choice questions from 57 diverse academic categories\\n\\nTriviaQA: 11313 free response pop culture trivia questions\\n\\nWinograd: 273 schema questions where the model must resolve which referent of a pronoun is most likely.\\n\\nWinogrande: 1,267 schema questions where the model must resolve which ambiguous sentence is more logically likely (both versions of the sentence are syntactically valid)\\n\\nMPT Hugging Face Spaces Privacy Policy\\n\\nPlease see our MPT Hugging Face Spaces Privacy Policy.\\n\\nRelated posts\\n\\nJune 22, 2023\\n\\nMPT-30B: Raising the bar for open-source foundation models\\n\\nJuly 18, 2023\\n\\nAnnouncing MPT-7B-8K: 8K Context Length for Document Understanding\\n\\nMarch 9, 2023\\n\\nMosaicBERT: Pretraining BERT from Scratch for $20'),\n", " Document(metadata={'source': 'https://stability.ai/blog/stability-ai-launches-the-first-of-its-stablelm-suite-of-language-models'}, page_content='Stability AI Launches the First of its Stable LM Suite of Language Models\\n\\nProduct\\n\\n19 Apr\\n\\nWritten By Guest User\\n\\nToday, Stability AI released a new open source language model, Stable LM. The Alpha version of the model is available in 3 billion and 7 billion parameters, with 15 billion to 65 billion parameter models to follow. Developers can freely inspect, use, and adapt our Stable LM base models for commercial or research purposes, subject to the terms of the CC BY-SA-4.0 license.\\n\\nIn 2022, Stability AI drove the public release of Stable Diffusion, a revolutionary image model representing a transparent, open, and scalable alternative to proprietary AI. With the launch of the Stable LM suite of models, Stability AI is continuing to make foundational AI technology accessible to all. Our Stable LM models can generate text and code and will power various downstream applications. They demonstrate how small and efficient models can deliver high performance with appropriate training.\\n\\nThe release of Stable LM builds on our experience in open sourcing earlier language models with EleutherAI, a nonprofit research hub. These language models include GPT-J, GPT-NeoX, and the Pythia suite, trained on The Pile open source dataset. Many recent open source language models continue to build on these efforts, including Cerebras-GPT and Dolly-2.\\n\\nStable LM is trained on a new experimental dataset built on The Pile, but three times larger with 1.5 trillion content tokens. We will release details on the dataset in due course. The richness of this dataset gives Stable LM surprisingly high performance in conversational and coding tasks despite its small size of 3 to 7 billion parameters (by comparison, GPT-3 has 175 billion parameters).\\n\\nWe are also releasing a set of research models that are fine-tuned instructions. Initially, these fine-tuned models will use a combination of five recent open source datasets for conversational agents: Alpaca, GPT4All, Dolly, ShareGPT, and HH. These fine-tuned models are intended for research use only and are released under a noncommercial CC BY-NC-SA 4.0 license, in line with Stanford’s Alpaca license.\\n\\nCheck out some examples below, produced by our 7 billion parameters fine-tuned model:\\n\\nView fullsize\\n\\nView fullsize\\n\\nView fullsize\\n\\nLanguage models will form the backbone of our digital economy, and we want everyone to have a voice in their design. Models like Stable LM demonstrate our commitment to AI technology that is transparent, accessible, and supportive:\\n\\nTransparent. We open source our models to promote transparency and foster trust. Researchers can “look under the hood” to verify performance, work on interpretability techniques, identify potential risks, and help develop safeguards. Organizations across the public and private sectors can adapt (“fine-tune”) these open source models for their own applications without sharing their sensitive data or giving up control of their AI capabilities.\\n\\nAccessible. We design for the edge so everyday users can run our models on local devices. Using these models, developers can build independent applications compatible with widely available hardware instead of relying on proprietary services from one or two companies. In this way, the economic benefits of AI are shared by a broad community of users and developers. Open, fine-grained access to our models allows the broad research and academic community to develop interpretability and safety techniques beyond what is possible with closed models.\\n\\nSupportive. We build models to support our users, not replace them. We are focused on efficient, specialized, and practical AI performance – not a quest for god-like intelligence. We develop tools that help everyday people and everyday firms use AI to unlock creativity, boost their productivity, and open up new economic opportunities.\\n\\nThe models are now available in our GitHub repository. We will publish a full technical report in the near future and look forward to ongoing collaboration with developers and researchers as we roll out the Stable LM suite. In addition, we will be kicking off our crowd-sourced RLHF program and working with community efforts such as Open Assistant to create an open source dataset for AI assistants.\\n\\nWe will be releasing more models soon and are growing our team. If you are passionate about democratizing access to this technology and experienced in LLMs, please apply here!\\n\\nGuest User\\n\\nPrevious\\n\\nPrevious\\n\\nStability AI releases its Image Upscaling API\\n\\nNext\\n\\nNext\\n\\nStability AI Partners with Iconic Artist Peter Gabriel to Launch Series of AI Animation Challenges titled #DiffuseTogether'),\n", " Document(metadata={'source': 'https://lmsys.org/blog/2023-03-30-vicuna/'}, page_content='LMSYS ORG\\n\\nProjectsBlogAboutDonationsChatbot Arena\\n\\nVicuna: An Open-Source Chatbot Impressing GPT-4 with 90%* ChatGPT Quality\\n\\nby: The Vicuna Team, Mar 30, 2023\\n\\nWe introduce Vicuna-13B, an open-source chatbot trained by fine-tuning LLaMA on user-shared conversations collected from ShareGPT. Preliminary evaluation using GPT-4 as a judge shows Vicuna-13B achieves more than 90%* quality of OpenAI ChatGPT and Google Bard while outperforming other models like LLaMA and Stanford Alpaca in more than 90%* of cases. The cost of training Vicuna-13B is around $300. The code and weights, along with an online demo, are publicly available for non-commercial use.\\n\\nVicuna (generated by stable diffusion 2.1)\\n\\nAccording to a fun and non-scientific evaluation with GPT-4. Further rigorous evaluation is needed.\\n\\nHow Good is Vicuna?\\n\\nAfter fine-tuning Vicuna with 70K user-shared ChatGPT conversations, we discover that Vicuna becomes capable of generating more detailed and well-structured answers compared to Alpaca (see examples below), with the quality on par with ChatGPT.\\n\\nHowever, evaluating chatbots is never a simple task. With recent advancements in GPT-4, we are curious whether its capabilities have reached a human-like level that could enable an automated evaluation framework for benchmark generation and performance assessments. Our initial finding indicates that GPT-4 can produce highly consistent ranks and detailed assessment when comparing chatbots’ answers (see above example of GPT-4 judgment). Preliminary evaluations based on GPT-4, summarized in Figure 1, show that Vicuna achieves 90%* capability of Bard/ChatGPT. While this proposed framework shows a potential to automate chatbot assessment, it is not yet a rigorous approach. Building an evaluation system for chatbots remains an open question requiring further research. More details are provided in the evaluation section.\\n\\nFigure 1. Relative Response Quality Assessed by GPT-4*\\n\\nOnline Demo\\n\\nTry the Vicuna-13B demo here!\\n\\nOverview\\n\\nThe rapid advancement of large language models (LLMs) has revolutionized chatbot systems, resulting in unprecedented levels of intelligence as seen in OpenAI\\'s ChatGPT. However, despite its impressive performance, the training and architecture details of ChatGPT remain unclear, hindering research and open-source innovation in this field. Inspired by the Meta LLaMA and Stanford Alpaca project, we introduce Vicuna-13B, an open-source chatbot backed by an enhanced dataset and an easy-to-use, scalable infrastructure. By fine-tuning a LLaMA base model on user-shared conversations collected from ShareGPT.com, Vicuna-13B has demonstrated competitive performance compared to other open-source models like Stanford Alpaca. This blog post provides a preliminary evaluation of Vicuna-13B\\'s performance and describes its training and serving infrastructure. We also invite the community to interact with our online demo to test the capabilities of this chatbot.\\n\\nFigure 2. Workflow Overview\\n\\nFigure 2 provides an overview of our work. To begin, we collected around 70K conversations from ShareGPT.com, a website where users can share their ChatGPT conversations. Next, we enhanced the training scripts provided by Alpaca to better handle multi-turn conversations and long sequences. The training was done with PyTorch FSDP on 8 A100 GPUs in one day. For serving the demo, we implemented a lightweight distributed serving system. We conducted a preliminary evaluation of the model quality by creating a set of 80 diverse questions and utilizing GPT-4 to judge the model outputs. To compare two different models, we combine the outputs from each model into a single prompt for each question. The prompts are then sent to GPT-4, which assesses which model provides better responses. A detailed comparison of LLaMA, Alpaca, ChatGPT, and Vicuna is shown in Table 1 below.\\n\\nTable 1. Comparison between several notable models\\n\\nModel Name LLaMA Alpaca Vicuna Bard/ChatGPT Dataset Publicly available datasets (1T token) Self-instruct from davinci-003 API (52K samples) User-shared conversations (70K samples) N/A Training code N/A Available Available N/A Evaluation metrics Academic benchmark Author evaluation GPT-4 assessment Mixed Training cost (7B) 82K GPU-hours $500 (data) + $100 (training) $140 (training) N/A Training cost (13B) 135K GPU-hours N/A $300 (training) N/A\\n\\nTraining\\n\\nVicuna is created by fine-tuning a LLaMA base model using approximately 70K user-shared conversations gathered from ShareGPT.com with public APIs. To ensure data quality, we convert the HTML back to markdown and filter out some inappropriate or low-quality samples. Additionally, we divide lengthy conversations into smaller segments that fit the model\\'s maximum context length.\\n\\nOur training recipe builds on top of Stanford’s alpaca with the following improvements.\\n\\nMulti-turn conversations: We adjust the training loss to account for multi-turn conversations and compute the fine-tuning loss solely on the chatbot\\'s output.\\n\\nMemory Optimizations: To enable Vicuna\\'s understanding of long context, we expand the max context length from 512 in alpaca to 2048, which substantially increases GPU memory requirements. We tackle the memory pressure by utilizing gradient checkpointing and flash attention.\\n\\nCost Reduction via Spot Instance: The 40x larger dataset and 4x sequence length for training poses a considerable challenge in training expenses. We employ SkyPilot managed spot to reduce the cost by leveraging the cheaper spot instances with auto-recovery for preemptions and auto zone switch. This solution slashes costs for training the 7B model from $500 to around $140 and the 13B model from around $1K to $300.\\n\\nServing\\n\\nWe build a serving system that is capable of serving multiple models with distributed workers. It supports flexible plug-in of GPU workers from both on-premise clusters and the cloud. By utilizing a fault-tolerant controller and managed spot feature in SkyPilot, this serving system can work well with cheaper spot instances from multiple clouds to reduce the serving costs. It is currently a lightweight implementation and we are working on integrating more of our latest research into it.\\n\\nHow To Evaluate a Chatbot?\\n\\nEvaluating AI chatbots is a challenging task, as it requires examining language understanding, reasoning, and context awareness. With AI chatbots becoming more advanced, current open benchmarks may no longer suffice. For instance, the evaluation dataset used in Stanford’s Alpaca, self-instruct, can be effectively answered by SOTA chatbots, making it difficult for humans to discern differences in performance. More limitations include training/test data contamination and the potentially high cost of creating new benchmarks. To tackle these issues, we propose an evaluation framework based on GPT-4 to automate chatbot performance assessment.\\n\\nFirst, we devised eight question categories, such as Fermi problems, roleplay scenarios, and coding/math tasks, to test various aspects of a chatbot\\'s performance. Through careful prompt engineering, GPT-4 is able to generate diverse, challenging questions that baseline models struggle with. We select ten questions per category and collect answers from five chatbots: LLaMA, Alpaca, ChatGPT, Bard, and Vicuna. We then ask GPT-4 to rate the quality of their answers based on helpfulness, relevance, accuracy, and detail. We discover that GPT-4 can produce not only relatively consistent scores but also detailed explanations on why such scores are given (detailed examples link). However, we also notice that GPT-4 is not very good at judging coding/math tasks.\\n\\nFigure 3. Response Comparison Assessed by GPT-4\\n\\nFigure 3 displays the comparison results between all baselines and Vicuna. GPT-4 prefers Vicuna over state-of-the-art open-source models (LLaMA, Alpaca) in more than 90% of the questions, and it achieves competitive performance against proprietary models (ChatGPT, Bard). In 45% of the questions, GPT-4 rates Vicuna\\'s response as better or equal to ChatGPT\\'s. As GPT-4 assigns a quantitative score to each response on a scale of 10, we calculate the total score for each (baseline, Vicuna) comparison pair by adding up the scores obtained by each model on 80 questions. As shown in Table 2, Vicuna’s total score is 92% of ChatGPT’s. Despite recent advancements, these chatbots still face limitations, such as struggling with basic math problems or having limited coding ability.\\n\\nTable 2. Total Scores Assessed by GPT-4.\\n\\nBaseline Baseline Score Vicuna Score LLaMA-13B 513.0 694.0 Alpaca-13B 583.0 704.0 Bard 664.0 655.5 ChatGPT 693.0 638.0\\n\\nWhile this proposed evaluation framework demonstrates the potential for assessing chatbots, it is not yet a rigorous or mature approach, as large language models are prone to hallucinate. Developing a comprehensive, standardized evaluation system for chatbots remains an open question requiring further research.\\n\\nEdited: After this blog post, we conducted a deeper study on this GPT4-based evaluation approach. You are welcome to read our new Judging LLM-as-a-judge paper and try the new evaluation tool.\\n\\nLimitations\\n\\nWe have noticed that, similar to other large language models, Vicuna has certain limitations. For instance, it is not good at tasks involving reasoning or mathematics, and it may have limitations in accurately identifying itself or ensuring the factual accuracy of its outputs. Additionally, it has not been sufficiently optimized to guarantee safety or mitigate potential toxicity or bias. To address the safety concerns, we use the OpenAI moderation API to filter out inappropriate user inputs in our online demo. Nonetheless, we anticipate that Vicuna can serve as an open starting point for future research to tackle these limitations.\\n\\nRelease\\n\\nIn our first release, we will share the training, serving, and evaluation code on a GitHub repo: https://github.com/lm-sys/FastChat. We also released the Vicuna-13B model weights. There is no plan to release the dataset. Join our Discord server and follow our Twitter to get the latest updates.\\n\\nLicense\\n\\nThe online demo is a research preview intended for non-commercial use only, subject to the model License of LLaMA, Terms of Use of the data generated by OpenAI, and Privacy Practices of ShareGPT. Please contact us If you find any potential violation. The code is released under the Apache License 2.0.\\n\\nAcknowledgment\\n\\nWe would like to thank Xinyang Geng, Hao Liu, and Eric Wallace from BAIR; Xuecheng Li, and Tianyi Zhang from Stanford Alpaca team for their insightful discussion and feedback; Qirong Ho from MBZUAI for providing support on the serving cluster. Please check out a blog post from BAIR about a concurrent effort on their chatbot, Koala.\\n\\nThe Team\\n\\nThis is a joint effort with collaborators from multiple institutions, including UC Berkeley, CMU, Stanford, UC San Diego, and MBZUAI.\\n\\nStudents (alphabetical order): Wei-Lin Chiang, Zhuohan Li, Zi Lin, Ying Sheng, Zhanghao Wu, Hao Zhang (✉), Lianmin Zheng (✉), Siyuan Zhuang, Yonghao Zhuang\\n\\nAdvisors (alphabetical order): Joseph E. Gonzalez, Ion Stoica, Eric P. Xing\\n\\n✉ Correspondence to: Lianmin Zheng (<EMAIL>), Hao Zhang (<EMAIL>), or LMSYS (<EMAIL>).\\n\\nCitation\\n\\n@misc{vicuna2023,\\n    title = {Vicuna: An Open-Source Chatbot Impressing GPT-4 with 90\\\\%* ChatGPT Quality},\\n    url = {https://lmsys.org/blog/2023-03-30-vicuna/},\\n    author = {Chiang, Wei-Lin and Li, Zhuohan and Lin, Zi and Sheng, Ying and Wu, Zhanghao and Zhang, Hao and Zheng, Lianmin and Zhuang, Siyuan and Zhuang, Yonghao and Gonzalez, Joseph E. and Stoica, Ion and Xing, Eric P.},\\n    month = {March},\\n    year = {2023}\\n}\\n\\nAfter this blog post, we extended our idea of GPT-4 based evaluation and wrote a more formal paper that systematically studies this \"LLM-as-a-judge\" approach. You are welcome to read and cite this paper: Judging LLM-as-a-judge with MT-Bench and Chatbot Arena.')]"]}, "metadata": {}, "execution_count": 6}]}, {"cell_type": "code", "source": ["len(data)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "VQp4MaDzeC3g", "outputId": "02540ad9-b477-4ede-de1c-2fd5565f863d"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["4"]}, "metadata": {}, "execution_count": 7}]}, {"cell_type": "markdown", "source": ["#**4: Split the Text into Chunks**"], "metadata": {"id": "0hZXVlFqdwRv"}}, {"cell_type": "code", "source": ["text_splitter=CharacterTextSplitter(separator='\\n',\n", "                                    chunk_size=1000,\n", "                                    chunk_overlap=200)"], "metadata": {"id": "3DihTE_ob_zL"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["text_chunks=text_splitter.split_documents(data)"], "metadata": {"id": "Fpk8mrzjeH17"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["len(text_chunks)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "R-l41qDAeNSf", "outputId": "3b23b350-3ff7-430e-8c2c-e4333b9cf6a3"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["86"]}, "metadata": {}, "execution_count": 10}]}, {"cell_type": "code", "source": ["text_chunks[0]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "NZjRHBnoeQ2R", "outputId": "93820a39-b34d-410f-ba31-a58a36e06d3e"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Document(metadata={'source': 'https://blog.gopenai.com/paper-review-llama-2-open-foundation-and-fine-tuned-chat-models-23e539522acb'}, page_content='Open in app\\nSign up\\nSign in\\nWrite\\nSign up\\nSign in\\nPaper Review\\nPaper Review: Llama 2: Open Foundation and Fine-Tuned Chat Models\\nLlama 2: one of the best open source models\\nAnd<PERSON>\\nFollow\\nPublished in\\nGoPenAI\\n15 min read\\nJul 20, 2023\\n--\\nProject link\\nModel link\\nPaper link\\nThe authors of the work present Llama 2, an assortment of pretrained and fine-tuned large language models (LLMs) with sizes varying from 7 billion to 70 billion parameters. The fine-tuned versions, named Llama 2-Chat, are specifically designed for dialogue applications. These models surpass the performance of existing open-source chat models on most benchmarks, and according to human evaluations for usefulness and safety, they could potentially replace closed-source models. The authors also detail their approach to fine-tuning and safety enhancements for Llama 2-Chat to support the community in further developing and responsibly handling LLMs.\\nPretraining')"]}, "metadata": {}, "execution_count": 11}]}, {"cell_type": "code", "source": ["text_chunks[1]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "BSSE72OYeqCi", "outputId": "f83486e9-e282-469f-b95f-014638d16164"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Document(metadata={'source': 'https://blog.gopenai.com/paper-review-llama-2-open-foundation-and-fine-tuned-chat-models-23e539522acb'}, page_content='Pretraining\\nThe authors developed the Llama 2 model family starting from the pretraining methodology of Llama, which utilizes an optimized auto-regressive transformer. They implemented several modifications for improved performance, including enhanced data cleaning, updated data mixes, training on 40% more total tokens, and doubling the context length. They also incorporated grouped-query attention (GQA) to enhance the inference scalability for their larger models.\\nPretraining Data')"]}, "metadata": {}, "execution_count": 12}]}, {"cell_type": "code", "source": ["text_chunks[2]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-zb9LQ20e4Qs", "outputId": "938ec423-cee1-40a4-eb31-79c0532abfee"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Document(metadata={'source': 'https://blog.gopenai.com/paper-review-llama-2-open-foundation-and-fine-tuned-chat-models-23e539522acb'}, page_content='Pretraining Data\\nThe authors utilized a novel mix of data from publicly accessible sources to train the Llama 2 models, excluding any data from Meta’s products or services. They made efforts to erase data from certain sites known for harboring large amounts of personal information about private individuals. They trained the models on 2 trillion tokens of data, believing this amount provided a beneficial performance-cost balance. They also up-sampled the most factual sources to boost knowledge and reduce instances of false information generation or “hallucinations”.\\nLlama 2 Pretrained Model Evaluation\\nLlama 2 models significantly outperform their Llama 1 counterparts:\\nThe 70 billion-parameter Llama 2 model notably improves results on the MMLU and BBH benchmarks by roughly 5 and 8 points, respectively, when compared to the 65 billion-parameter Llama 1 model.\\nLlama 2 models with 7 billion and 30 billion parameters outdo MPT models of similar size in all categories except code benchmarks.')"]}, "metadata": {}, "execution_count": 13}]}, {"cell_type": "markdown", "source": ["#**5: Download the OpenAI Embeddings or Hugging Face Embeddings**"], "metadata": {"id": "oRzFp91Wfb5d"}}, {"cell_type": "code", "source": ["embeddings = OpenAIEmbeddings()"], "metadata": {"id": "GbpaRosM8Oud", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "a37f9730-04a5-42da-8a01-5f2c97c65486"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["<ipython-input-14-365b0dc4bdf0>:3: LangChainDeprecationWarning: The class `OpenAIEmbeddings` was deprecated in LangChain 0.0.9 and will be removed in 1.0. An updated version of the class exists in the langchain-openai package and should be used instead. To use it run `pip install -U langchain-openai` and import as `from langchain_openai import OpenAIEmbeddings`.\n", "  embeddings = OpenAIEmbeddings()\n"]}]}, {"cell_type": "code", "source": ["embeddings"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sHNvev2k-V4D", "outputId": "d9b279d0-78ed-423e-dbdc-efc4a63f5732"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["OpenAIEmbeddings(client=<openai.resources.embeddings.Embeddings object at 0x7abcf06cfca0>, async_client=<openai.resources.embeddings.AsyncEmbeddings object at 0x7abcefad7400>, model='text-embedding-ada-002', deployment='text-embedding-ada-002', openai_api_version='', openai_api_base=None, openai_api_type='', openai_proxy='', embedding_ctx_length=8191, openai_api_key='************************************************************************************************************************************', openai_organization=None, allowed_special=set(), disallowed_special='all', chunk_size=1000, max_retries=2, request_timeout=None, headers=None, tiktoken_enabled=True, tiktoken_model_name=None, show_progress_bar=False, model_kwargs={}, skip_empty=False, default_headers=None, default_query=None, retry_min_seconds=4, retry_max_seconds=20, http_client=None)"]}, "metadata": {}, "execution_count": 15}]}, {"cell_type": "code", "source": ["query_result = embeddings.embed_query(\"Hello world\")\n", "len(query_result)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-JTIYLHPUMtN", "outputId": "62a45bb3-b133-407b-91e7-56c31764df13"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["1536"]}, "metadata": {}, "execution_count": 16}]}, {"cell_type": "markdown", "source": ["#**06: Convert the Text Chunks into Embeddings and Create a Knowledge Base**"], "metadata": {"id": "e7UG7kM0hrtg"}}, {"cell_type": "code", "source": ["vectorstore=FAISS.from_documents(text_chunks, embeddings)"], "metadata": {"id": "3iG2iA-4hzIX"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["#**07: Create a Large Language Model (LLM) Wrapper**"], "metadata": {"id": "s7V8N04XjAdT"}}, {"cell_type": "code", "source": ["llm=ChatOpenAI()"], "metadata": {"id": "WmbUqPijjG3C", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "40157aa6-dab1-49d9-dbc8-d654e4b0b90c"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["<ipython-input-18-bd12acb39de2>:1: LangChainDeprecationWarning: The class `ChatOpenAI` was deprecated in LangChain 0.0.10 and will be removed in 1.0. An updated version of the class exists in the langchain-openai package and should be used instead. To use it run `pip install -U langchain-openai` and import as `from langchain_openai import ChatOpenAI`.\n", "  llm=ChatOpenAI()\n"]}]}, {"cell_type": "code", "source": ["llm"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TSKa8emUsmhW", "outputId": "072a6791-d0af-4327-f1da-a34d23e9233a"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["ChatOpenAI(client=<openai.resources.chat.completions.Completions object at 0x7abcef48e1d0>, async_client=<openai.resources.chat.completions.AsyncCompletions object at 0x7abcef4a0340>, model_kwargs={}, openai_api_key='************************************************************************************************************************************', openai_proxy='')"]}, "metadata": {}, "execution_count": 19}]}, {"cell_type": "code", "source": ["llm.predict(\"Please provide a concise summary of the Book Harry Potter\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 124}, "id": "yDmBlmEDCG62", "outputId": "6d04fbba-7123-42f2-948f-0d8f904dd117"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["<ipython-input-20-181e585c8cae>:1: LangChainDeprecationWarning: The method `BaseChatModel.predict` was deprecated in langchain-core 0.1.7 and will be removed in 1.0. Use invoke instead.\n", "  llm.predict(\"Please provide a concise summary of the Book Harry Potter\")\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'\"<PERSON>\" follows the life of a young boy, <PERSON>, who discovers he is a wizard and attends Hogwarts School of Witchcraft and Wizardry. Alongside his friends <PERSON> and <PERSON><PERSON><PERSON>, <PERSON> battles the dark wizard <PERSON><PERSON><PERSON><PERSON> and uncovers the truth about his own past. The series explores themes of friendship, bravery, and the power of love against evil forces.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 20}]}, {"cell_type": "markdown", "source": ["#**08: Initialize the Retrieval QA with Source Chain**"], "metadata": {"id": "qzgC6Z6wjpXC"}}, {"cell_type": "code", "source": ["chain = RetrievalQAWithSourcesChain.from_llm(llm=llm, retriever=vectorstore.as_retriever())"], "metadata": {"id": "8OITqbQgjvAj"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["result=chain({\"question\": \"How good is <PERSON><PERSON>?\"}, return_only_outputs=True)"], "metadata": {"id": "YhTOLCIGl3Os", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "01dd820c-d51c-49b1-9532-8455897b2eaf"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["<ipython-input-22-6dc5d9fb268a>:1: Lang<PERSON>hainDeprecationWarning: The method `Chain.__call__` was deprecated in langchain 0.1.0 and will be removed in 1.0. Use invoke instead.\n", "  result=chain({\"question\": \"How good is <PERSON><PERSON>?\"}, return_only_outputs=True)\n"]}]}, {"cell_type": "code", "source": ["result['answer']"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "0ioHtqtBY7Ec", "outputId": "3002c704-ff60-4f49-fe73-8406458f78f4"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Vicuna has been shown to have competitive performance compared to other open-source models like Stanford Alpaca. \\n'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 23}]}, {"cell_type": "code", "source": ["wrapped_text = textwrap.fill(result['answer'], width=500)"], "metadata": {"id": "meUipuOuGPff"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["wrapped_text"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "gy0zm_HDpxSg", "outputId": "c89fd5fb-91c2-418d-f795-71052ccf5eac"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Vicuna has been shown to have competitive performance compared to other open-source models like Stanford Alpaca.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 25}]}, {"cell_type": "code", "source": ["result=chain({\"question\": \"How does Llama 2 outperforms other models\"}, return_only_outputs=True)"], "metadata": {"id": "ibpSu9GeZ0ED"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["result['answer']"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 52}, "id": "Yorw-ocya_ac", "outputId": "4bab8f6d-9150-4182-e64a-a3a358d14fba"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Llama 2 outperforms other models in all benchmark categories except code benchmarks, surpasses all open-source models, and significantly improves results on various benchmarks when compared to Llama 1 counterparts.\\n'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 27}]}, {"cell_type": "code", "source": ["wrapped_text = textwrap.fill(result['answer'], width=500)"], "metadata": {"id": "R3AeyflGrSF9"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["wrapped_text"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 52}, "id": "6NGE0JP5rTyu", "outputId": "dc07896d-21dc-4ba2-9678-f64f293eb9a9"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'Llama 2 outperforms other models in all benchmark categories except code benchmarks, surpasses all open-source models, and significantly improves results on various benchmarks when compared to Llama 1 counterparts.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 29}]}, {"cell_type": "code", "source": ["result=chain({\"question\": \"What is is stableLM?\"}, return_only_outputs=True)\n"], "metadata": {"id": "32qmjjbja4fW"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["result['answer']"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 70}, "id": "lTrdGoL7a9Ur", "outputId": "4886ba03-729c-43ef-e521-42e7dbb7f029"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'StableLM is an open-source language model released by Stability AI. The model is available in various parameter sizes, ranging from 3 billion to 7 billion, with larger models to follow. It is trained on a dataset three times larger than The Pile, consisting of 1.5 trillion tokens of content. Despite its smaller size compared to GPT-3, StableLM demonstrates high performance in conversational and coding tasks.\\n'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 68}]}, {"cell_type": "code", "source": ["wrapped_text = textwrap.fill(result['answer'], width=500)"], "metadata": {"id": "3uB48-lmrdgF"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["wrapped_text"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 70}, "id": "1EV_zfRFreap", "outputId": "137ba366-5079-4995-90bf-d32dfdb67117"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'StableLM is an open-source language model released by Stability AI. The model is available in various parameter sizes, ranging from 3 billion to 7 billion, with larger models to follow. It is trained on a dataset three times larger than The Pile, consisting of 1.5 trillion tokens of content. Despite its smaller size compared to GPT-3, StableLM demonstrates high performance in conversational and coding tasks.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 70}]}, {"cell_type": "code", "source": ["result=chain({\"question\": \"Can you please share some details about MPT-7b Model\"}, return_only_outputs=True)"], "metadata": {"id": "rFOT5g9TE7ws"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["result['answer']"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 140}, "id": "mH-s6NSuZcfr", "outputId": "803a9e22-a3a9-47a9-8478-12d6faba001a"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'The MPT-7B model was built by the MosaicML NLP team using tools available to every customer of MosaicML. It was trained on the MosaicML platform using tools such as Compute: A100-40GB and A100-80GB GPUs from Oracle Cloud, Orchestration and Fault Tolerance: MCLI and MosaicML platform, Data: OCI Object Storage and StreamingDataset, and Training software: Composer, PyTorch FSDP, and LLM Foundry. The model was built in a few weeks, including data preparation, training, finetuning, and deployment. It offers faster performance compared to other 7B models like LLaMa-7B when using `model.generate(...)` due to its optimized layers. The base MPT-7B model took approximately 9.5 days to train on 440xA100-40GB GPUs, costing around $200k, while the finetuned models required less compute and were cheaper. Each training recipe can be fully customized on the MosaicML platform.\\n'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 72}]}, {"cell_type": "code", "source": ["wrapped_text = textwrap.fill(result['answer'], width=100)"], "metadata": {"id": "WwngqtcCDu9k"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["wrapped_text"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 140}, "id": "WtC9OJDHefg2", "outputId": "75390806-e569-4151-e1b9-df7dbb69f4d5"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'The MPT-7B model was built by the MosaicML NLP team using tools available to every customer of\\nMosaicML. It was trained on the MosaicML platform using tools such as Compute: A100-40GB and\\nA100-80GB GPUs from Oracle Cloud, Orchestration and Fault Tolerance: MCLI and MosaicML platform,\\nData: OCI Object Storage and StreamingDataset, and Training software: Composer, PyTorch FSDP, and\\nLLM Foundry. The model was built in a few weeks, including data preparation, training, finetuning,\\nand deployment. It offers faster performance compared to other 7B models like LLaMa-7B when using\\n`model.generate(...)` due to its optimized layers. The base MPT-7B model took approximately 9.5 days\\nto train on 440xA100-40GB GPUs, costing around $200k, while the finetuned models required less\\ncompute and were cheaper. Each training recipe can be fully customized on the MosaicML platform.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 74}]}, {"cell_type": "code", "source": ["while True:\n", "  query=input(f\"Prompt: \")\n", "  if query == 'exit':\n", "    print('Exiting')\n", "    sys.exit()\n", "  if query =='':\n", "    continue\n", "  result=chain({'question':query})\n", "  print(f\"Answer: \" +result[\"answer\"])\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 463}, "id": "ffPUXfwKkLvH", "outputId": "5f5a0b6d-6894-4830-f0e9-e4bb5ca421c2"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Prompt: what is mpt 7b\n", "Answer:  The MosaicML Text Data Set includes a wide range of text data, including news articles, product reviews, and social media posts.\n", "\n"]}, {"output_type": "error", "ename": "KeyboardInterrupt", "evalue": "ignored", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-57-1c2bfd8ef476>\u001b[0m in \u001b[0;36m<cell line: 1>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0;32mwhile\u001b[0m \u001b[0;32mTrue\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 2\u001b[0;31m   \u001b[0mquery\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34mf\"Prompt: \"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      3\u001b[0m   \u001b[0;32mif\u001b[0m \u001b[0mquery\u001b[0m \u001b[0;34m==\u001b[0m \u001b[0;34m'exit'\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m     \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'Exiting'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m     \u001b[0msys\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mexit\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/ipykernel/kernelbase.py\u001b[0m in \u001b[0;36mraw_input\u001b[0;34m(self, prompt)\u001b[0m\n\u001b[1;32m    849\u001b[0m                 \u001b[0;34m\"raw_input was called, but this frontend does not support input requests.\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    850\u001b[0m             )\n\u001b[0;32m--> 851\u001b[0;31m         return self._input_request(str(prompt),\n\u001b[0m\u001b[1;32m    852\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_parent_ident\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    853\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_parent_header\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/ipykernel/kernelbase.py\u001b[0m in \u001b[0;36m_input_request\u001b[0;34m(self, prompt, ident, parent, password)\u001b[0m\n\u001b[1;32m    893\u001b[0m             \u001b[0;32mexcept\u001b[0m \u001b[0mKeyboardInterrupt\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    894\u001b[0m                 \u001b[0;31m# re-raise KeyboardInterrupt, to truncate traceback\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 895\u001b[0;31m                 \u001b[0;32mraise\u001b[0m \u001b[0mKeyboardInterrupt\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"Interrupted by user\"\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mfrom\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    896\u001b[0m             \u001b[0;32mexcept\u001b[0m \u001b[0mException\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    897\u001b[0m                 \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mlog\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mwarning\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"Invalid Message:\"\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mexc_info\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: Interrupted by user"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "ab1lxX5WwXsJ"}, "execution_count": null, "outputs": []}]}