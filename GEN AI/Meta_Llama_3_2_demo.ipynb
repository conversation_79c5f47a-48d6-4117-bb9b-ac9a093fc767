{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "machine_shape": "hm", "gpuType": "A100"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "source": ["https://console.groq.com/playground"], "metadata": {"id": "40fCn-HRXsjF"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "aILu3apDU7Yx", "colab": {"base_uri": "https://localhost:8080/", "height": 741}, "outputId": "9ce64b1e-3ac8-46ee-9b5e-6ecaa192dc18"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: transformers in /usr/local/lib/python3.10/dist-packages (4.44.2)\n", "Collecting transformers\n", "  Downloading transformers-4.45.1-py3-none-any.whl.metadata (44 kB)\n", "\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/44.4 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m44.4/44.4 kB\u001b[0m \u001b[31m3.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from transformers) (3.16.1)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.23.2 in /usr/local/lib/python3.10/dist-packages (from transformers) (0.24.7)\n", "Requirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.10/dist-packages (from transformers) (1.26.4)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.10/dist-packages (from transformers) (24.1)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from transformers) (6.0.2)\n", "Requirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.10/dist-packages (from transformers) (2024.9.11)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from transformers) (2.32.3)\n", "Requirement already satisfied: safetensors>=0.4.1 in /usr/local/lib/python3.10/dist-packages (from transformers) (0.4.5)\n", "Collecting tokenizers<0.21,>=0.20 (from transformers)\n", "  Downloading tokenizers-0.20.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.7 kB)\n", "Requirement already satisfied: tqdm>=4.27 in /usr/local/lib/python3.10/dist-packages (from transformers) (4.66.5)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.23.2->transformers) (2024.6.1)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.23.2->transformers) (4.12.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->transformers) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->transformers) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->transformers) (2.2.3)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->transformers) (2024.8.30)\n", "Downloading transformers-4.45.1-py3-none-any.whl (9.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m9.9/9.9 MB\u001b[0m \u001b[31m91.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading tokenizers-0.20.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.9/2.9 MB\u001b[0m \u001b[31m100.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: tokenizers, transformers\n", "  Attempting uninstall: tokenizers\n", "    Found existing installation: tokenizers 0.19.1\n", "    Uninstalling tokenizers-0.19.1:\n", "      Successfully uninstalled tokenizers-0.19.1\n", "  Attempting uninstall: transformers\n", "    Found existing installation: transformers 4.44.2\n", "    Uninstalling transformers-4.44.2:\n", "      Successfully uninstalled transformers-4.44.2\n", "Successfully installed tokenizers-0.20.0 transformers-4.45.1\n"]}, {"output_type": "display_data", "data": {"application/vnd.colab-display-data+json": {"pip_warning": {"packages": ["transformers"]}, "id": "d6e8b357ee8d4218a395b01f5291f2c6"}}, "metadata": {}}], "source": ["pip install --upgrade transformers"]}, {"cell_type": "code", "source": ["import requests\n", "import torch\n", "from PIL import Image\n", "from transformers import MllamaForConditionalGeneration, AutoProcessor\n", "\n", "model_id = \"meta-llama/Llama-3.2-11B-Vision-Instruct\"\n", "\n", "model = MllamaForConditionalGeneration.from_pretrained(\n", "    model_id,\n", "    torch_dtype=torch.bfloat16,\n", "    device_map=\"auto\",\n", ")\n", "processor = AutoProcessor.from_pretrained(model_id)\n", "\n", "url = \"https://huggingface.co/datasets/huggingface/documentation-images/resolve/0052a70beed5bf71b92610a43a52df6d286cd5f3/diffusers/rabbit.jpg\"\n", "image = Image.open(requests.get(url, stream=True).raw)\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": [\n", "        {\"type\": \"image\"},\n", "        {\"type\": \"text\", \"text\": \"If I had to write a haiku for this one, it would be: \"}\n", "    ]}\n", "]\n", "input_text = processor.apply_chat_template(messages, add_generation_prompt=True)\n", "inputs = processor(image, input_text, return_tensors=\"pt\").to(model.device)\n", "\n", "output = model.generate(**inputs, max_new_tokens=30)\n", "print(processor.decode(output[0]))\n"], "metadata": {"id": "kVULpceLwAYj"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "8Qr2o8dewAWc"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "1O5uD9tcwATz"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "wzOXmP1TwARE"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["!pip install groq"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7tNlDgFxYMx3", "outputId": "ce2b0c74-a527-4727-e760-6b74eb29669f"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting groq\n", "  Downloading groq-0.11.0-py3-none-any.whl.metadata (13 kB)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.10/dist-packages (from groq) (3.7.1)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/lib/python3/dist-packages (from groq) (1.7.0)\n", "Collecting httpx<1,>=0.23.0 (from groq)\n", "  Downloading httpx-0.27.2-py3-none-any.whl.metadata (7.1 kB)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /usr/local/lib/python3.10/dist-packages (from groq) (2.9.2)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from groq) (1.3.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.7 in /usr/local/lib/python3.10/dist-packages (from groq) (4.12.2)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->groq) (3.10)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.5.0->groq) (1.2.2)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from httpx<1,>=0.23.0->groq) (2024.8.30)\n", "Collecting httpcore==1.* (from httpx<1,>=0.23.0->groq)\n", "  Downloading httpcore-1.0.5-py3-none-any.whl.metadata (20 kB)\n", "Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx<1,>=0.23.0->groq)\n", "  Downloading h11-0.14.0-py3-none-any.whl.metadata (8.2 kB)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1.9.0->groq) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.23.4 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1.9.0->groq) (2.23.4)\n", "Downloading groq-0.11.0-py3-none-any.whl (106 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m106.5/106.5 kB\u001b[0m \u001b[31m7.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading httpx-0.27.2-py3-none-any.whl (76 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m76.4/76.4 kB\u001b[0m \u001b[31m8.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading httpcore-1.0.5-py3-none-any.whl (77 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m8.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m5.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: h11, httpcore, httpx, groq\n", "Successfully installed groq-0.11.0 h11-0.14.0 httpcore-1.0.5 httpx-0.27.2\n"]}]}, {"cell_type": "code", "source": ["from groq import Groq\n", "\n", "client = Groq(api_key=\"********************************************************\")\n", "\n", "\n", "prompt = \"Give me a python code to add 2 numbers\"\n", "\n", "completion = client.chat.completions.create(\n", "    model=\"llama-3.2-1b-preview\",\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": prompt\n", "        }\n", "    ],\n", "    temperature=1,\n", "    max_tokens=1024,\n", "    top_p=1,\n", "    stream=True,\n", "    stop=None,\n", ")\n", "\n", "for chunk in completion:\n", "    print(chunk.choices[0].delta.content or \"\", end=\"\")\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ssKd7LUhYLRx", "outputId": "b74ed38a-812d-4270-f607-9e1eaf9f9705"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["**Adding 2 Numbers in Python**\n", "================================\n", "\n", "Here's a simple Python function to add two numbers:\n", "\n", "```python\n", "def add_numbers(num1, num2):\n", "    \"\"\"\n", "    Adds two numbers and returns the result.\n", "\n", "    Args:\n", "        num1 (float): The first number to add.\n", "        num2 (float): The second number to add.\n", "\n", "    Returns:\n", "        float: The sum of num1 and num2.\n", "    \"\"\"\n", "    return num1 + num2\n", "\n", "# Example usage:\n", "num1 = 10.5\n", "num2 = 2.2\n", "result = add_numbers(num1, num2)\n", "print(f\"{num1} + {num2} = {result}\")\n", "```\n", "\n", "In this code:\n", "\n", "1.  We define a function `add_numbers` that takes two arguments `num1` and `num2`.\n", "2.  The function returns the sum of `num1` and `num2`.\n", "3.  We create an instance of `add_numbers` with example values for `num1` and `num2`.\n", "4.  We call the function to compute the sum and print the result.\n", "\n", "When you run this code, it will output the sum of `10.5` and `2.2`.\n", "\n", "**Alternative Implementation**\n", "-----------------------------\n", "\n", "You can also use the built-in `+` operator in Python to add two numbers:\n", "\n", "```python\n", "result = num1 + num2\n", "print(f\"{num1} + {num2} = {result}\")\n", "```\n", "\n", "This will produce the same result as the previous implementation.\n", "\n", "I hope this helps! Let me know if you have any questions or need further assistance."]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "aavjqV54YPzn"}, "execution_count": null, "outputs": []}]}