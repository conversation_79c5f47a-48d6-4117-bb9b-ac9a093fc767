{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["https://console.weaviate.cloud/"], "metadata": {"id": "t26J2nX6B2AH"}}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jnIh-coaAGnU", "outputId": "6cb219f5-30f9-4bcf-c1a9-6ab550bd3eac"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting weaviate-client\n", "  Downloading weaviate_client-3.25.2-py3-none-any.whl (120 kB)\n", "\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/120.3 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[91m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[90m╺\u001b[0m\u001b[90m━━\u001b[0m \u001b[32m112.6/120.3 kB\u001b[0m \u001b[31m3.3 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m120.3/120.3 kB\u001b[0m \u001b[31m2.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: requests<3.0.0,>=2.30.0 in /usr/local/lib/python3.10/dist-packages (from weaviate-client) (2.31.0)\n", "Collecting validators<1.0.0,>=0.21.2 (from weaviate-client)\n", "  Downloading validators-0.22.0-py3-none-any.whl (26 kB)\n", "Collecting authlib<2.0.0,>=1.2.1 (from weaviate-client)\n", "  Downloading Authlib-1.2.1-py2.py3-none-any.whl (215 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m215.3/215.3 kB\u001b[0m \u001b[31m9.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: cryptography>=3.2 in /usr/local/lib/python3.10/dist-packages (from authlib<2.0.0,>=1.2.1->weaviate-client) (41.0.5)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.30.0->weaviate-client) (3.3.1)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.30.0->weaviate-client) (3.4)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.30.0->weaviate-client) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3.0.0,>=2.30.0->weaviate-client) (2023.7.22)\n", "Requirement already satisfied: cffi>=1.12 in /usr/local/lib/python3.10/dist-packages (from cryptography>=3.2->authlib<2.0.0,>=1.2.1->weaviate-client) (1.16.0)\n", "Requirement already satisfied: pycparser in /usr/local/lib/python3.10/dist-packages (from cffi>=1.12->cryptography>=3.2->authlib<2.0.0,>=1.2.1->weaviate-client) (2.21)\n", "Installing collected packages: validators, authlib, weaviate-client\n", "Successfully installed authlib-1.2.1 validators-0.22.0 weaviate-client-3.25.2\n", "Collecting langchain\n", "  Downloading langchain-0.0.330-py3-none-any.whl (2.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m20.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: PyYAML>=5.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.0.22)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /usr/local/lib/python3.10/dist-packages (from langchain) (3.8.6)\n", "Requirement already satisfied: anyio<4.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (3.7.1)\n", "Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (4.0.3)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain)\n", "  Downloading dataclasses_json-0.6.1-py3-none-any.whl (27 kB)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain)\n", "  Downloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Collecting langsmith<0.1.0,>=0.0.52 (from langchain)\n", "  Downloading langsmith-0.0.57-py3-none-any.whl (44 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m44.5/44.5 kB\u001b[0m \u001b[31m5.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.23.5)\n", "Requirement already satisfied: pydantic<3,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.10.13)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.31.0)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (8.2.3)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (23.1.0)\n", "Requirement already satisfied: charset-normalizer<4.0,>=2.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (3.3.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (6.0.4)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.9.2)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.4.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.3.1)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.10/dist-packages (from anyio<4.0->langchain) (3.4)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.10/dist-packages (from anyio<4.0->langchain) (1.3.0)\n", "Requirement already satisfied: exceptiongroup in /usr/local/lib/python3.10/dist-packages (from anyio<4.0->langchain) (1.1.3)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain)\n", "  Downloading marshmallow-3.20.1-py3-none-any.whl (49 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.4/49.4 kB\u001b[0m \u001b[31m5.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain)\n", "  Downloading typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain)\n", "  Downloading jsonpointer-2.4-py2.py3-none-any.whl (7.8 kB)\n", "Requirement already satisfied: typing-extensions>=4.2.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<3,>=1->langchain) (4.5.0)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2023.7.22)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (3.0.0)\n", "Requirement already satisfied: packaging>=17.0 in /usr/local/lib/python3.10/dist-packages (from marshmallow<4.0.0,>=3.18.0->dataclasses-json<0.7,>=0.5.7->langchain) (23.2)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Installing collected packages: mypy-extensions, marshmallow, jsonpointer, typing-inspect, langsmith, jsonpatch, dataclasses-json, langchain\n", "Successfully installed dataclasses-json-0.6.1 jsonpatch-1.33 jsonpointer-2.4 langchain-0.0.330 langsmith-0.0.57 marshmallow-3.20.1 mypy-extensions-1.0.0 typing-inspect-0.9.0\n", "Collecting openai\n", "  Downloading openai-0.28.1-py3-none-any.whl (76 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.0/77.0 kB\u001b[0m \u001b[31m2.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: requests>=2.20 in /usr/local/lib/python3.10/dist-packages (from openai) (2.31.0)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from openai) (4.66.1)\n", "Requirement already satisfied: aiohttp in /usr/local/lib/python3.10/dist-packages (from openai) (3.8.6)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.20->openai) (3.3.1)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests>=2.20->openai) (3.4)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.20->openai) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests>=2.20->openai) (2023.7.22)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai) (23.1.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai) (6.0.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0.0a3 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai) (4.0.3)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai) (1.9.2)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai) (1.4.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai) (1.3.1)\n", "Installing collected packages: openai\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "llmx 0.0.15a0 requires cohere, which is not installed.\n", "llmx 0.0.15a0 requires tiktoken, which is not installed.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed openai-0.28.1\n"]}], "source": ["!pip install weaviate-client\n", "!pip install langchain\n", "!pip install openai"]}, {"cell_type": "code", "source": ["OPENAI_API_KEY = \"***************************************************\"\n", "WEAVIATE_API_KEY = \"k5tt1mzsi7g17sfFUdcOgRvPuybOAvOCdyFu\"\n", "WEAVIATE_CLUSTER = \"https://test-njh6t5hm.weaviate.network\""], "metadata": {"id": "V-sQraYtBvBv"}, "execution_count": 7, "outputs": []}, {"cell_type": "markdown", "source": ["## Data Reading"], "metadata": {"id": "uuytDEGdCyCl"}}, {"cell_type": "code", "source": ["!mkdir data"], "metadata": {"id": "UBGcj0bxCwgO"}, "execution_count": 4, "outputs": []}, {"cell_type": "code", "source": ["!pip install unstructured\n", "!pip install \"unstructured[pdf]\""], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "Vzf37wCDEHom", "outputId": "c7d07776-e8b2-48f5-a983-ca307cd5c0af"}, "execution_count": 6, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting unstructured\n", "  Downloading unstructured-0.10.28-py3-none-any.whl (1.7 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.7/1.7 MB\u001b[0m \u001b[31m18.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: chardet in /usr/local/lib/python3.10/dist-packages (from unstructured) (5.2.0)\n", "Collecting filetype (from unstructured)\n", "  Downloading filetype-1.2.0-py2.py3-none-any.whl (19 kB)\n", "Collecting python-magic (from unstructured)\n", "  Downloading python_magic-0.4.27-py2.py3-none-any.whl (13 kB)\n", "Requirement already satisfied: lxml in /usr/local/lib/python3.10/dist-packages (from unstructured) (4.9.3)\n", "Requirement already satisfied: nltk in /usr/local/lib/python3.10/dist-packages (from unstructured) (3.8.1)\n", "Requirement already satisfied: tabulate in /usr/local/lib/python3.10/dist-packages (from unstructured) (0.9.0)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from unstructured) (2.31.0)\n", "Requirement already satisfied: beautifulsoup4 in /usr/local/lib/python3.10/dist-packages (from unstructured) (4.11.2)\n", "Collecting emoji (from unstructured)\n", "  Downloading emoji-2.8.0-py2.py3-none-any.whl (358 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m358.9/358.9 kB\u001b[0m \u001b[31m29.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: dataclasses-json in /usr/local/lib/python3.10/dist-packages (from unstructured) (0.6.1)\n", "Collecting python-iso639 (from unstructured)\n", "  Downloading python_iso639-2023.6.15-py3-none-any.whl (275 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m275.1/275.1 kB\u001b[0m \u001b[31m26.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langdetect (from unstructured)\n", "  Downloading langdetect-1.0.9.tar.gz (981 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m981.5/981.5 kB\u001b[0m \u001b[31m67.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from unstructured) (1.23.5)\n", "Collecting rapidfuzz (from unstructured)\n", "  Downloading rapidfuzz-3.5.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.3 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.3/3.3 MB\u001b[0m \u001b[31m48.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting backoff (from unstructured)\n", "  Downloading backoff-2.2.1-py3-none-any.whl (15 kB)\n", "Requirement already satisfied: typing-extensions in /usr/local/lib/python3.10/dist-packages (from unstructured) (4.5.0)\n", "Requirement already satisfied: soupsieve>1.2 in /usr/local/lib/python3.10/dist-packages (from beautifulsoup4->unstructured) (2.5)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json->unstructured) (3.20.1)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json->unstructured) (0.9.0)\n", "Requirement already satisfied: six in /usr/local/lib/python3.10/dist-packages (from langdetect->unstructured) (1.16.0)\n", "Requirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured) (8.1.7)\n", "Requirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured) (1.3.2)\n", "Requirement already satisfied: regex>=2021.8.3 in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured) (2023.6.3)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured) (4.66.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured) (3.3.1)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured) (3.4)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured) (2023.7.22)\n", "Requirement already satisfied: packaging>=17.0 in /usr/local/lib/python3.10/dist-packages (from marshmallow<4.0.0,>=3.18.0->dataclasses-json->unstructured) (23.2)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /usr/local/lib/python3.10/dist-packages (from typing-inspect<1,>=0.4.0->dataclasses-json->unstructured) (1.0.0)\n", "Building wheels for collected packages: langdetect\n", "  Building wheel for langdetect (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for langdetect: filename=langdetect-1.0.9-py3-none-any.whl size=993224 sha256=82449007d49f233240cf5be86b735b1ae7083ac0ad3088afcdd55e86ae1a21ad\n", "  Stored in directory: /root/.cache/pip/wheels/95/03/7d/59ea870c70ce4e5a370638b5462a7711ab78fba2f655d05106\n", "Successfully built langdetect\n", "Installing collected packages: filetype, rapidfuzz, python-magic, python-iso639, langdetect, emoji, backoff, unstructured\n", "Successfully installed backoff-2.2.1 emoji-2.8.0 filetype-1.2.0 langdetect-1.0.9 python-iso639-2023.6.15 python-magic-0.4.27 rapidfuzz-3.5.2 unstructured-0.10.28\n", "Requirement already satisfied: unstructured[pdf] in /usr/local/lib/python3.10/dist-packages (0.10.28)\n", "Requirement already satisfied: chardet in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (5.2.0)\n", "Requirement already satisfied: filetype in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (1.2.0)\n", "Requirement already satisfied: python-magic in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (0.4.27)\n", "Requirement already satisfied: lxml in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (4.9.3)\n", "Requirement already satisfied: nltk in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (3.8.1)\n", "Requirement already satisfied: tabulate in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (0.9.0)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (2.31.0)\n", "Requirement already satisfied: beautifulsoup4 in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (4.11.2)\n", "Requirement already satisfied: emoji in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (2.8.0)\n", "Requirement already satisfied: dataclasses-json in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (0.6.1)\n", "Requirement already satisfied: python-iso639 in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (2023.6.15)\n", "Requirement already satisfied: langdetect in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (1.0.9)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (1.23.5)\n", "Requirement already satisfied: rapidfuzz in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (3.5.2)\n", "Requirement already satisfied: backoff in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (2.2.1)\n", "Requirement already satisfied: typing-extensions in /usr/local/lib/python3.10/dist-packages (from unstructured[pdf]) (4.5.0)\n", "Collecting onnx (from unstructured[pdf])\n", "  Downloading onnx-1.15.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (15.7 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m15.7/15.7 MB\u001b[0m \u001b[31m70.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting pdf2image (from unstructured[pdf])\n", "  Downloading pdf2image-1.16.3-py3-none-any.whl (11 kB)\n", "Collecting pdfminer.six (from unstructured[pdf])\n", "  Downloading pdfminer.six-********-py3-none-any.whl (5.6 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.6/5.6 MB\u001b[0m \u001b[31m102.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting unstructured-inference==0.7.10 (from unstructured[pdf])\n", "  Downloading unstructured_inference-0.7.10-py3-none-any.whl (61 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m61.8/61.8 kB\u001b[0m \u001b[31m6.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting unstructured.pytesseract>=0.3.12 (from unstructured[pdf])\n", "  Downloading unstructured.pytesseract-0.3.12-py3-none-any.whl (14 kB)\n", "Collecting layoutparser[layoutmodels,tesseract] (from unstructured-inference==0.7.10->unstructured[pdf])\n", "  Downloading layoutparser-0.3.4-py3-none-any.whl (19.2 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m19.2/19.2 MB\u001b[0m \u001b[31m66.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting python-multipart (from unstructured-inference==0.7.10->unstructured[pdf])\n", "  Downloading python_multipart-0.0.6-py3-none-any.whl (45 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m45.7/45.7 kB\u001b[0m \u001b[31m5.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting huggingface-hub (from unstructured-inference==0.7.10->unstructured[pdf])\n", "  Downloading huggingface_hub-0.18.0-py3-none-any.whl (301 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m302.0/302.0 kB\u001b[0m \u001b[31m31.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: opencv-python!=******** in /usr/local/lib/python3.10/dist-packages (from unstructured-inference==0.7.10->unstructured[pdf]) (********)\n", "Collecting onnxruntime<1.16 (from unstructured-inference==0.7.10->unstructured[pdf])\n", "  Downloading onnxruntime-1.15.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (5.9 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.9/5.9 MB\u001b[0m \u001b[31m104.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting transformers>=4.25.1 (from unstructured-inference==0.7.10->unstructured[pdf])\n", "  Downloading transformers-4.35.0-py3-none-any.whl (7.9 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m7.9/7.9 MB\u001b[0m \u001b[31m44.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: packaging>=21.3 in /usr/local/lib/python3.10/dist-packages (from unstructured.pytesseract>=0.3.12->unstructured[pdf]) (23.2)\n", "Requirement already satisfied: Pillow>=8.0.0 in /usr/local/lib/python3.10/dist-packages (from unstructured.pytesseract>=0.3.12->unstructured[pdf]) (9.4.0)\n", "Requirement already satisfied: soupsieve>1.2 in /usr/local/lib/python3.10/dist-packages (from beautifulsoup4->unstructured[pdf]) (2.5)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json->unstructured[pdf]) (3.20.1)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in /usr/local/lib/python3.10/dist-packages (from dataclasses-json->unstructured[pdf]) (0.9.0)\n", "Requirement already satisfied: six in /usr/local/lib/python3.10/dist-packages (from langdetect->unstructured[pdf]) (1.16.0)\n", "Requirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured[pdf]) (8.1.7)\n", "Requirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured[pdf]) (1.3.2)\n", "Requirement already satisfied: regex>=2021.8.3 in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured[pdf]) (2023.6.3)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured[pdf]) (4.66.1)\n", "Requirement already satisfied: protobuf>=3.20.2 in /usr/local/lib/python3.10/dist-packages (from onnx->unstructured[pdf]) (3.20.3)\n", "Requirement already satisfied: charset-normalizer>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from pdfminer.six->unstructured[pdf]) (3.3.1)\n", "Requirement already satisfied: cryptography>=36.0.0 in /usr/local/lib/python3.10/dist-packages (from pdfminer.six->unstructured[pdf]) (41.0.5)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured[pdf]) (3.4)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured[pdf]) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured[pdf]) (2023.7.22)\n", "Requirement already satisfied: cffi>=1.12 in /usr/local/lib/python3.10/dist-packages (from cryptography>=36.0.0->pdfminer.six->unstructured[pdf]) (1.16.0)\n", "Collecting coloredlogs (from onnxruntime<1.16->unstructured-inference==0.7.10->unstructured[pdf])\n", "  Downloading coloredlogs-15.0.1-py2.py3-none-any.whl (46 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m46.0/46.0 kB\u001b[0m \u001b[31m5.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: flatbuffers in /usr/local/lib/python3.10/dist-packages (from onnxruntime<1.16->unstructured-inference==0.7.10->unstructured[pdf]) (23.5.26)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from onnxruntime<1.16->unstructured-inference==0.7.10->unstructured[pdf]) (1.12)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from transformers>=4.25.1->unstructured-inference==0.7.10->unstructured[pdf]) (3.12.4)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from transformers>=4.25.1->unstructured-inference==0.7.10->unstructured[pdf]) (6.0.1)\n", "Collecting tokenizers<0.15,>=0.14 (from transformers>=4.25.1->unstructured-inference==0.7.10->unstructured[pdf])\n", "  Downloading tokenizers-0.14.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.8/3.8 MB\u001b[0m \u001b[31m73.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting safetensors>=0.3.1 (from transformers>=4.25.1->unstructured-inference==0.7.10->unstructured[pdf])\n", "  Downloading safetensors-0.4.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.3 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.3/1.3 MB\u001b[0m \u001b[31m62.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub->unstructured-inference==0.7.10->unstructured[pdf]) (2023.6.0)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /usr/local/lib/python3.10/dist-packages (from typing-inspect<1,>=0.4.0->dataclasses-json->unstructured[pdf]) (1.0.0)\n", "Requirement already satisfied: scipy in /usr/local/lib/python3.10/dist-packages (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf]) (1.11.3)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf]) (1.5.3)\n", "Collecting iopath (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf])\n", "  Downloading iopath-0.1.10.tar.gz (42 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m42.2/42.2 kB\u001b[0m \u001b[31m2.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Collecting pdfplumber (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf])\n", "  Downloading pdfplumber-0.10.3-py3-none-any.whl (48 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.0/49.0 kB\u001b[0m \u001b[31m5.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: torch in /usr/local/lib/python3.10/dist-packages (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf]) (2.1.0+cu118)\n", "Requirement already satisfied: torchvision in /usr/local/lib/python3.10/dist-packages (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf]) (0.16.0+cu118)\n", "Collecting effdet (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf])\n", "  Downloading effdet-0.4.1-py3-none-any.whl (112 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m112.5/112.5 kB\u001b[0m \u001b[31m12.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting pytesseract (from layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf])\n", "  Downloading pytesseract-0.3.10-py3-none-any.whl (14 kB)\n", "Requirement already satisfied: pycparser in /usr/local/lib/python3.10/dist-packages (from cffi>=1.12->cryptography>=36.0.0->pdfminer.six->unstructured[pdf]) (2.21)\n", "Collecting huggingface-hub (from unstructured-inference==0.7.10->unstructured[pdf])\n", "  Downloading huggingface_hub-0.17.3-py3-none-any.whl (295 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m295.0/295.0 kB\u001b[0m \u001b[31m26.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting humanfriendly>=9.1 (from coloredlogs->onnxruntime<1.16->unstructured-inference==0.7.10->unstructured[pdf])\n", "  Downloading humanfriendly-10.0-py2.py3-none-any.whl (86 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.8/86.8 kB\u001b[0m \u001b[31m9.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting timm>=0.9.2 (from effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf])\n", "  Downloading timm-0.9.10-py3-none-any.whl (2.2 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.2/2.2 MB\u001b[0m \u001b[31m69.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pycocotools>=2.0.2 in /usr/local/lib/python3.10/dist-packages (from effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf]) (2.0.7)\n", "Collecting omegaconf>=2.0 (from effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf])\n", "  Downloading omegaconf-2.3.0-py3-none-any.whl (79 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m79.5/79.5 kB\u001b[0m \u001b[31m8.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: networkx in /usr/local/lib/python3.10/dist-packages (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf]) (3.2)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf]) (3.1.2)\n", "Requirement already satisfied: triton==2.1.0 in /usr/local/lib/python3.10/dist-packages (from torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf]) (2.1.0)\n", "Collecting portalocker (from iopath->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf])\n", "  Downloading portalocker-2.8.2-py3-none-any.whl (17 kB)\n", "Requirement already satisfied: python-dateutil>=2.8.1 in /usr/local/lib/python3.10/dist-packages (from pandas->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf]) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf]) (2023.3.post1)\n", "Collecting pypdfium2>=4.18.0 (from pdfplumber->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf])\n", "  Downloading pypdfium2-4.23.1-py3-none-manylinux_2_17_x86_64.whl (3.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.0/3.0 MB\u001b[0m \u001b[31m51.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: mpmath>=0.19 in /usr/local/lib/python3.10/dist-packages (from sympy->onnxruntime<1.16->unstructured-inference==0.7.10->unstructured[pdf]) (1.3.0)\n", "Collecting antlr4-python3-runtime==4.9.* (from omegaconf>=2.0->effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf])\n", "  Downloading antlr4-python3-runtime-4.9.3.tar.gz (117 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m117.0/117.0 kB\u001b[0m \u001b[31m11.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: matplotlib>=2.1.0 in /usr/local/lib/python3.10/dist-packages (from pycocotools>=2.0.2->effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf]) (3.7.1)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf]) (2.1.3)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib>=2.1.0->pycocotools>=2.0.2->effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf]) (1.1.1)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.10/dist-packages (from matplotlib>=2.1.0->pycocotools>=2.0.2->effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf]) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.10/dist-packages (from matplotlib>=2.1.0->pycocotools>=2.0.2->effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf]) (4.43.1)\n", "Requirement already satisfied: kiwisolver>=1.0.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib>=2.1.0->pycocotools>=2.0.2->effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf]) (1.4.5)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.10/dist-packages (from matplotlib>=2.1.0->pycocotools>=2.0.2->effdet->layoutparser[layoutmodels,tesseract]->unstructured-inference==0.7.10->unstructured[pdf]) (3.1.1)\n", "Building wheels for collected packages: iopath, antlr4-python3-runtime\n", "  Building wheel for iopath (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for iopath: filename=iopath-0.1.10-py3-none-any.whl size=31530 sha256=d4af088ff361a088ed5e96c438ce9cc8432b8a275d61858de051d67e22baa646\n", "  Stored in directory: /root/.cache/pip/wheels/9a/a3/b6/ac0fcd1b4ed5cfeb3db92e6a0e476cfd48ed0df92b91080c1d\n", "  Building wheel for antlr4-python3-runtime (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for antlr4-python3-runtime: filename=antlr4_python3_runtime-4.9.3-py3-none-any.whl size=144554 sha256=e58adff98e19e0f4d25d0c8c5a8dde588fb58305915e96284f097f2ae037d798\n", "  Stored in directory: /root/.cache/pip/wheels/12/93/dd/1f6a127edc45659556564c5730f6d4e300888f4bca2d4c5a88\n", "Successfully built iopath antlr4-python3-runtime\n", "Installing collected packages: antlr4-python3-runtime, unstructured.pytesseract, safetensors, python-multipart, pytesseract, pypdfium2, portalocker, pdf2image, onnx, omegaconf, humanfriendly, iopath, huggingface-hub, coloredlogs, tokenizers, pdfminer.six, onnxruntime, transformers, timm, pdfplumber, layoutparser, effdet, unstructured-inference\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "lida 0.0.10 requires fastapi, which is not installed.\n", "lida 0.0.10 requires kaleido, which is not installed.\n", "lida 0.0.10 requires uvicorn, which is not installed.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed antlr4-python3-runtime-4.9.3 coloredlogs-15.0.1 effdet-0.4.1 huggingface-hub-0.17.3 humanfriendly-10.0 iopath-0.1.10 layoutparser-0.3.4 omegaconf-2.3.0 onnx-1.15.0 onnxruntime-1.15.1 pdf2image-1.16.3 pdfminer.six-******** pdfplumber-0.10.3 portalocker-2.8.2 pypdfium2-4.23.1 pytesseract-0.3.10 python-multipart-0.0.6 safetensors-0.4.0 timm-0.9.10 tokenizers-0.14.1 transformers-4.35.0 unstructured-inference-0.7.10 unstructured.pytesseract-0.3.12\n"]}, {"output_type": "display_data", "data": {"application/vnd.colab-display-data+json": {"pip_warning": {"packages": ["pydevd_plugins"]}}}, "metadata": {}}]}, {"cell_type": "code", "source": ["from langchain.document_loaders import DirectoryLoader\n", "\n", "loader = DirectoryLoader(\"./data\",glob = \"**/*.pdf\")\n", "data = loader.load()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "noStRcDmCweN", "outputId": "0780dcf3-fc0a-4929-d88e-7badb4bccde5"}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["[nltk_data] Downloading package punkt to /root/nltk_data...\n", "[nltk_data]   Unzipping tokenizers/punkt.zip.\n", "[nltk_data] Downloading package averaged_perceptron_tagger to\n", "[nltk_data]     /root/nltk_data...\n", "[nltk_data]   Unzipping taggers/averaged_perceptron_tagger.zip.\n"]}]}, {"cell_type": "code", "source": ["data"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ZrWdn1M0EdxX", "outputId": "693252cb-6b83-4ab0-ee09-1eea9cea2f57"}, "execution_count": 2, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='You Only Look Once (YOLO): Unified, Real-Time Object Detection\\n\\nPresenter: <PERSON><PERSON>\\n\\nSept 2nd, 2021\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n1\\n\\nProblem Addressed: Object Detection\\n\\n❖ Object detection is the problem of both\\n\\nlocating AND classifying objects\\n\\n❖ Goal of YOLO algorithm is to do object\\n\\ndetection both fast AND with high\\n\\naccuracy\\n\\n“Deep Learning for Vision Systems” (Elgendy)\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\nObject Detection vs Classification\\n\\n2\\n\\nImportance of Object Detection for Robotics\\n\\n❖ Visual modality is very powerful\\n\\n❖ Humans are able to detect objects and do\\n\\nVision based vs LIDAR (self driving)\\n\\nperception using just this modality in real time\\n\\n(not needing radar)\\n\\n❖ If we want responsive robot systems that\\n\\nwork in real time (without specialized\\n\\nsensors) almost real time vision based object\\n\\ndetection can help greatly\\n\\nTesla Investor Day Presentation\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n3\\n\\nPrevious Object Detection Paradigm This pipeline was used in nearly all SOTA Object Detection prior:\\n\\nLabel + confidence\\n\\nImage Classifier\\n\\nhat - 0.92 racket - 0.2 ball - 0.23\\n\\nStep 1: Scan the image to generate candidate bounding boxes\\n\\nStep 2: Run the bounding box through a classifier\\n\\nStep 3: Conduct post-processing (filtering out redundant bounding boxes)\\n\\nDiagram developed by presenter\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n4\\n\\nKey Insights\\n\\nPrevious Approaches\\n\\n❖ A separate model for generating\\n\\nbounding boxes and for classification\\n\\n(more complicated model pipeline)\\n\\n❖ Need to run classification many\\n\\ntimes (expensive computation)\\n\\n❖ Looks at limited part of the image\\n\\n(lacks contextual information for\\n\\ndetection)\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\nYOLO algorithm\\n\\n❖ A single neural network for\\n\\nlocalization and for classification\\n\\n(less complicated pipeline)\\n\\n❖ Need to inference only once\\n\\n(efficient computation)\\n\\n❖ Looks at the entire image each time\\n\\nleading to less false positives (has\\n\\ncontextual information for detection)\\n\\n5\\n\\nFormal Problem Setting\\n\\n❖ Given an image generate bounding boxes, one for\\n\\neach detectable object in image\\n\\n❖ For each bounding box, output 5 predictions: x, y, w,\\n\\nh, confidence. Also output class\\n\\n❖ x, y (coordinates for center of bounding box)\\n\\n❖ w,h (width and height)\\n\\n❖ confidence (probability bounding box has object)\\n\\n❖ class (classification of object in bounding box)\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n6\\n\\nRelated Work\\n\\nR-CNN or Region Based Convolutional Network (Girshick et al. 2014):\\n\\n\\n\\n-\\n\\nUsed the sliding window approach from earlier, with Selective Search, a smarter way to select candidates (which means there is less computation) Still feeds a limited part of the image to the classifier Drawbacks: Large pipeline, slow, too many false positives\\n\\nFast and Faster R-CNN:\\n\\nOptimize parts of the pipeline described earlier - Drawbacks: loses accuracy\\n\\nDeep Multibox (Szegedy et. al 2014):\\n\\n-\\n\\nTrain a CNN to find areas of interest Drawbacks: Doesn’t address classification only localization\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n7\\n\\nRelated Work\\n\\nMultiGrasp (Redmon et. al 2014)\\n\\nSimilar to YOLO - A much simpler task (only needs to predict object not multiple objects)\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n8\\n\\nYOLO overview\\n\\n❖ First, image is split into a SxS grid ❖ For each grid square, generate B bounding boxes ❖ For each bounding box, there are 5 predictions: x, y, w, h,\\n\\nconfidence\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\nS = 3, B = 2\\n\\n9\\n\\nYOLO Training\\n\\n❖ YOLO is a regression algorithm. What is\\n\\nX? What is Y?\\n\\n❖ X is simple, just an image width (in\\n\\npixels) * height (in pixels) * RGB values ❖ Y is a tensor of size S * S * (B * 5 + C) ❖ B*5 + C term represents the predictions\\n\\n+ class predicted distribution for a grid\\n\\nblock\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\nFor each grid block, we have a\\n\\nvector like this. For this example\\n\\nB is 2 and C is 2\\n\\nGT label\\n\\nexample:\\n\\n10\\n\\nYOLO Architecture\\n\\nNow that we know the input and output, we can discuss the model\\n\\nWe are given 448 by 448 by 3 as\\n\\n\\n\\nour input. Implementation uses 7 convolution layers\\n\\nPaper parameters: S = 7, B = 2,\\n\\nC = 20\\n\\nOutput is S*S*(5B+C) = 7*7*(5*2+20) = 7*7*30\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n11\\n\\nYOLO Prediction\\n\\n❖ We then use the output to make final detections ❖ Use a threshold to filter out bounding boxes with\\n\\nlow P(Object)\\n\\n❖ In order to know the class for the bounding box\\n\\ncompute score take argmax over the distribution\\n\\nPr(Class|Object) for the grid the bounding box’s\\n\\ncenter is in\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n12\\n\\nNon-maximal suppression\\n\\n❖ Most of the time objects fall in one grid,\\n\\nhowever it is still possible to get redundant boxes (rare case as object must be close to multiple grid cells for this to happen) ❖ Discard bounding box with high overlap (keeping the bounding box with highest confidence)\\n\\n❖ Adds 2-3% on final mAP score\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n13\\n\\nYOLO Objective Function\\n\\n❖ For YOLO, we need to minimize the following loss ❖ Sum squared error is used\\n\\nCoordinate Loss: Minimize the difference between x,y,w,h pred and x,y,w,h ground truth. ONLY IF object exists in grid box and if bounding box is resp for pred\\n\\nConfidence Loss: Loss based on confidence ONLY IF there is object\\n\\nNo Object Loss based on confidence if there is no object\\n\\nClass loss, minimize loss between true class of object in grid box\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n14\\n\\nExperimental Setup\\n\\n❖ Authors compare YOLO against the previous work described above on PASCAL VOC 2007, and\\n\\nVOC 2012 as well as out of domain art dataset\\n\\n❖ Correct if IOU metric above .5 and class is correct\\n\\n❖ Use two performance metrics:\\n\\n➢ mAP score: mean average precision\\n\\n➢ FPS:\\n\\nframes per second\\n\\n❖ Add FAST YOLO: which has less parameters\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n15\\n\\nExperimental Results\\n\\n❖ Baseline YOLO outperform\\n\\nreal time detectors by large\\n\\namount\\n\\n❖ Do better than most less than\\n\\nreal time as well\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n16\\n\\nExperimental Results\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n17\\n\\nExperimental Results - Error Analysis Exper\\n\\nMakes far less background errors (less likely to predict false positives on background) IOU is VERY small with any ground truth label\\n\\n\\n\\nBut far more localization errors\\n\\n\\n\\nCorrect class, IOU is somewhat small\\n\\nLocalization error\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\nBackground error\\n\\n18\\n\\nExperimental Results - Out of Domain\\n\\n❖ Ran YOLO + competitors\\n\\n(trained on natural images) on art\\n\\n❖ Does well on artistic datasets where more having global context greatly helps\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n19\\n\\nDiscussion of Results\\n\\n❖ Pro: YOLO is a lot faster than the other algorithms for image detection\\n\\n❖ Pro: YOLO’s use of global information rather than only local information allows it to understand\\n\\ncontextual information when doing object detection\\n\\n➢ Does better in domains such as artwork due to this\\n\\n❖ Con: YOLO lagged behind the SOTA models in object detection\\n\\n➢ This is attributed to making many localization errors and unable to detect small object\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n20\\n\\nCritique / Limitations / Open Issues\\n\\n❖ Performance lags behind SOTA\\n\\n❖ Requires data to be labeled with bounding boxes, hard to collect for many classes\\n\\n➢ Previous work could generalize better since it used image classifier\\n\\n➢ 2014 COCO dataset (very large dataset) addressed this somewhat\\n\\n❖ Regarding experiments: number of classes predicted is very limited\\n\\n➢ Not convinced that YOLO v1 is generalizable\\n\\n❖ Confidence output of YOLO not confidence of class but P(Object), lowers interpretability\\n\\n❖ Another limitation of YOLO is that it imposed spatial constraints on the objects in the image since\\n\\nonly B boxes can be predicted on an SxS grid\\n\\n❖ Since the architecture only predicts boxes, this might make it less useful for irregular shapes\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n21\\n\\nFuture Work for Paper / Reading\\n\\n❖ One extension of this work would be to look\\n\\nat image segmentation and see if the insights\\n\\ncarry over\\n\\nYOLOACT (Boyla et al 2019): Real\\n\\ntime image segmentation ❖ YOLO has been upgraded 2 times\\n\\nSolves a lot of issues relating to\\n\\ndetecting small objects,\\n\\ngeneralizability, and localization\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\nYOLOACT example\\n\\n22\\n\\nExtended Readings\\n\\n❖ YOLO v2 (https://arxiv.org/abs/1506.02640) (extends on the work greatly) (Redmond et al 2016)\\n\\n➢ Deals with the generalizability problem, has 9000 classes\\n\\n➢ Class probability distribution per bounding box, not per grid\\n\\n➢ High resolution classifier (finetune on high resolution)\\n\\n➢ Batch norm\\n\\n➢ Trained on MSCOCO (released after YOLO v1 paper)\\n\\n❖ YOLO v3 (https://arxiv.org/abs/1804.02767)\\n\\n➢ “Incremental Improvement”\\n\\n➢ Uses independent logistic classifiers for class\\n\\n■ Allows for more specificity in classes\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n23\\n\\nSummary\\n\\n❖ Object detection is the problem of detecting multiple objects in an image ❖ Almost real time object detection can make highly responsive robot systems without complex sensors ❖ Prior work relies on a large architecture with numerous parts to optimize ❖ YOLO proposes a unified architecture, which does all the tasks in one model and by one inference\\n\\nover the entire image\\n\\n❖ They show enormous speed improvement and show that they can beat most other prior work in terms\\n\\nof mAPs\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n24', metadata={'source': 'data/yolo.pdf'})]"]}, "metadata": {}, "execution_count": 2}]}, {"cell_type": "markdown", "source": ["## Text Splitting"], "metadata": {"id": "Ig_Tc37GEhV_"}}, {"cell_type": "code", "source": ["from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=20)\n", "docs = text_splitter.split_documents(data)"], "metadata": {"id": "v28l9Y3lEdvH"}, "execution_count": 3, "outputs": []}, {"cell_type": "code", "source": ["docs"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "NVSqLB3GEds-", "outputId": "fe49a831-e04c-4795-93e9-f1e9de81f538"}, "execution_count": 4, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='You Only Look Once (YOLO): Unified, Real-Time Object Detection\\n\\nPresenter: <PERSON><PERSON>\\n\\nSept 2nd, 2021\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n1\\n\\nProblem Addressed: Object Detection\\n\\n❖ Object detection is the problem of both\\n\\nlocating AND classifying objects\\n\\n❖ Goal of YOLO algorithm is to do object\\n\\ndetection both fast AND with high\\n\\naccuracy\\n\\n“Deep Learning for Vision Systems” (Elgendy)\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\nObject Detection vs Classification\\n\\n2\\n\\nImportance of Object Detection for Robotics\\n\\n❖ Visual modality is very powerful\\n\\n❖ Humans are able to detect objects and do\\n\\nVision based vs LIDAR (self driving)\\n\\nperception using just this modality in real time\\n\\n(not needing radar)\\n\\n❖ If we want responsive robot systems that\\n\\nwork in real time (without specialized\\n\\nsensors) almost real time vision based object\\n\\ndetection can help greatly\\n\\nTesla Investor Day Presentation\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n3', metadata={'source': 'data/yolo.pdf'}),\n", " Document(page_content='3\\n\\nPrevious Object Detection Paradigm This pipeline was used in nearly all SOTA Object Detection prior:\\n\\nLabel + confidence\\n\\nImage Classifier\\n\\nhat - 0.92 racket - 0.2 ball - 0.23\\n\\nStep 1: Scan the image to generate candidate bounding boxes\\n\\nStep 2: Run the bounding box through a classifier\\n\\nStep 3: Conduct post-processing (filtering out redundant bounding boxes)\\n\\nDiagram developed by presenter\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n4\\n\\nKey Insights\\n\\nPrevious Approaches\\n\\n❖ A separate model for generating\\n\\nbounding boxes and for classification\\n\\n(more complicated model pipeline)\\n\\n❖ Need to run classification many\\n\\ntimes (expensive computation)\\n\\n❖ Looks at limited part of the image\\n\\n(lacks contextual information for\\n\\ndetection)\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\nYOLO algorithm\\n\\n❖ A single neural network for\\n\\nlocalization and for classification\\n\\n(less complicated pipeline)\\n\\n❖ Need to inference only once\\n\\n(efficient computation)\\n\\n❖ Looks at the entire image each time', metadata={'source': 'data/yolo.pdf'}),\n", " Document(page_content='leading to less false positives (has\\n\\ncontextual information for detection)\\n\\n5\\n\\nFormal Problem Setting\\n\\n❖ Given an image generate bounding boxes, one for\\n\\neach detectable object in image\\n\\n❖ For each bounding box, output 5 predictions: x, y, w,\\n\\nh, confidence. Also output class\\n\\n❖ x, y (coordinates for center of bounding box)\\n\\n❖ w,h (width and height)\\n\\n❖ confidence (probability bounding box has object)\\n\\n❖ class (classification of object in bounding box)\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n6\\n\\nRelated Work\\n\\nR-CNN or Region Based Convolutional Network (G<PERSON>hic<PERSON> et al. 2014):\\n\\n\\n\\n-\\n\\nUsed the sliding window approach from earlier, with Selective Search, a smarter way to select candidates (which means there is less computation) Still feeds a limited part of the image to the classifier Drawbacks: Large pipeline, slow, too many false positives\\n\\nFast and Faster R-CNN:\\n\\nOptimize parts of the pipeline described earlier - Drawbacks: loses accuracy\\n\\nDeep Multibox (<PERSON><PERSON><PERSON><PERSON> et. al 2014):\\n\\n-', metadata={'source': 'data/yolo.pdf'}),\n", " Document(page_content='-\\n\\nTrain a CNN to find areas of interest Drawbacks: Doesn’t address classification only localization\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n7\\n\\nRelated Work\\n\\nMultiGrasp (<PERSON><PERSON> et. al 2014)\\n\\nSimilar to YOLO - A much simpler task (only needs to predict object not multiple objects)\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n8\\n\\nYOLO overview\\n\\n❖ First, image is split into a SxS grid ❖ For each grid square, generate B bounding boxes ❖ For each bounding box, there are 5 predictions: x, y, w, h,\\n\\nconfidence\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\nS = 3, B = 2\\n\\n9\\n\\nYOLO Training\\n\\n❖ YOLO is a regression algorithm. What is\\n\\nX? What is Y?\\n\\n❖ X is simple, just an image width (in\\n\\npixels) * height (in pixels) * RGB values ❖ Y is a tensor of size S * S * (B * 5 + C) ❖ B*5 + C term represents the predictions\\n\\n+ class predicted distribution for a grid\\n\\nblock\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\nFor each grid block, we have a\\n\\nvector like this. For this example\\n\\nB is 2 and C is 2\\n\\nGT label\\n\\nexample:\\n\\n10', metadata={'source': 'data/yolo.pdf'}),\n", " Document(page_content='example:\\n\\n10\\n\\nYOLO Architecture\\n\\nNow that we know the input and output, we can discuss the model\\n\\nWe are given 448 by 448 by 3 as\\n\\n\\n\\nour input. Implementation uses 7 convolution layers\\n\\nPaper parameters: S = 7, B = 2,\\n\\nC = 20\\n\\nOutput is S*S*(5B+C) = 7*7*(5*2+20) = 7*7*30\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n11\\n\\nYOLO Prediction\\n\\n❖ We then use the output to make final detections ❖ Use a threshold to filter out bounding boxes with\\n\\nlow P(Object)\\n\\n❖ In order to know the class for the bounding box\\n\\ncompute score take argmax over the distribution\\n\\nPr(Class|Object) for the grid the bounding box’s\\n\\ncenter is in\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n12\\n\\nNon-maximal suppression\\n\\n❖ Most of the time objects fall in one grid,\\n\\nhowever it is still possible to get redundant boxes (rare case as object must be close to multiple grid cells for this to happen) ❖ Discard bounding box with high overlap (keeping the bounding box with highest confidence)\\n\\n❖ Adds 2-3% on final mAP score', metadata={'source': 'data/yolo.pdf'}),\n", " Document(page_content='CS391R: Robot Learning (Fall 2021)\\n\\n13\\n\\nYOLO Objective Function\\n\\n❖ For YOLO, we need to minimize the following loss ❖ Sum squared error is used\\n\\nCoordinate Loss: Minimize the difference between x,y,w,h pred and x,y,w,h ground truth. ONLY IF object exists in grid box and if bounding box is resp for pred\\n\\nConfidence Loss: Loss based on confidence ONLY IF there is object\\n\\nNo Object Loss based on confidence if there is no object\\n\\nClass loss, minimize loss between true class of object in grid box\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n14\\n\\nExperimental Setup\\n\\n❖ Authors compare YOLO against the previous work described above on PASCAL VOC 2007, and\\n\\nVOC 2012 as well as out of domain art dataset\\n\\n❖ Correct if IOU metric above .5 and class is correct\\n\\n❖ Use two performance metrics:\\n\\n➢ mAP score: mean average precision\\n\\n➢ FPS:\\n\\nframes per second\\n\\n❖ Add FAST YOLO: which has less parameters\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n15\\n\\nExperimental Results\\n\\n❖ Baseline YOLO outperform', metadata={'source': 'data/yolo.pdf'}),\n", " Document(page_content='real time detectors by large\\n\\namount\\n\\n❖ Do better than most less than\\n\\nreal time as well\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n16\\n\\nExperimental Results\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n17\\n\\nExperimental Results - Error Analysis Exper\\n\\nMakes far less background errors (less likely to predict false positives on background) IOU is VERY small with any ground truth label\\n\\n\\n\\nBut far more localization errors\\n\\n\\n\\nCorrect class, IOU is somewhat small\\n\\nLocalization error\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\nBackground error\\n\\n18\\n\\nExperimental Results - Out of Domain\\n\\n❖ Ran YOLO + competitors\\n\\n(trained on natural images) on art\\n\\n❖ Does well on artistic datasets where more having global context greatly helps\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n19\\n\\nDiscussion of Results\\n\\n❖ Pro: YOLO is a lot faster than the other algorithms for image detection\\n\\n❖ Pro: YOLO’s use of global information rather than only local information allows it to understand\\n\\ncontextual information when doing object detection', metadata={'source': 'data/yolo.pdf'}),\n", " Document(page_content='➢ Does better in domains such as artwork due to this\\n\\n❖ Con: YOLO lagged behind the SOTA models in object detection\\n\\n➢ This is attributed to making many localization errors and unable to detect small object\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n20\\n\\nCritique / Limitations / Open Issues\\n\\n❖ Performance lags behind SOTA\\n\\n❖ Requires data to be labeled with bounding boxes, hard to collect for many classes\\n\\n➢ Previous work could generalize better since it used image classifier\\n\\n➢ 2014 COCO dataset (very large dataset) addressed this somewhat\\n\\n❖ Regarding experiments: number of classes predicted is very limited\\n\\n➢ Not convinced that YOLO v1 is generalizable\\n\\n❖ Confidence output of YOLO not confidence of class but P(Object), lowers interpretability\\n\\n❖ Another limitation of YOLO is that it imposed spatial constraints on the objects in the image since\\n\\nonly B boxes can be predicted on an SxS grid\\n\\n❖ Since the architecture only predicts boxes, this might make it less useful for irregular shapes', metadata={'source': 'data/yolo.pdf'}),\n", " Document(page_content='CS391R: Robot Learning (Fall 2021)\\n\\n21\\n\\nFuture Work for Paper / Reading\\n\\n❖ One extension of this work would be to look\\n\\nat image segmentation and see if the insights\\n\\ncarry over\\n\\nYOLOACT (<PERSON><PERSON> et al 2019): Real\\n\\ntime image segmentation ❖ YOLO has been upgraded 2 times\\n\\nSolves a lot of issues relating to\\n\\ndetecting small objects,\\n\\ngeneralizability, and localization\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\nYOLOACT example\\n\\n22\\n\\nExtended Readings\\n\\n❖ YOLO v2 (https://arxiv.org/abs/1506.02640) (extends on the work greatly) (<PERSON><PERSON> et al 2016)\\n\\n➢ Deals with the generalizability problem, has 9000 classes\\n\\n➢ Class probability distribution per bounding box, not per grid\\n\\n➢ High resolution classifier (finetune on high resolution)\\n\\n➢ Batch norm\\n\\n➢ Trained on MSCOCO (released after YOLO v1 paper)\\n\\n❖ YOLO v3 (https://arxiv.org/abs/1804.02767)\\n\\n➢ “Incremental Improvement”\\n\\n➢ Uses independent logistic classifiers for class\\n\\n■ Allows for more specificity in classes\\n\\nCS391R: Robot Learning (Fall 2021)', metadata={'source': 'data/yolo.pdf'}),\n", " Document(page_content='23\\n\\nSummary\\n\\n❖ Object detection is the problem of detecting multiple objects in an image ❖ Almost real time object detection can make highly responsive robot systems without complex sensors ❖ Prior work relies on a large architecture with numerous parts to optimize ❖ YOLO proposes a unified architecture, which does all the tasks in one model and by one inference\\n\\nover the entire image\\n\\n❖ They show enormous speed improvement and show that they can beat most other prior work in terms\\n\\nof mAPs\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n24', metadata={'source': 'data/yolo.pdf'})]"]}, "metadata": {}, "execution_count": 4}]}, {"cell_type": "code", "source": ["len(docs)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "H0du36H1EmR9", "outputId": "cd0c25ab-6a58-49c2-ceef-8c944021581c"}, "execution_count": 5, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["10"]}, "metadata": {}, "execution_count": 5}]}, {"cell_type": "markdown", "source": ["## Embedding Convertion"], "metadata": {"id": "KAWoYPCCEr-P"}}, {"cell_type": "code", "source": ["from langchain.embeddings.openai import OpenAIEmbeddings\n", "\n", "embeddings = OpenAIEmbeddings(openai_api_key= OPENAI_API_KEY)"], "metadata": {"id": "U13DhBHdEoLf"}, "execution_count": 8, "outputs": []}, {"cell_type": "code", "source": ["embeddings"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "3t7wpOMwF93g", "outputId": "3fb54c60-4bbb-4f51-c35e-506e30d1c139"}, "execution_count": 11, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["OpenAIEmbeddings(client=<class 'openai.api_resources.embedding.Embedding'>, model='text-embedding-ada-002', deployment='text-embedding-ada-002', openai_api_version='', openai_api_base='', openai_api_type='', openai_proxy='', embedding_ctx_length=8191, openai_api_key='***************************************************', openai_organization='', allowed_special=set(), disallowed_special='all', chunk_size=1000, max_retries=6, request_timeout=None, headers=None, tiktoken_model_name=None, show_progress_bar=False, model_kwargs={}, skip_empty=False)"]}, "metadata": {}, "execution_count": 11}]}, {"cell_type": "markdown", "source": ["## Vector Database Storage"], "metadata": {"id": "Pa6pqWXVE5b2"}}, {"cell_type": "code", "source": ["import weaviate\n", "from langchain.vectorstores import Weaviate\n", "\n", "#Connect to weaviate Cluster\n", "auth_config = weaviate.auth.AuthApiKey(api_key = WEAVIATE_API_KEY)\n", "WEAVIATE_URL = WEAVIATE_CLUSTER\n", "\n", "client = weaviate.Client(\n", "    url = WEAVIATE_URL,\n", "    additional_headers = {\"X-OpenAI-Api-key\": OPENAI_API_KEY},\n", "    auth_client_secret = auth_config,\n", "    startup_period = 10\n", ")"], "metadata": {"id": "gu-4tnGSE3H2"}, "execution_count": 9, "outputs": []}, {"cell_type": "code", "source": ["client.is_ready()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zLrjVzwkE3Fv", "outputId": "65908f1e-1345-4ecf-e2fb-55209c94069d"}, "execution_count": 10, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["True"]}, "metadata": {}, "execution_count": 10}]}, {"cell_type": "code", "source": ["# define input structure\n", "client.schema.delete_all()\n", "client.schema.get()\n", "schema = {\n", "    \"classes\": [\n", "        {\n", "            \"class\": \"Chatbot\",\n", "            \"description\": \"Documents for chatbot\",\n", "            \"vectorizer\": \"text2vec-openai\",\n", "            \"moduleConfig\": {\"text2vec-openai\": {\"model\": \"ada\", \"type\": \"text\"}},\n", "            \"properties\": [\n", "                {\n", "                    \"dataType\": [\"text\"],\n", "                    \"description\": \"The content of the paragraph\",\n", "                    \"moduleConfig\": {\n", "                        \"text2vec-openai\": {\n", "                            \"skip\": <PERSON><PERSON><PERSON>,\n", "                            \"vectorizePropertyName\": <PERSON><PERSON><PERSON>,\n", "                        }\n", "                    },\n", "                    \"name\": \"content\",\n", "                },\n", "            ],\n", "        },\n", "    ]\n", "}\n", "\n", "client.schema.create(schema)\n", "vectorstore = Weaviate(client, \"Chatbot\", \"content\", attributes=[\"source\"])"], "metadata": {"id": "-8pusC7QFWUf"}, "execution_count": 12, "outputs": []}, {"cell_type": "code", "source": ["# load text into the vectorstore\n", "text_meta_pair = [(doc.page_content, doc.metadata) for doc in docs]\n", "texts, meta = list(zip(*text_meta_pair))\n", "vectorstore.add_texts(texts, meta)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mAAP1K88GDXW", "outputId": "d4f0eb14-740d-4f05-8080-dc391269cb81"}, "execution_count": 13, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['9c496404-7515-4b3c-8b8e-88e2dc09dfc8',\n", " '0c6883df-8242-4011-a51d-13efd4d6dd39',\n", " '3ad7f0e4-d5b9-4ffc-9fb0-1d4a4b31f610',\n", " '4845d89b-4e65-4ec3-ac46-e182793e9b68',\n", " '32df9a02-fdf1-4e71-81b5-1103264f7499',\n", " '6e2be198-6bb7-4374-8bf8-bc3f42aefd17',\n", " '5074cbc0-870c-416e-a37c-63a8f28a06bd',\n", " '59fd3d13-29f3-4001-af59-7fc48a4e1bec',\n", " 'cb52baa4-bd78-4491-801e-21b074f6aa8d',\n", " '631ccd1a-e35d-4eb4-94ae-8beb3c1f99e4']"]}, "metadata": {}, "execution_count": 13}]}, {"cell_type": "markdown", "source": ["## Similarity Measurement"], "metadata": {"id": "qMNy5RQ9Gjn1"}}, {"cell_type": "code", "source": ["query = \"what is a yolo?\"\n", "\n", "# retrieve text related to the query\n", "docs = vectorstore.similarity_search(query, top_k=20)"], "metadata": {"id": "BNFmkvRsGDVI"}, "execution_count": 14, "outputs": []}, {"cell_type": "code", "source": ["docs"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "KJ8RTLX0GrFe", "outputId": "c21e2a31-6143-466e-9ef6-98e3f6600c9f"}, "execution_count": 15, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='You Only Look Once (YOLO): Unified, Real-Time Object Detection\\n\\nPresenter: <PERSON><PERSON>\\n\\nSept 2nd, 2021\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n1\\n\\nProblem Addressed: Object Detection\\n\\n❖ Object detection is the problem of both\\n\\nlocating AND classifying objects\\n\\n❖ Goal of YOLO algorithm is to do object\\n\\ndetection both fast AND with high\\n\\naccuracy\\n\\n“Deep Learning for Vision Systems” (Elgendy)\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\nObject Detection vs Classification\\n\\n2\\n\\nImportance of Object Detection for Robotics\\n\\n❖ Visual modality is very powerful\\n\\n❖ Humans are able to detect objects and do\\n\\nVision based vs LIDAR (self driving)\\n\\nperception using just this modality in real time\\n\\n(not needing radar)\\n\\n❖ If we want responsive robot systems that\\n\\nwork in real time (without specialized\\n\\nsensors) almost real time vision based object\\n\\ndetection can help greatly\\n\\nTesla Investor Day Presentation\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n3', metadata={'source': 'data/yolo.pdf'}),\n", " Document(page_content='23\\n\\nSummary\\n\\n❖ Object detection is the problem of detecting multiple objects in an image ❖ Almost real time object detection can make highly responsive robot systems without complex sensors ❖ Prior work relies on a large architecture with numerous parts to optimize ❖ YOLO proposes a unified architecture, which does all the tasks in one model and by one inference\\n\\nover the entire image\\n\\n❖ They show enormous speed improvement and show that they can beat most other prior work in terms\\n\\nof mAPs\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n24', metadata={'source': 'data/yolo.pdf'}),\n", " Document(page_content='CS391R: Robot Learning (Fall 2021)\\n\\n13\\n\\nYOLO Objective Function\\n\\n❖ For YOLO, we need to minimize the following loss ❖ Sum squared error is used\\n\\nCoordinate Loss: Minimize the difference between x,y,w,h pred and x,y,w,h ground truth. ONLY IF object exists in grid box and if bounding box is resp for pred\\n\\nConfidence Loss: Loss based on confidence ONLY IF there is object\\n\\nNo Object Loss based on confidence if there is no object\\n\\nClass loss, minimize loss between true class of object in grid box\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n14\\n\\nExperimental Setup\\n\\n❖ Authors compare YOLO against the previous work described above on PASCAL VOC 2007, and\\n\\nVOC 2012 as well as out of domain art dataset\\n\\n❖ Correct if IOU metric above .5 and class is correct\\n\\n❖ Use two performance metrics:\\n\\n➢ mAP score: mean average precision\\n\\n➢ FPS:\\n\\nframes per second\\n\\n❖ Add FAST YOLO: which has less parameters\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n15\\n\\nExperimental Results\\n\\n❖ Baseline YOLO outperform', metadata={'source': 'data/yolo.pdf'}),\n", " Document(page_content='➢ Does better in domains such as artwork due to this\\n\\n❖ Con: YOLO lagged behind the SOTA models in object detection\\n\\n➢ This is attributed to making many localization errors and unable to detect small object\\n\\nCS391R: Robot Learning (Fall 2021)\\n\\n20\\n\\nCritique / Limitations / Open Issues\\n\\n❖ Performance lags behind SOTA\\n\\n❖ Requires data to be labeled with bounding boxes, hard to collect for many classes\\n\\n➢ Previous work could generalize better since it used image classifier\\n\\n➢ 2014 COCO dataset (very large dataset) addressed this somewhat\\n\\n❖ Regarding experiments: number of classes predicted is very limited\\n\\n➢ Not convinced that YOLO v1 is generalizable\\n\\n❖ Confidence output of YOLO not confidence of class but P(Object), lowers interpretability\\n\\n❖ Another limitation of YOLO is that it imposed spatial constraints on the objects in the image since\\n\\nonly B boxes can be predicted on an SxS grid\\n\\n❖ Since the architecture only predicts boxes, this might make it less useful for irregular shapes', metadata={'source': 'data/yolo.pdf'})]"]}, "metadata": {}, "execution_count": 15}]}, {"cell_type": "code", "source": ["from langchain.chains.question_answering import load_qa_chain\n", "from langchain.llms import OpenAI"], "metadata": {"id": "SAiHxgysGrWX"}, "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["# define chain\n", "chain = load_qa_chain(\n", "    OpenAI(openai_api_key = OPENAI_API_KEY,temperature=0),\n", "    chain_type=\"stuff\")"], "metadata": {"id": "GDfNPBH0GxE_"}, "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": ["# create answer\n", "chain.run(input_documents=docs, question=query)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 53}, "id": "9wThVt_yG6tO", "outputId": "b8d886fa-9ad9-406a-9212-69d4ecf89544"}, "execution_count": 18, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["' YOLO is an algorithm for object detection that is unified, real-time, and has high accuracy. It is presented by <PERSON><PERSON> in the CS391R: Robot Learning (Fall 2021) course on Sept 2nd, 2021.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 18}]}]}