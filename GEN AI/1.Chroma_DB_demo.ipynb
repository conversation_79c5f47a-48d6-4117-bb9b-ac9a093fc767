{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-Db7sd4MprR1", "outputId": "f8422385-ee61-45c4-eb8d-fb1f4bb7d722"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["OK!\n"]}], "source": ["print(\"OK!\")"]}, {"cell_type": "code", "source": ["!pip -q install chromadb openai langchain tiktoken"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jzD_ylViqHRd", "outputId": "11f4abe4-ae84-455c-ae92-3c8b16b537a8"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/479.8 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[91m━━━━━━━━━━\u001b[0m\u001b[90m╺\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m122.9/479.8 kB\u001b[0m \u001b[31m3.5 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m479.8/479.8 kB\u001b[0m \u001b[31m7.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.0/77.0 kB\u001b[0m \u001b[31m8.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m45.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m65.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.4/2.4 MB\u001b[0m \u001b[31m80.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m92.9/92.9 kB\u001b[0m \u001b[31m10.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m59.6/59.6 kB\u001b[0m \u001b[31m6.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.4/5.4 MB\u001b[0m \u001b[31m30.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.2/6.2 MB\u001b[0m \u001b[31m93.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m57.5/57.5 kB\u001b[0m \u001b[31m5.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m103.9/103.9 kB\u001b[0m \u001b[31m11.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.8/3.8 MB\u001b[0m \u001b[31m96.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m67.3/67.3 kB\u001b[0m \u001b[31m7.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n", "  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m593.7/593.7 kB\u001b[0m \u001b[31m49.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.6/1.6 MB\u001b[0m \u001b[31m72.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m44.5/44.5 kB\u001b[0m \u001b[31m4.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.4/49.4 kB\u001b[0m \u001b[31m5.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m67.0/67.0 kB\u001b[0m \u001b[31m7.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m143.8/143.8 kB\u001b[0m \u001b[31m15.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m46.0/46.0 kB\u001b[0m \u001b[31m5.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m50.8/50.8 kB\u001b[0m \u001b[31m5.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m295.0/295.0 kB\u001b[0m \u001b[31m27.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m5.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m341.4/341.4 kB\u001b[0m \u001b[31m34.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.4/3.4 MB\u001b[0m \u001b[31m95.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.3/1.3 MB\u001b[0m \u001b[31m72.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m130.2/130.2 kB\u001b[0m \u001b[31m16.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.8/86.8 kB\u001b[0m \u001b[31m9.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Building wheel for pypika (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "lida 0.0.10 requires kaleido, which is not installed.\n", "lida 0.0.10 requires python-multipart, which is not installed.\n", "llmx 0.0.15a0 requires cohere, which is not installed.\n", "tensorflow-probability 0.22.0 requires typing-extensions<4.6.0, but you have typing-extensions 4.8.0 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0m"]}]}, {"cell_type": "code", "source": ["!pip show chromadb"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qZJnhMAzqdHt", "outputId": "********-1fb3-4c77-96f9-aae8bc422856"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Name: chromadb\n", "Version: 0.4.15\n", "Summary: Chroma.\n", "Home-page: \n", "Author: \n", "Author-email: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\n", "License: \n", "Location: /usr/local/lib/python3.10/dist-packages\n", "Requires: bcrypt, chroma-hnswlib, fastapi, grpcio, importlib-resources, kubernetes, numpy, onnxruntime, opentelemetry-api, opentelemetry-exporter-otlp-proto-grpc, opentelemetry-sdk, overrides, posthog, pulsar-client, pydantic, pypika, requests, tenacity, tokenizers, tqdm, typer, typing-extensions, uvicorn\n", "Required-by: \n"]}]}, {"cell_type": "code", "source": ["!wget -q https://www.dropbox.com/s/vs6ocyvpzzncvwh/new_articles.zip"], "metadata": {"id": "cH6uqRZSqsON"}, "execution_count": 4, "outputs": []}, {"cell_type": "code", "source": ["!unzip -q new_articles.zip -d new_articles"], "metadata": {"id": "6BJJ51qbrSGP"}, "execution_count": 5, "outputs": []}, {"cell_type": "markdown", "source": ["## Setting up Environment"], "metadata": {"id": "1cmkL7vlsCgk"}}, {"cell_type": "code", "source": ["import os\n", "\n", "os.environ['OPENAI_API_KEY'] = \"***************************************************\""], "metadata": {"id": "SFJM3znisBuM"}, "execution_count": 6, "outputs": []}, {"cell_type": "markdown", "source": ["## Import some libraries"], "metadata": {"id": "7_F76skAsWI8"}}, {"cell_type": "code", "source": ["from langchain.vectorstores import Chroma\n", "from langchain.embeddings import OpenAIEmbeddings\n", "from langchain.llms import OpenAI\n", "from langchain.document_loaders import DirectoryLoader\n", "from langchain.document_loaders import TextLoader"], "metadata": {"id": "oShJCP_-sTP1"}, "execution_count": 7, "outputs": []}, {"cell_type": "markdown", "source": ["## Load data"], "metadata": {"id": "koOlGaoMt6N1"}}, {"cell_type": "code", "source": ["loader = DirectoryLoader(\"/content/new_articles/\", glob = \"./*.txt\", loader_cls= TextLoader)"], "metadata": {"id": "fIUfmg34t36V"}, "execution_count": 8, "outputs": []}, {"cell_type": "code", "source": ["document = loader.load()"], "metadata": {"id": "4PbOvt1uujSO"}, "execution_count": 9, "outputs": []}, {"cell_type": "code", "source": ["document"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "inwfWQSwunic", "outputId": "5d49bb32-4bfe-4ec5-ef57-f9a5e0c9ad24"}, "execution_count": 10, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='Google I/O 2023 is next week; here’s what we’re expecting A whole bunch of new hardware, coupled with a lot of AI and the best look yet at Android 14\\n\\nGoogle’s annual developer conference, Google I/O, returns to Mountain View’s Shoreline Amphitheater next week, and for the first time in four years, we’ll be returning along with it. The kickoff keynote is always jammed-packed full of information, debuting all of the different software projects the company has been working on for the past year.\\n\\nUpdate: Google just went ahead and announced the Pixel Fold over on Twitter. The company gave a good look at the upcoming foldable smartphone from just about every angle. That means all three of the expected pieces of hardware – including the Pixel 7a and Pixel Tablet – have officially been announced.\\n\\nThe event, which kicks off May 10 at 10 AM PT will be a big showcase for everything that’s on the way for Android 14. The company has, arguably, missed a step when it comes to the current generative AI land rush — hell, who could have predicted after all of these years that Bing would finally have a moment?\\n\\nCEO Sundar Pichai will no doubt be making the case that the company continues to lead the way in the world of artificial intelligence. There’s always been a fair bit of the stuff at the event largely focused on practical real-world applications like mobile imaging and dealing with customer service. This year, however, I’d say it’s safe to say the company is going to go bonkers with the stuff.\\n\\nHardware, meanwhile, is always a bit of a crapshoot at developer conferences. But after an off-year for the industry at large, a deluge of rumors are aligning, pointing to what’s likely to be an unusually consumer electronics-focused keynote. Given the fact that the last bit is my focus at TechCrunch, I’m going to start the list there.\\n\\nThe Pixel 7a is about as sure as bets get. Google has settled into a comfortable release cadence: releasing a flagship in the fall, followed by a budget device in the spring. The former is designed to be an ideal showcase for its latest mobile operating system and first-party silicon, while the latter makes some compromises for price, while maintaining as many of its predecessors as possible.\\n\\nHow to show excitement without shouting? Asking for a friend Coming to @Flipkart on 11th May. pic.twitter.com/il6GUx3MmR — Google India (@GoogleIndia) May 2, 2023\\n\\nIt’s a good system that works, and Google’s newly focused mobile hardware team has created some surprisingly good devices at extremely reasonable prices. Never one to be outdone by the deluge of rumors, the company went ahead and announced via Twitter its next device is due out on May 11 — the day after Google I/O and, perhaps not coincidentally, my birthday. It was Google India that specifically made the announcement — perhaps not surprising, as the company is likely to aggressively target the world’s number one smartphone market with the product. The image points to a very similar design as the 7 — not really a surprise as these things go. Though it does stop short of actually mentioning the name, as it’s done in the past.\\n\\nBasically expect the 7 with cheaper materials. Rumors point to a 6.1-inch device featuring a 90Hz refresh rate, coupled with a 64-megapixel rear camera. The 7’s Tensor G2 returns for a command performance, likely bringing with it many of the software features it enabled the first time around.\\n\\nWe know for sure that a Pixel Tablet is coming…at some point. Google confirmed the device’s existence at last year’s event, providing a broad 2023 release date, along with a render alongside the rest of the current Pixel lineup. Effectively there are two points this year Google is likely to officially announce the thing: next week or September/October. I would be shocked if the company’s long-awaited (?) reentry into the category doesn’t, at the very least, get a bit of stage time. As a category, the Android tablet has been very hit or miss over the years — presumably/hopefully the company’s got a unique spin here. I would be surprised if Google jumped back into the space without some sort of novel angle.\\n\\nThe leaks point to a design that would effectively turn the system into one giant Nest dock. It’s not entirely original, as Amazon tried something similar with its Fire tablets, but it would certainly buck the iPad model, which is so pervasive in the industry. Other rumors include the aforementioned Tensor G2, coupled with 8GB of RAM.\\n\\nHere’s your wildcard, folks: the Pixel Fold. Google has seemingly been laying the groundwork for its own foldable for years. Here’s what I wrote a couple of weeks ago:\\n\\nSome important background here. First, Google announced foldable screen support for Android back in 2018. Obviously, Samsung was both the big partner and recipient in those days, and Google wanted to make Android development as frictionless as possible for other OEMs in exploring the form factor. The following year, Google foldable patents surfaced. Now, we’re all adults here, who implicitly understand that patents don’t mean a company is working on a product. That said, it’s another key data point in this story. In the intervening years, foldables have begun gathering steam, even outside of the Samsung orbit. I was genuinely amazed by how many different models there were populating the halls of MWC back in March. The leaked renders point to a form factor that is more Samsung Galaxy Z Fold than Samsung Galaxy Z Flip. It also looks like it shares some common design DNA with Oppo’s recently foldable, which is frankly the right direction. EV Leaks says the foldable is half an inch thick when folded and 0.2 inches unfolded, weight in at 283 grams.\\n\\nAs evidenced by our trip to MWC back in February, foldables are no longer fringe devices. It’s true that they’re still cost-prohibitive for most, but it’s getting to the point soon where nearly ever Android manufacturer will have their take on the category. So why shouldn’t Google?\\n\\nOther less likely hardware rumors include a Google/Nest AirTag competitor (the company announced yesterday that it’s working with Apple to create a standard for the category), new Pixel Buds and a Pixel Watch 2. I’d say all are unlikely — that last one in particular. We didn’t get much in terms of Nest products last year, but so far not much is forthcoming in terms of rumors for home products.\\n\\nAndroid is always a tentpole of Google I/O for obvious reasons. We’ve already caught some major glimpses of the mobile operating system, by way of beta releases. As Frederic noted in March, “So far, most of the features Google has talked about have also been developer-centric, with only a few user-facing features exposed to far. That also holds true for this second preview, which mostly focuses on added new security and privacy features.”\\n\\nThe operating system, which is apparently named Upside Down Cake internally, is likely set for a summer release in late-July or August. At the top of the list of potential features are a boost to battery life (can always use one of those), additional accessibility features and privacy/security features, which include blocking users from installing ancient apps over malware concerns.\\n\\nAI is going to be everywhere. Expect generative AI (Bard) in particular to make appearances in virtually every existing piece of Google consumer software, following the lead of Gmail and Docs. Search and the Chrome browser are prime targets here.\\n\\nA preview of a new Wear OS seems likely. I don’t anticipate a ton of news on the AR/VR side of things, but I would also be surprised if it doesn’t at least get a nod, given what Apple reportedly has in the works for June.\\n\\nThe keynote kicks off at 10 AM PT on May 10. As ever, TechCrunch will be bringing you the news as it breaks.', metadata={'source': '/content/new_articles/05-05-google-i-o-2023-is-next-week-heres-what-were-expecting.txt'}),\n", " Document(page_content='Signaling that investments in the supply chain sector remain robust, Pando, a startup developing fulfillment management technologies, today announced that it raised $30 million in a Series B round, bringing its total raised to $45 million.\\n\\nIron Pillar and Uncorrelated Ventures led the round, with participation from existing investors Nexus Venture Partners, Chiratae Ventures and Next47. CEO and founder <PERSON><PERSON> says that the new capital will be put toward expanding Pando’s global sales, marketing and delivery capabilities.\\n\\n“We will not expand into new industries or adjacent product areas,” he told TechCrunch in an email interview. “Great talent is the foundation of the business — we will continue to augment our teams at all levels of the organization. Pando is also open to exploring strategic partnerships and acquisitions with this round of funding.”\\n\\nPando was co-launched by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>, who previously worked together at iDelivery, an India-based freight tech marketplace — and their first startup. The two saw firsthand manufacturers, distributors and retailers were struggling with legacy tech and point solutions to understand, optimize and manage their global logistics operations — or at least, that’s the story <PERSON><PERSON><PERSON><PERSON><PERSON> tells.\\n\\n“Supply chain leaders were trying to build their own tech and throwing people at the problem,” he said. “This caught our attention — we spent months talking to and building for enterprise users at warehouses, factories, freight yards and ports and eventually, in 2018, decided to start Pando to solve for global logistics through a software-as-a-service platform offering.”\\n\\nThere’s truth to what <PERSON>krishnan’s expressing about pent-up demand. According to a recent McKinsey survey, supply chain companies had — and have — a strong desire for tools that deliver greater supply chain visibility. Sixty-seven percent of respondents to the survey say that they’ve implemented dashboards for this purpose, while over half say that they’re investing in supply chain visibility services more broadly.\\n\\nPando aims to meet the need by consolidating supply chain data that resides in multiple silos within and outside of the enterprise, including data on customers, suppliers, logistics service providers, facilities and product SKUs. The platform provides various tools and apps for accomplishing different tasks across freight procurement, trade and transport management, freight audit and payment and document management, as well as dispatch planning and analytics.\\n\\nCustomers can customize the tools and apps or build their own using Pando’s APIs. This, along with the platform’s emphasis on no-code capabilities, differentiates Pando from incumbents like SAP, Oracle, Blue Yonder and E2Open, Jayakrishnan asserts.\\n\\n“Pando comes pre-integrated with leading enterprise resource planning (ERPs) systems and has ready APIs and a professional services team to integrate with any new ERPs and enterprise systems,” he added. “Pando’s no-code capabilities enable business users to customize the apps while maintaining platform integrity — reducing the need for IT resources for each customization.”\\n\\nPando also taps algorithms and forms of machine learning to make predictions around supply chain events. For example, the platform attempts to match customer orders with suppliers, customers through the “right” channel (in terms of aspects like cost and carbon footprint) and fulfillment strategy (e.g. mode of freight, carrier, etc.). Beyond this, Pando can detect anomalies among deliveries, orders and freight invoices and anticipate supply chain risk given demand and supply trends.\\n\\nPando isn’t the only vendor doing this. Altana, which bagged $100 million in venture capital last October, uses an AI system to connect to and learn from logistics and business-to-business data — creating a shared view of supply chain networks. Everstream, another Pando rival, offers its own dashboards for data analysis, integrated with existing ERP, transportation management and supplier relationship management systems.\\n\\nBut Pando has a compelling sales pitch, judging by its momentum. The company counts Fortune 500 manufacturers and retailers — including P&G, J&J, Valvoline, Castrol, Cummins, Siemens, Danaher and Accuride — among its customer base. Since the startup’s Series A in 2020, revenue has grown 8x while the number of customers has increased 5x, Jayakrishnan said.\\n\\nAsked whether he expects expansion to continue well into the future, given the signs of potential trouble on the horizon, Jayakrishnan seemed fairly optimistic. He pointed to a Deloitte survey that found that more than 70% of manufacturing companies have been impacted by supply chain disruptions in the past year, with 90% of those companies experiencing increased costs and declining productivity.\\n\\nThe result of those major disruptions? The digital logistics market is estimated to climb to $46.5 billion by 2025, per Markets and Markets — up from $17.4 billion in 2019. Crunchbase reports that investors poured more than $7 billion in seed through growth-stage rounds globally for supply chain-focused startups from January to October 2022, nearly eclipsing 2021’s record-setting levels.\\n\\n“Pando has a strong balance sheet and profit and loss statement, with an eye on profitable growth,” Jayakrishnan said. “We’re are scaling operations in North America, Europe and India with marquee customer wins and a network of strong partners … Pando is well-positioned to ride this growth wave, and drive supply chain agility for the 2030 economy.”', metadata={'source': '/content/new_articles/05-03-ai-powered-supply-chain-startup-pando-lands-30m-investment.txt'}),\n", " Document(page_content='As brands incorporate generative AI into their creative workflows to generate new content associated with the company, they need to tread carefully to be sure that the new material adheres to the company’s style and brand guidelines.\\n\\nNova is an early-stage startup building a suite of generative AI tools designed to protect brand integrity, and today, the company is announcing two new products to help brands police AI-generated content: BrandGuard and BrandGPT.\\n\\nWith BrandGuard, you ingest your company’s brand guidelines and style guide, and with a series of models Nova has created, it can check the content against those rules to make sure it’s in compliance, while BrandGPT lets you ask questions about the brand’s content rules in ChatGPT style.\\n\\nR<PERSON> <PERSON>, founder and CEO at the company, who previously founded Backupify, a cloud backup startup that was acquired by <PERSON><PERSON> back in 2014, recognized that companies wanted to start taking advantage of generative AI technology to create content faster, but they still worried about maintaining brand integrity, so he came up with the idea of building a guard rail system to protect the brand from generative AI mishaps.\\n\\n“We heard from multiple CMOs who were worried about ‘how do I know this AI-generated content is on brand?’ So we built this architecture that we’re launching called BrandGuard, which is a really interesting series of models, along with BrandGPT, which acts as an interface on top of the models,” <PERSON> told TechCrunch.\\n\\nBrandGuard is like the back end for this brand protection system. <PERSON> built five models that look for things that might seem out of whack. They run checks for brand safety, quality checking, whether it’s on brand, whether it adheres to style and whether it’s on campaign. Then it assigns each piece with a content score, and each company can decide what the threshold is for calling in a human to check the content before publishing.\\n\\n“When you have generative AI creating stuff, you can now score it on a continuum. And then you can set thresholds, and if something’s below, say 85% on brand, you can have the system flag it so that a human can take a look at it,” he said. Companies can decide whatever threshold they’re comfortable with.\\n\\nBrandGPT is designed for working with third parties like an agency or a contractor, who can ask questions about the company’s brand guidelines to make sure they are complying with them, May said. “We’re launching BrandGPT, which is meant to be the interface to all this brand-related security stuff that we’re doing, and as people interact with brands, they can access the style guides and better understand the brand, whether they’re a part of the company or not.\\n\\nThese two products are available in public beta starting today. The company launched last year and has raised $2.4 million from Bee Partners, Fyrfly Ventures and Argon Ventures.', metadata={'source': '/content/new_articles/05-03-nova-is-building-guardrails-for-generative-ai-content-to-protect-brand-integrity.txt'}),\n", " Document(page_content='Welcome back to This Week in Apps, the weekly TechCrunch series that recaps the latest in mobile OS news, mobile applications and the overall app economy.\\n\\nThe app economy in 2023 hit a few snags, as consumer spending last year dropped for the first time by 2% to $167 billion, according to data.ai’s “State of Mobile” report. However, downloads are continuing to grow, up 11% year-over-year in 2022 to reach 255 billion. Consumers are also spending more time in mobile apps than ever before. On Android devices alone, hours spent in 2022 grew 9%, reaching 4.1 trillion.\\n\\nThis Week in Apps offers a way to keep up with this fast-moving industry in one place with the latest from the world of apps, including news, updates, startup fundings, mergers and acquisitions, and much more.\\n\\nDo you want This Week in Apps in your inbox every Saturday? Sign up here: techcrunch.com/newsletters\\n\\nTop Stories\\n\\nDorsey criticizes Twitter, <PERSON><PERSON> on the alternative social networks he’s backing\\n\\nAs demand for <PERSON><PERSON>, the Jack <PERSON>-backed decentralized Twitter rival grows, the former Twitter CEO took to the app to share his thoughts on Twitter’s future, <PERSON><PERSON> and the decision to take the company private. As TechCrunch’s <PERSON> reported, <PERSON><PERSON> responded to questions posed to him from other users and reporters on <PERSON><PERSON>, including one where he was asked if <PERSON><PERSON> has proven to be the best possible steward for the social network.\\n\\nDorsey said he had not:\\n\\nNo. Nor do I think he acted right after realizing his timing was bad. Nor do I think the board should have forced the sale. It all went south. But it happened and all we can do now is build something to avoid that ever happening again. So I’m happy <PERSON> and team and nostr devs exist and building it.\\n\\nHowever, the Twitter co-founder stressed that Twitter would have never survived as a public company and defended himself from an accusation that he was deflecting blame for Twitter’s current situation.\\n\\nThough Bluesky is having a moment, particularly as a haven for marginalized groups, sex workers and trans users, it’s not the only Twitter alternative Dorsey is now backing. In fact, he’s been more active in recent days on the social network nostr (which he also financially backed), where he’s also been critical of some of Musk’s recent decisions. For example, as The NYT reported, Dorsey posted last month “This is weak,” in response to Musk’s move to stop Twitter users from linking to Substack after it launched a Twitter-like service for its own community of writers and readers.\\n\\nDorsey also touted his belief in these platforms during Block’s recent earnings call, suggesting on his nostr profile this may be the first time the network’s name had been mentioned during a public earnings event.\\n\\n“Open protocols represent another fork in the road moment for people and companies,” Dorsey told investors. “Bitcoin, nostr, Bluesky, web5 and others are all working to level the playing field for competition and give individuals and organizations entirely new capabilities,” he added.\\n\\nOver the past few weeks, Bluesky has been gaining traction, but the network has been difficult to access due to its invite-only nature. That’s turned Bluesky invites into hot commodities, where they’re even selling for hundreds of dollars on eBay, as most users have to wait to receive only one invite every two weeks.\\n\\nBluesky leadership will also sometimes gift a user with a larger number of invites in order to have them invite members of a specific community. Developers who can demonstrate they’re building a Bluesky app may also request additional invites, we understand.\\n\\nThe network has received outsized press coverage relative to its size — just 50,000+ users — possibly because of the heavy infusion of tech journalists on there and Dorsey’s name attached. But the reality is that Bluesky’s future remains uncertain. The company, for now, is able to build and grow thanks to the $13 million in initial funds it received from Twitter, where it was incubated under Dorsey’s leadership. It has since spun out into its own, independent company (a public benefit LLC). It’s unclear how Bluesky intends to maintain its operations in the long term, not to mention its freewheeling culture and accepting community. Networks can often be pleasant and welcoming when small, like Bluesky — or early Twitter, for that matter — but face challenges once they scale to millions of users.\\n\\nNewFronts round-up\\n\\nThis week was IAB’s NewFronts, where digital media companies and social networks pitched their platforms to advertisers looking to reach online audiences. The event saw major brands introducing a range of new offerings, including both ad products and formats, as well as touting their latest features, in some cases, as Snap did with its My AI integration.\\n\\nHere’s what you may have missed from the app makers’ NewFronts this week:\\n\\nSnap said it’s beginning to test a feature that lets partners leverage its new My AI chatbot to place sponsored links in front of users. Snap also announced new ad slots, including the option to reserve the first video ad seen in Snapchat’s Friend Stories and the ability to advertise within its TikTok-like Spotlight feature.\\n\\nSnap also announced including the option to reserve the and the ability to YouTube introduced new ad opportunities for Shorts, including the expansion of Shorts into Video reach campaigns that leverage Google AI to serve the best combination of ads and improve reach on YouTube. Plus, YouTube Select is now coming to Shorts, allowing advertisers to place their ads alongside the most popular YouTube Shorts’ content, similar to TikTok Pulse. Another option, First Position on Shorts, will let advertisers be the first ad Shorts users see in their viewing session.\\n\\nincluding the that leverage Google AI to serve the best combination of ads and improve reach on YouTube. Plus, allowing advertisers to place their ads alongside the most popular YouTube Shorts’ content, similar to TikTok Pulse. Another option, will let advertisers be the first ad Shorts users see in their viewing session. TikTok announced partnerships with big-name publishers, including NBCU, Condé Nast, DotDash Meredith, BuzzFeed and others, in an effort to pull in more premium ad dollars. The new premium ad product, Pulse Premiere, would allow marketers, for the first time, to position their brand ads directly after TikTok’s publisher and media partners’ content in over a dozen categories, including lifestyle, sports, entertainment, education and more. Publisher partners would receive a rev share as a result.\\n\\nThe would allow marketers, for the first time, to position their brand ads directly after TikTok’s publisher and media partners’ content in over a dozen categories, including lifestyle, sports, entertainment, education and more. Publisher partners would receive a rev share as a result. Meta announced AR would become available to Reels Ads and Facebook Stories. They had previously been available only to the Facebook Feed, Instagram Feed and Instagram Stories. It also announced features to make Reels Ads more interactive, including a t est of a larger “call to action” button with additional advertiser information on Facebook and Instagram Reels ads. Other updates included multi-destination product ads, the ability to pause a video ad to preview a link’s destination and support for Reels Ads campaigns with select third-party measurement firms .\\n\\nThey had previously been available only to the Facebook Feed, Instagram Feed and Instagram Stories. It also announced features to make Reels Ads more interactive, including a t with additional advertiser information on Facebook and Instagram Reels ads. Other updates included the ability to and support for . NBCU will let Peacock users shop products that appear in its content through “Must ShopTV,” which puts a QR code on the screen when a shoppable product appears.\\n\\nApple & Google team up on Bluetooth tracker safety\\n\\nAfter numerous cases of Bluetooth trackers like Apple’s AirTag being used for stalking or other criminal apps, Apple and Google this week released a joint announcement saying they will work together to lead an industry-wide initiative to draft a specification that would alert users in the case of unwanted tracking from Bluetooth devices. The companies said they’re seeking input from other industry participants and advocacy groups in the matter, and noted that other tracker makers like Samsung, Tile, Chipolo, eufy Security and Pebblebee have also expressed interest in the draft.\\n\\nThe companies submitted a proposed specification as an Internet-Draft via a standards development organization, the Internet Engineering Task Force (IETF). Other interested parties are now being invited to review and comment over the next three months. After this time, Apple and Google will offer feedback and will release a production implementation of the specification by year’s end that will be supported in future versions of iOS and Android, they said.\\n\\nThe spec would build on the AirTag protections Apple had already released but also, critically, would ensure that users would be able to combat unwanted tracking by offering tools across both iOS and Android platforms.\\n\\nGoogle’s participation could signal more than a desire to protect its users — it’s been rumored the company may also be developing an AirTag rival.\\n\\nPlatforms\\n\\nApple\\n\\nGoogle — I/O Preview\\n\\nGoogle I/O kicks off next week and we already know at least one of the announcements — because Google leaked it. The company plans to introduce its first foldable smartphone with the Pixel Fold. The device shares Pixel’s familiar camera bar and features an interface that showcases Material UI design. We expect to learn more at the event.\\n\\nIn addition, Google I/O 2023 should bring a Pixel 7a , a budget device that could also help address Pixel demand in emerging markets, plus possibly a Pixel tablet, an AirTag rival, a Wear OS update, and a lot of new developer tools and features. We also expect to hear quite a bit about Google’s AI plans, with generative AI (like Bard) appearing across Google’s line of products.\\n\\n, a budget device that could also help address Pixel demand in emerging markets, plus possibly a Pixel tablet, an AirTag rival, a Wear OS update, and a lot of new developer tools and features. We also expect to hear quite a bit about Google’s AI plans, with generative AI (like Bard) appearing across Google’s line of products. To get ready for I/O, even if you’re attending virtually, Google offered a new planning guide and a playlist of developer content to help attendees prepare.\\n\\nto help attendees prepare. Checks, Google’s AI-powered data protection project, exited to Google from its in-house incubator Area 120. The tool uses AI to check mobile apps for compliance with various privacy rules and regulations.\\n\\nApp Updates\\n\\nSocial\\n\\nSocial networking app IRL’s CEO Abraham Shafi stepped down following allegations he used bots to inflate the number of users IRL reported publicly and to its investors , The Information reported. A former employee had alleged he was fired after expressing concern over the use of bots. The SEC is now investigating if the company violated securities laws. IRL raised around $200 million from SoftBank Vision Fund, Founders Fund and others.\\n\\n, The Information reported. A former employee had alleged he was fired after expressing concern over the use of bots. The SEC is now investigating if the company violated securities laws. IRL raised around $200 million from SoftBank Vision Fund, Founders Fund and others. After laying off 50% of staff, declining audio social network Clubhouse says it’s building “Clubhouse 2.0,” but hasn’t shared exactly what that plan may involve. Last year, the company began shifting its focus away from public audio to private rooms but it’s not clear there’s much demand for audio social networking in the post-pandemic market.\\n\\nbut hasn’t shared exactly what that plan may involve. Last year, the company began shifting its focus away from public audio to private rooms but it’s not clear there’s much demand for audio social networking in the post-pandemic market. Once-hot viral app Poparazzi shuts down and returns remaining funds to investors. The app had let friends tag others to build out their social profiles of real moments, not polished images, but had been on the decline, with only a few thousand MAUs down from a height of 4 million MAUs previously.\\n\\nA Twitter bug saw users able to regain their blue Verification checks just by editing their bio. Shortly afterward, the Twitter desktop website began randomly logging out users. Later in the week, the mobile website was also down.\\n\\nShortly afterward, the Twitter desktop website began randomly logging out users. Later in the week, the mobile website was also down. As Bluesky gains attention, rival decentralized social platform Mastodon announced a new, simpler onboarding experience that provides new users with an account on mastodon.social by default , instead of requiring them to pick a server. This doesn’t eliminate server choice, it simply means that joining another server requires a few extra clicks.\\n\\n, instead of requiring them to pick a server. This doesn’t eliminate server choice, it simply means that joining another server requires a few extra clicks. Neighborhood social network Nextdoor added new features powered by generative AI, including an Assistant feature aimed at helping users write posts that are more likely to drive positive community engagement. The Assistant will offer writing suggestions that users can review and optionally adopt. The company says it will also use AI to better match content to users when providing recommendations.\\n\\nincluding an Assistant feature aimed at helping users write posts that are more likely to drive positive community engagement. The Assistant will offer writing suggestions that users can review and optionally adopt. The company says it will also use AI to better match content to users when providing recommendations. BeReal is testing another new feature in the U.K., “RealPeople,” that shows users a timeline of the “world’s most interesting people” — that is, athletes, artists, activists and other public figures. The company also recently began testing the option to post more often as usage has declined.\\n\\nand other public figures. The company also recently began testing the option to post more often as usage has declined. Meta introduced new discovery and personalization options for Facebook Reels. Users can now choose “Show More” or “Show Less” options to control what sort of Reels they want to see. Facebook will also explain why it’s showing you a Reel, like if a friend viewed it, and is adding Reels to the main navigation at the top of Facebook Watch.\\n\\nWordPress drops Twitter integration, says sharing to Instagram and Mastodon is coming instead. The Automattic-owned publishing platform said the Twitter connection on Jetpack and WordPress.com will cease to work, meaning users’ blog posts will no longer be auto-shared to Twitter as before. The company said Elon Musk’s decision to “dramatically change the terms and pricing” for Twitter’s API was to blame for this decision. The API now starts at $42,000/month for 50 million tweets. The move will likely hurt Twitter more than WordPress, as the latter powers over 40% of the global internet, including WordPress.com blogs.\\n\\nThe Automattic-owned publishing platform said the Twitter connection on Jetpack and WordPress.com will cease to work, meaning users’ blog posts will no longer be auto-shared to Twitter as before. The company said Elon Musk’s decision to “dramatically change the terms and pricing” for Twitter’s API was to blame for this decision. The API now starts at $42,000/month for 50 million tweets. The move will likely hurt Twitter more than WordPress, as the latter powers over 40% of the global internet, including WordPress.com blogs. Mozilla announced it’s opening up its own Mastodon server — or “instance,” in Mastodon lingo — into private beta testing. The company had said last year it planned to create and begin testing a publicly accessible instance at mozilla.social. It explains its approach to Mastodon will involve high levels of moderation.\\n\\nThe company had said last year it planned to create and begin testing a publicly accessible instance at mozilla.social. It explains its approach to Mastodon will involve high levels of moderation. Twitter announced it would make its API free for public service announcements after New York’s Metro Transit Service (MTS) abandoned the service and the National Weather Services (NWS) said it would no longer auto-post warnings.\\n\\nafter New York’s Metro Transit Service (MTS) abandoned the service and the National Weather Services (NWS) said it would no longer auto-post warnings. TikTok’s U.S. head of trust and safety Eric Han is leaving the company on May 12 as lawmakers weigh a TikTok ban. Han had played a key role in TikTok’s strategy to avoid a U.S. ban.\\n\\nas lawmakers weigh a TikTok ban. Han had played a key role in TikTok’s strategy to avoid a U.S. ban. Discord is making all users change their usernames, the company announced this week. Originally, Discord users had been identified by a name and random number separated by a hash sign, but now it wants to adopt a simpler format so people can more easily share their usernames with others. The new plan will include a unique alphanumeric username with the @ symbol in front of it, plus a freely assignable display name that can be changed at any time.\\n\\nAI\\n\\nSlack introduced SlackGPT, its own generative AI built on Slack’s platform which developers can use to create AI-driven experiences.\\n\\nwhich developers can use to create AI-driven experiences. Microsoft launched its Bing chatbot to all users globally, meaning there’s no more waitlist to get started. It’s also adding more image- and graphic-centric answers in Bing Chat, including by creating graphs and charts and generating images from text prompts. It will also allow users to export their Bing Chat histories. And it will embrace multimodality, meaning it can understand queries with images and text combined. Bing now sees more than 100 million daily active users and says visitors have engaged in over half a billion chats.\\n\\nIt’s also adding more image- and graphic-centric answers in Bing Chat, including by creating graphs and charts and generating images from text prompts. It will also allow users to export their Bing Chat histories. And it will embrace multimodality, meaning it can understand queries with images and text combined. Bing now sees more than 100 million daily active users and says visitors have engaged in over half a billion chats. Plexamp, the music player originally incubated by the Labs division of media company Plex, is tapping into ChatGPT with its latest update. The new feature called “Sonic Sage,” powered by OpenAI’s ChatGPT, will build unique music playlists by scanning users’ libraries and leveraging their TIDAL subscription.\\n\\nMedia & Entertainment\\n\\nFintech\\n\\nYC-backed Kenyan fintech Fingo launched its neobanking app, developed in collaboration with Pan-African financial institution Ecobank Kenya. The company raised $4 million in seed funding after its YC S21 participation. Fingo offers users a bank account, paired with free peer-to-peer transactions and access to savings, financial education and smart spending analytics.\\n\\nThe company raised $4 million in seed funding after its YC S21 participation. Fingo offers users a bank account, paired with free peer-to-peer transactions and access to savings, financial education and smart spending analytics. The FDIC is looking into Tellus, an Andreessen Horowitz-backed fintech company that claims it can offer people higher yields on their savings balances by using that money to fund certain U.S. single-family-home loans. U.S. Senator Sherrod Brown, chairman of the Senate Banking, Housing, and Urban Affairs Committee, wrote a letter to FDIC Chairman Martin Gruenberg expressing concerns about Tellus, and asking the FDIC to review Tellus’s business practices which may put customers at risk.\\n\\nMessaging\\n\\nWhatsApp now lets users create single-vote polls and forward media with captions, Meta announced this week. Single-vote polls let users run a poll where people are only allowed to vote once, including multiple choice, as has been the default.\\n\\nMeta announced this week. Single-vote polls let users run a poll where people are only allowed to vote once, including multiple choice, as has been the default. Reddit’s latest update provides link previews for messaging apps. Now, when you share a Reddit link via a messaging app, it will include a visual preview of the content, the subreddit name, the total upvotes tally and the number of comments. The update also includes the ability to share directly to IG Stories and other tools for publishers.\\n\\nTravel & Transportation\\n\\nFollowing its acquisition by Via, Citymapper said it’s lowering the paywall for its premium features while also introducing a new subscription plan ($1.49/mo) purely for removing ads.\\n\\nwhile also introducing a new subscription plan ($1.49/mo) purely for removing ads. Uber reported a Q1 earnings beat with its revenue up 29% YoY to $8.82 billion, gross bookings up 19% YoY to $31.4 billion and adjusted EBITDA up 353% YoY to $761 million. It also reported a $157 million net loss.\\n\\ngross bookings up 19% YoY to $31.4 billion and adjusted EBITDA up 353% YoY to $761 million. It also reported a $157 million net loss. Uber Eats is also planning to offer support for Live Activities and Dynamic Island on iPhone and integrated with Alexa for order updates.\\n\\nfor order updates. Lyft shared worrisome Q2 guidance sending its stock down after Q1 earnings where it had reported a 14% YoY increase in revenue to $1 billion and a net loss drop of 5% to $187.6 million. Ridership was up 9.8% YoY to 19.5 million.\\n\\nGaming\\n\\nSnowman, the mobile game studio behind Alto’s Adventure and Alto’s Odyssey, launched its newest title, Laya’s Horizon, exclusively with Netflix. The wingsuit game sees players mastering the art of flying, diving off mountains, weaving across forests and gliding over rivers to unlock new abilities as they explore a vast and peaceful world.\\n\\nCross-platform game engine Unity announced layoffs of 8% of its workforce, or around 600 jobs, after laying off 500+ in January and last June.\\n\\nafter laying off 500+ in January and last June. Amazon announced that customers in the United States, Canada, Germany and the United Kingdom can now play Fortnite on their Fire TVs via its Amazon Luna cloud gaming service.\\n\\nCommerce & Food Delivery\\n\\nAmazon Inspire, the e-commerce giant’s in-app TikTok-like shopping feed has rolled out to all customers in the United States. The company had been experimenting since last year with the new feed, which features content creators by influencers.\\n\\nThe company had been experimenting since last year with the new feed, which features content creators by influencers. DoorDash revenue was up 40% YoY in Q1, reaching $2.04 billion, beating estimates of $1.93 billion. Its net loss also declined 3% to $162 million and orders were up 27% to 512 million.\\n\\nEtc.\\n\\nAmazon rolled out a Matter update for Alexa that includes support for Thread, setup on iOS, and a new version of its Works with Alexa program.\\n\\nand a new version of its Works with Alexa program. Match Group posted a Q1 earnings miss with revenue down by 1% YoY to $787 million and paying users down 3% to 15.9 million. The company, however, said it’s “very possible” the recent Apple-Epic court decision could result in App Store fee relief.\\n\\nMedtech startup Healthy.io, which provides urine analysis through a mobile app, is laying off a third of its staff, or around 70 people. The company had just raised $50 million in Series D funding.\\n\\nThe company had just raised $50 million in Series D funding. Airbnb announced Rooms, a feature that focuses on the ability to book single rooms averaging $67 per night as users complain about excessive fees, onerous checkout procedures and rising Airbnb prices.\\n\\naveraging $67 per night as users complain about excessive fees, onerous checkout procedures and rising Airbnb prices. Google’s smart home app, Google Home, added support for smart garage door openers.\\n\\nSecurity\\n\\nGoogle announced that passkeys are now rolling out to Google Account users globally. Passkey let users sign in to websites and apps using the same biometrics or screen-lock PIN they use to unlock their devices.\\n\\nPasskey let users sign in to websites and apps using the same biometrics or screen-lock PIN they use to unlock their devices. Google announced that in 2022, it prevented 1.43 million policy-violating apps from being published on Google Play “in part due to new and improved security features and policy enhancements.”\\n\\nGovernment, Policy and Lawsuits\\n\\nThe EU’s Digital Markets Act (DMA) became applicable on May 2, but enforcement is not expected until spring 2024. The act focused on gatekeepers like Apple, Google, Meta and Microsoft. It limits how they can use third-party data, bans self-preferencing, introduces interoperability requirements, bans tracking users for targeted ads without consent and more. It also says app stores can’t require the use of their own payment services and permits app sideloading.\\n\\nBipartisan U.S. lawmakers reintroduced the Kids Online Safety Act with updates aimed at fixing earlier issues. The bill says platforms have to take reasonable steps to stop the spread of posts that promote eating disorders, suicide, substance abuse and more and undergo independent analysis about their safety for minors. It now also includes protections for support services, like the National Suicide Hotline, substance abuse groups and LGBTQ+ youth centers. However, critics, including the ACLU, say the changes are not enough and they remain opposed to the increased surveillance of kids this bill would require and other matters.\\n\\nThe bill says platforms have to take reasonable steps to stop the spread of posts that promote eating disorders, suicide, substance abuse and more and undergo independent analysis about their safety for minors. It now also includes protections for support services, like the National Suicide Hotline, substance abuse groups and LGBTQ+ youth centers. However, critics, including the ACLU, say the changes are not enough and they remain opposed to the increased surveillance of kids this bill would require and other matters. France’s competition watchdog announced interim measures against Meta, saying it suspects Meta of abusing its dominant position in the French market for ads on social media and across the broader (non-search-related) online ads market.\\n\\nsaying it suspects Meta of abusing its dominant position in the French market for ads on social media and across the broader (non-search-related) online ads market. The U.S. Federal Trade Commission (FTC) says Meta has “repeatedly violated” privacy rules and proposed to tighten its 2020 privacy order against the company, which would completely bar it from monetizing data from anyone under 18 in any way, among other new restrictions. The FTC also accused Meta of COPPA, a children’s privacy law, by misrepresenting its Messenger Kids parental controls, which allowed group chats and group calls with unapproved contacts.\\n\\nFunding and M&A\\n\\nAmazon acquired a small audio-focused artificial intelligence firm called Snackable.AI in 2022, The Post reported. Deal terms weren’t disclosed but Mari Joller, the founder and CEO of Snackable, is now the artificial intelligence and machine learning product leader at Amazon.\\n\\nDownloads\\n\\nRTRO\\n\\nNew social networking startup RTRO launched its app this week with the goal of connecting brands, creators and their fans and followers in a more positive environment focused on human connections and communities, not algorithm-driven content. To accomplish this, RTRO divides its social experience into two parts — on one side, you can keep up with friends or family in RTRO’s “circles.” On the other side, users can switch over to see content from creators and brands in their own space, dubbed RTRO TV.\\n\\nDistroKid\\n\\nMusic distribution service DistroKid this week launched its first mobile app, initially only for iPhone. The new app lets artists upload new releases, receive instant payment alerts, access stats from Apple and Spotify, edit metadata and more from their phones. The company said the mobile app had been the number one request from DistroKid members.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='OpenAI may be synonymous with machine learning now and Google is doing its best to pick itself up off the floor, but both may soon face a new threat: rapidly multiplying open source projects that push the state of the art and leave the deep-pocketed but unwieldy corporations in their dust. This Zerg-like threat may not be an existential one, but it will certainly keep the dominant players on the defensive.\\n\\nThe notion is not new by a long shot — in the fast-moving AI community, it’s expected to see this kind of disruption on a weekly basis — but the situation was put in perspective by a widely shared document purported to originate within Google. “We have no moat, and neither does OpenAI,” the memo reads.\\n\\nI won’t encumber the reader with a lengthy summary of this perfectly readable and interesting piece, but the gist is that while GPT-4 and other proprietary models have obtained the lion’s share of attention and indeed income, the head start they’ve gained with funding and infrastructure is looking slimmer by the day.\\n\\nWhile the pace of OpenAI’s releases may seem blistering by the standards of ordinary major software releases, GPT-3, ChatGPT and GPT-4 were certainly hot on each other’s heels if you compare them to versions of iOS or Photoshop. But they are still occurring on the scale of months and years.\\n\\nWhat the memo points out is that in March, a leaked foundation language model from Meta, called LLaMA, was leaked in fairly rough form. Within weeks, people tinkering around on laptops and penny-a-minute servers had added core features like instruction tuning, multiple modalities and reinforcement learning from human feedback. OpenAI and Google were probably poking around the code, too, but they didn’t — couldn’t — replicate the level of collaboration and experimentation occurring in subreddits and Discords.\\n\\nCould it really be that the titanic computation problem that seemed to pose an insurmountable obstacle — a moat — to challengers is already a relic of a different era of AI development?\\n\\nSam Altman already noted that we should expect diminishing returns when throwing parameters at the problem. Bigger isn’t always better, sure — but few would have guessed that smaller was instead.\\n\\nGPT-4 is a Walmart, and nobody actually likes Walmart\\n\\nThe business paradigm being pursued by OpenAI and others right now is a direct descendant of the SaaS model. You have some software or service of high value and you offer carefully gated access to it through an API or some such. It’s a straightforward and proven approach that makes perfect sense when you’ve invested hundreds of millions into developing a single monolithic yet versatile product like a large language model.\\n\\nIf GPT-4 generalizes well to answering questions about precedents in contract law, great — never mind that a huge number of its “intellect” is dedicated to being able to parrot the style of every author who ever published a work in the English language. GPT-4 is like a Walmart. No one actually wants to go there, so the company makes damn sure there’s no other option.\\n\\nBut customers are starting to wonder, why am I walking through 50 aisles of junk to buy a few apples? Why am I hiring the services of the largest and most general-purpose AI model ever created if all I want to do is exert some intelligence in matching the language of this contract against a couple hundred other ones? At the risk of torturing the metaphor (to say nothing of the reader), if GPT-4 is the Walmart you go to for apples, what happens when a fruit stand opens in the parking lot?\\n\\nIt didn’t take long in the AI world for a large language model to be run, in highly truncated form of course, on (fittingly) a Raspberry Pi. For a business like OpenAI, its jockey Microsoft, Google or anyone else in the AI-as-a-service world, it effectively beggars the entire premise of their business: that these systems are so hard to build and run that they have to do it for you. In fact it starts to look like these companies picked and engineered a version of AI that fit their existing business model, not vice versa!\\n\\nOnce upon a time you had to offload the computation involved in word processing to a mainframe — your terminal was just a display. Of course that was a different era, and we’ve long since been able to fit the whole application on a personal computer. That process has occurred many times since as our devices have repeatedly and exponentially increased their capacity for computation. These days when something has to be done on a supercomputer, everyone understands that it’s just a matter of time and optimization.\\n\\nFor Google and OpenAI, the time came a lot quicker than expected. And they weren’t the ones to do the optimizing — and may never be at this rate.\\n\\nNow, that doesn’t mean that they’re plain out of luck. Google didn’t get where it is by being the best — not for a long time, anyway. Being a Walmart has its benefits. Companies don’t want to have to find the bespoke solution that performs the task they want 30% faster if they can get a decent price from their existing vendor and not rock the boat too much. Never underestimate the value of inertia in business!\\n\\nSure, people are iterating on LLaMA so fast that they’re running out of camelids to name them after. Incidentally, I’d like to thank the developers for an excuse to just scroll through hundreds of pictures of cute, tawny vicuñas instead of working. But few enterprise IT departments are going to cobble together an implementation of Stability’s open source derivative-in-progress of a quasi-legal leaked Meta model over OpenAI’s simple, effective API. They’ve got a business to run!\\n\\nBut at the same time, I stopped using Photoshop years ago for image editing and creation because the open source options like Gimp and Paint.net have gotten so incredibly good. At this point, the argument goes the other direction. Pay how much for Photoshop? No way, we’ve got a business to run!\\n\\nWhat Google’s anonymous authors are clearly worried about is that the distance from the first situation to the second is going to be much shorter than anyone thought, and there doesn’t appear to be a damn thing anybody can do about it.\\n\\nExcept, the memo argues: embrace it. Open up, publish, collaborate, share, compromise. As they conclude:', metadata={'source': '/content/new_articles/05-05-google-and-openai-are-walmarts-besieged-by-fruit-stands.txt'}),\n", " Document(page_content='The best way to avoid a down round is to found an AI startup\\n\\nAs we see unicorns slash staff and the prevalence of down rounds spike, it may seem that the startup ecosystem is chock-full of bad news and little else. That’s not precisely the case.\\n\\nWhile AI, and in particular the generative AI subcategory, are as hot as the sun, not all venture attention is going to the handful of names that you already know. Sure, OpenAI is able to land nine and 10-figure rounds from a murderer’s row of tech investors and mega-cap corporations. And rising companies like Hugging Face and Anthropic cannot stay out of the news, proving that smaller AI-focused startups are doing more than well.\\n\\nIn fact, new data from Carta, which provides cap table management and other services, indicates that AI-focused startups are outperforming their larger peer group at both the seed and Series A stage.\\n\\nThe dataset, which notes that AI-centered startups are raising more and at higher valuations than other startups, indicates that perhaps the best way to avoid a down round today is to build in the artificial intelligence space.\\n\\nWhat the data says\\n\\nPer Carta data relating to the first quarter of the year, seed funding to non-AI startups in the U.S. market that use its services dipped from $1.64 billion to $1.08 billion, or a decline of around 34%. That result is directionally aligned with other data that we’ve seen regarding Q1 2023 venture capital totals; the data points down.', metadata={'source': '/content/new_articles/05-06-ai-startups-q1-investments.txt'}),\n", " Document(page_content='The legal spats between artists and the companies training AI on their artwork show no sign of abating.\\n\\nWithin the span of a few months, several lawsuits have emerged over generative AI tech from companies including OpenAI and Stability AI, brought by plaintiffs who allege that copyrighted data — mostly art — was used without their permission to train the generative models. Generative AI models “learn” to create art, code and more by “training” on sample images and text, usually scraped indiscriminately from the web.\\n\\nIn an effort to grant artists more control over how — and where — their art’s used, <PERSON> and <PERSON><PERSON> co-founded the startup Spawning AI. Spawning created HaveIBeenTrained, a website that allows creators to opt out of the training dataset for one art-generating AI model, Stable Diffusion v3, due to be released in the coming months.\\n\\nAs of March, artists had used HaveIBeenTrained to remove 80 million pieces of artwork from the Stable Diffusion training set. By late April, that figure had eclipsed 1 billion.\\n\\nAs the demand for Spawning’s service grew, the company — which was entirely bootstrapped up until that point — sought an outside investment. And it got it. Spawning today announced that it raised $3 million in a seed round led by True Ventures with participation from the Seed Club Ventures, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and Noise DAO.\\n\\nSpeaking to TechCrunch via email, <PERSON> said that the funding will allow Spawning to continue developing “IP standards for the AI era” and establish more robust opt-out and opt-in standards.\\n\\n“We are enthusiastic about the potential of AI tooling. We developed domain expertise in the field from being passionate about new opportunities AI provides to creators, but feel that consent is a fundamental layer to make these developments something everyone can feel good about,” Meyer said.\\n\\nSpawning’s metrics speak for themselves. Clearly, there’s a demand from artists for more say in how their art’s used (or scraped, as the case may be). But beyond partnerships with art platforms like Shutterstock and ArtStation, Spawning hasn’t managed to rally the industry around a common opt-out or provenance standard.\\n\\nAdobe, which recently announced generative AI tools, is pursuing its own opt-out mechanisms and tooling. So is DeviantArt, which in November launched a protection that relies on HTML tags to prohibit the software robots that crawl pages for images from downloading those images for training sets. OpenAI, the generative AI giant in the room, still doesn’t offer an opt-out tool — nor has it announced plans to anytime soon.\\n\\nSpawning has also come under criticism for the opaqueness — and vagueness — of its opt-out process. As Ars Technica noted in a recent piece, the opt-out process doesn’t appear to fit the definition of consent for personal data use in Europe’s General Data Protection Regulation, which states that consent must be actively given, not assumed by default. Also unclear is how Spawning intends to legally verify the identities of artists who make opt-out requests — or indeed, if it intends to attempt this at all.\\n\\nSpawning’s solution is multipronged. First, it plans to make it easier for AI model trainers to honor opt-out requests and streamline the process for creators. Then, Spawning will offer more services to organizations seeking to protect the work of their artists, Meyer says.\\n\\n“We want to build the consent layer for AI, which we feel will be a fundamentally helpful piece of infrastructure moving forward,” he added. “We plan to grow Spawning to address the many different domains touched by the AI economy, as each domain has their own particular needs.”\\n\\nIn a first step toward this ambitious vision, Spawning in March enabled “domain opt-outs,” allowing creators and content partners to quickly opt-out content from whole websites. Spawning says that 30,000 domains to date have been registered in the system.\\n\\nApril will mark the release of an API and open source Python package that’ll greatly expand the breadth of content that Spawning touches. Previously, opt-out requests through Spawning only applied to the LAION-5B dataset — the dataset used to train Stable Diffusion. As of April, any website, app or service will be able to use Spawning’s API to automatically comply with opt-outs not just for image data, but for text, audio, videos and more.\\n\\nMeyer says that Spawning will aggregate every new opt-out method (e.g. Adobe’s and DeviantArt’s) into its Python package for model trainers, with the goal of cutting down on the number of accounts model creators have to manage to comply with opt-out requests.\\n\\nTo boost visibility, Spawning is partnering with Hugging Face, one of the larger platforms for hosting and running AI models, to add a new info box on Hugging Face that’ll alert users to the proportion of “opted-out” data within text-to-image datasets. The box will also link to a Spawning API sign-up page so that model trainers can remove opted-out images at training time.\\n\\n“We feel that once companies and developers know that the option to honor creator wishes is available, there is little reason not to honor them,” Meyer said. “We are excited about the future of generative AI, but creators and organizations alike need standards in place to have their data work in their favor.”\\n\\nLooking ahead, Spawning intends to release an “exact-duplicate” detection feature to match opted-out images with copies that the platform finds across the web, followed by a “near-duplicate” detection feature to notify artists when Spawning finds likely copies of their work that’ve been cropped, compressed or otherwise slightly modified.\\n\\nBeyond that, there’s plans for a Chrome extension to let creators pre-emptively opt out of their work posted anywhere on the web and a caption search on the HaveIBeenTrained website to directly search image descriptions. The site’s current search tool uses only approximate matches between text and images as well as URL searches to find content hosted on specific websites.\\n\\nSpawning — now beholden to investors — plans to make money by building services on top of its content infrastructure, although Meyer wouldn’t divulge much. How that’ll sit with content creators remains to be seen.\\n\\n“We’ve spoken to quite a few organizations, with many conversations being too premature to announce, and think that our funding announcement and increased visibility will go some way to offer assurances that what we are building is a robust and dependable standard to work with,” Meyer said. “After we complete these features, we’ll begin building infrastructure to support more datasets — including music, video and text.”', metadata={'source': '/content/new_articles/05-03-spawning-lays-out-its-plans-for-letting-creators-opt-out-of-generative-ai-training.txt'}),\n", " Document(page_content='Well that was fast. The U.K.’s competition watchdog has announced an initial review of “AI foundational models”, such as the large language models (LLMs) which underpin OpenAI’s ChatGPT and Microsoft’s New Bing. Generative AI models which power AI art platforms such as OpenAI’s DALL-E or Midjourney will also likely fall in scope.\\n\\nThe Competition and Markets Authority (CMA) said its review will look at competition and consumer protection considerations in the development and use of AI foundational models — with the aim of understanding “how foundation models are developing and producing an assessment of the conditions and principles that will best guide the development of foundation models and their use in the future”.\\n\\nIt’s proposing to publish the review in “early September”, with a deadline of June 2 for interested stakeholders to submit responses to inform its work.\\n\\n“Foundation models, which include large language models and generative artificial intelligence (AI), that have emerged over the past five years, have the potential to transform much of what people and businesses do. To ensure that innovation in AI continues in a way that benefits consumers, businesses and the UK economy, the government has asked regulators, including the [CMA], to think about how the innovative development and deployment of AI can be supported against five overarching principles: safety, security and robustness; appropriate transparency and explainability; fairness; accountability and governance; and contestability and redress,” the CMA wrote in a press release.”\\n\\nStanford University’s Human-Centered Artificial Intelligence Center’s Center for Research on Foundation Models is credited with coining the term “foundational models”, back in 2021, to refer to AI systems that focus on training one model on a huge amount of data and adapting it to many applications.\\n\\n“The development of AI touches upon a number of important issues, including safety, security, copyright, privacy, and human rights, as well as the ways markets work. Many of these issues are being considered by government or other regulators, so this initial review will focus on the questions the CMA is best placed to address — what are the likely implications of the development of AI foundation models for competition and consumer protection?” the CMA added.\\n\\nIn a statement, its CEO, Sarah Cardell, also said:\\n\\nAI has burst into the public consciousness over the past few months but has been on our radar for some time. It’s a technology developing at speed and has the potential to transform the way businesses compete as well as drive substantial economic growth. It’s crucial that the potential benefits of this transformative technology are readily accessible to UK businesses and consumers while people remain protected from issues like false or misleading information. Our goal is to help this new, rapidly scaling technology develop in ways that ensure open, competitive markets and effective consumer protection.\\n\\nSpecifically, the U.K. competition regulator said its initial review of AI foundational models will:\\n\\nexamine how the competitive markets for foundation models and their use could evolve\\n\\nexplore what opportunities and risks these scenarios could bring for competition and consumer protection\\n\\nproduce guiding principles to support competition and protect consumers as AI foundation models develop\\n\\nWhile it may seen early for the antitrust regulator to conduct a review of such a fast-moving emerging technology the CMA is acting on government instruction.\\n\\nAn AI white paper published in March signalled ministers’ preference to avoid setting any bespoke rules (or oversight bodies) to govern uses of artificial intelligence at this stage. However ministers said existing U.K. regulators — including the CMA, which was directly name-checked — would be expected to issue guidance to encourage safe, fair and accountable uses of AI.\\n\\nThe CMA says its initial review of foundational AI models is in line with instructions in the white paper, where the government talked about existing regulators conducting “detailed risk analysis” in order to be in a position to carry out potential enforcements, i.e. on dangerous, unfair and unaccountable applications of AI, using their existing powers.\\n\\nThe regulator also points to its core mission — to support open, competitive markets — as another reason for taking a look at generative AI now.\\n\\nNotably, the competition watchdog is set to get additional powers to regulate Big Tech in the coming years, under plans taken off the back-burner by prime minister Rishi Sunak’s government last month, when ministers said it would move forward with a long-trailed (but much delayed) ex ante reform aimed at digital giants’ market power.\\n\\nThe expectation is that the CMA’s Digital Markets Unit, up and running since 2021 in shadow form, will (finally) gain legislative powers in the coming years to apply pro-active “pro-competition” rules which are tailored to platforms that are deemed to have “strategic market status” (SMS). So we can speculate that providers of powerful foundational AI models may, down the line, be judged to have SMS — meaning they could expect to face bespoke rules on how they must operate vis-a-vis rivals and consumers in the U.K. market.\\n\\nThe U.K.’s data protection watchdog, the ICO, also has its eye on generative AI. It’s another existing oversight body which the government has tasked with paying special mind to AI under its plan for context-specific guidance to steer development of the tech through the application of existing laws.\\n\\nIn a blog post last month, Stephen Almond, the ICO’s executive director of regulatory risk, offered some tips and a little warning for developers of generative AI when it comes to compliance with U.K. data protection rules. “Organisations developing or using generative AI should be considering their data protection obligations from the outset, taking a data protection by design and by default approach,” he suggested. “This isn’t optional — if you’re processing personal data, it’s the law.”\\n\\nOver the English Channel in the European Union, meanwhile, lawmakers are in the process of deciding a fixed set of rules that are likely to apply to generative AI.\\n\\nNegotiations toward a final text for the EU’s incoming AI rulebook are ongoing — but currently there’s a focus on how to regulate foundational models via amendments to the risk-based framework for regulating uses of AI the bloc published in draft over two years ago.\\n\\nIt remains to be seen where the EU’s co-legislators will end up on what’s sometimes also referred to as general purpose AI. But, as we reported recently, parliamentarians are pushing for a layered approach to tackle safety issues with foundational models; the complexity of responsibilities across AI supply chains; and to address specific content concerns (like copyright) which are associated with generative AI.\\n\\nAdd to that, EU data protection law already applies to AI, of course. And privacy-focused investigations of models like ChatGPT are underway in the bloc — including in Italy where an intervention by the local watchdog led to OpenAI rushing out a series of privacy disclosures and controls last month.\\n\\nThe European Data Protection Board also recently set up a task force to support coordination between different data protection authorities on investigations of the AI chatbot. Others investigating ChatGPT include Spain’s privacy watchdog.', metadata={'source': '/content/new_articles/05-04-cma-generative-ai-review.txt'}),\n", " Document(page_content='It’s that time of week again, folks — Week in Review (WiR) time. For those new to the scene, WiR is TechCrunch’s regular newsletter that recaps the biggest tech stories over the past few days. There’s no better digest for the person on the go, we’d argue — but of course, we’re a little biased.\\n\\nBefore we get into the meat of the thing, a quick reminder that TC City Spotlight: Atlanta is fast approaching. On June 7, TechCrunch is headed to Atlanta, where we’ll host a pitch competition, a talk on the economics of equality, a panel discussion on investing in the Atlanta ecosystem and more.\\n\\nElsewhere, there’s a TechCrunch Live event with Persona and Index Ventures on May 10, which will touch on how Persona keeps pace with new threats and how Index made a prescient move to spot and back Persona early on. And we have Disrupt in San Francisco from September 19–21 — our annual conference is jam-packed with expert-led sessions and interviews with movers and shakers in the tech space.\\n\\nNow, with that out of the way, here’s the top headlines.\\n\\nmost read\\n\\nAmazon debuts free channels: Amazon is doubling down on free, ad-supported content with this week’s introduction of Fire TV Channels. The new, free and ad-supported video experience, which came to Fire TV devices this week, will be continuously updated throughout the day and integrated into several areas across the Fire TV interface, Sarah reports.\\n\\nBio update for a check: Briefly, a bug on Twitter let legacy blue check holders get their badge back by updating their bio. Readers will recall that blue checks on Twitter once signified that a user was “verified,” but now serve as an indication that they’re paying for Twitter’s premium subscription service, Twitter Blue. Verified users who chose not to pay recently faced the prospect of blue check removal — but not necessarily permanently, judging by the bug.\\n\\nGoogle ditches passwords for passkeys: This week Google rolled out passkeys to Google Account users globally, roughly a year after the company — alongside Apple, Microsoft and the FIDO Alliance — announced a partnership to broadly advance passkey adoption. With passkeys, users’ authentication synchronizes across devices through the cloud using cryptographic key pairs, allowing them to sign in to websites and apps using the same biometrics or screen-lock PIN they use to unlock their devices.\\n\\nMicrosoft debuts Pegasus: Microsoft this week announced that it’ll extend the Startup Founders Hub, its self-service platform that provides founders with free resources, including Azure credits, with a new incubator program called the Pegasus Program. Pegasus will select startups with products that “fill a market need” and give them up to $350,000 in Azure, GitHub and LinkedIn credits plus backing from advisors, as well as “access to the best Microsoft tech,” Microsoft says.\\n\\nBlue check marks come to Gmail: Google is going to start displaying a blue check mark next to select senders’ names on Gmail to verify their identity, the company said on Wednesday. The check marks will automatically appear next to companies that have adopted Gmail’s existing brand indicators for message identification feature, reports Aisha.\\n\\nOpenAI rakes in the dough: OpenAI, the startup behind the widely used conversational AI model ChatGPT, has picked up new backers. In an exclusive report, Jagmeet and Ingrid reveal that VC firms, including Sequoia Capital, Andreessen Horowitz, Thrive, K2 Global and Founders Fund, have put just over $300 million into OpenAI, valuing the company at between $27 billion and $29 billion.\\n\\nApple releases security fix: On Monday, Apple released its first batch of publicly available “rapid security” patches, aimed at quickly fixing security vulnerabilities that are under active exploitation or pose significant risks to its customers. Apple says that these patches, which are enabled by default, were intended to let customers update their devices faster than a typical software upgrade.\\n\\nMusk settles for less: A defamation case brought against Tesla chief executive Elon Musk by critic Randeep Hothi is coming to a close, reportedly costing the billionaire ten big ones. Lawyers representing Hothi — a vocal member of the TSLAQ short-seller community on Twitter who rose to prominence as a skeptic of Tesla’s gigafactory plans and “full self-driving” tech — said in a statement that Musk asked to settle the nearly three-year-old case back in March.\\n\\nA new LLM for Alexa: Amazon is building a more “generalized and capable” large language model to power Alexa, said Amazon CEO Andy Jassy during the company’s first-quarter earnings call this week. He added that although Amazon has had an LLM powering Alexa, Amazon is working on one that’s more capable than the current one.\\n\\naudio\\n\\nTechCrunch’s stable of podcasts grows by the day — and it’s all quality stuff. This week, the Equity folks covered First Republic Bank, Poparazzi’s shutdown, Databricks’ acquisition, who’s going head-to-head with Stripe, the rise of down rounds and why Bluesky had them feeling less gray. Meanwhile, Found spoke with Stefan Bauer about how his company, Marker Learning, is cutting the cost of learning disability assessments by conducting them remotely. Chain Reaction interviewed Jake Chervinsky, the chief policy officer at Blockchain Association, a nonprofit organization focused on promoting “pro-innovation” policy for the digital asset world. On The TechCrunch Podcast — which, like WiR, covers the week in tech news — Devin talked about whether Meta’s cavalier approach to compliance might finally be coming to a close. And last but not least, TechCrunch Live profiled Sam Chaudhary, the founder of ClassDojo, and Chris Farmer, the founder and CEO of SignalFire, about playing the long game in edtech, investing in companies that aren’t rushing to monetize and the “outsider advantage.”\\n\\nTechCrunch+\\n\\nTC+ subscribers get access to in-depth commentary, analysis and surveys — which you know if you’re already a subscriber. If you’re not, consider signing up. Here are a few highlights from this week:\\n\\nA cloudy future: Lyft’s equity is selling off in the wake of the U.S. ride-hailing giant’s first-quarter results and its comments regarding the current quarter, and how its new strategic posture will affect its growth and economics in the coming quarters. But there’s not necessarily cause for panic. Alex and Anna write about Lyft’s new tack and the potential upsides, of which there are several.\\n\\nDown but not out: For the past year, everyone’s been predicting that the muted exit environment and bone-dry funding market would bring a reckoning for many late-stage companies. Down rounds carry a negative connotation and are often interpreted as the fault of the company or founder. But in a market where everything seems to be heading downward, they shouldn’t imply a company or its founders made a mistake — you often simply can’t help it, Rebecca writes.\\n\\nChatGPT, meet edtech: Shares of edtech company Chegg fell off a cliff this week even after the company reported Q1 results that bested analyst expectations. In its earnings call, the company’s executives noted that ChatGPT was slowing its ability to add new subscribers, not only potentially slowing growth but also throwing uncertainty into its ability to predict its future financial results. Alex and Natasha M dig deeper.\\n\\nGet your TechCrunch fix IRL. Join us at Disrupt 2023 in San Francisco this September to immerse yourself in all things startup. From headline interviews to intimate roundtables to a jam-packed startup expo floor, there’s something for everyone at Disrupt. Save up to $800 when you buy your pass now through May 15, and save 15% on top of that with promo code WIR. Learn more.', metadata={'source': '/content/new_articles/05-06-amazon-launches-free-channels-check-marks-come-to-gmail-and-openai-raises-more-moolah.txt'}),\n", " Document(page_content='ChatGPT: Everything you need to know about the AI-powered chatbot\\n\\nChatGPT, OpenAI’s text-generating AI chatbot, has taken the world by storm. It’s able to write essays, code and more given short text prompts, hyper-charging productivity. But it also has a more…nefarious side.\\n\\nIn any case, AI tools are not going away — and indeed has expanded dramatically since its launch just a few months ago. Major brands are experimenting with it, using the AI to generate ad and marketing copy, for example.\\n\\nAnd OpenAI is heavily investing in it. ChatGPT was recently super-charged by GPT-4, the latest language-writing model from OpenAI’s labs. Paying ChatGPT users have access to GPT-4, which can write more naturally and fluently than the model that previously powered ChatGPT. In addition to GPT-4, OpenAI recently connected ChatGPT to the internet with plugins available in alpha to users and developers on the waitlist.\\n\\nHere’s a timeline of ChatGPT product updates and releases, starting with the latest, to be updated regularly. We also answer the most common FAQs (see below).\\n\\nTimeline of the most recent ChatGPT updates\\n\\nMay 3, 2023\\n\\nMeta said in a report on May 3 that malware posing as ChatGPT was on the rise across its platforms.The company said that since March 2023, its security teams have uncovered 10 malware families using ChatGPT (and similar themes) to deliver malicious software to users’ devices.\\n\\n“In one case, we’ve seen threat actors create malicious browser extensions available in official web stores that claim to offer ChatGPT-based tools,” said Meta security engineers Duc H. Nguyen and Ryan Victory in a blog post. “They would then promote these malicious extensions on social media and through sponsored search results to trick people into downloading malware.”\\n\\nApril 28, 2023\\n\\nVC firms including Sequoia Capital, Andreessen Horowitz, Thrive and K2 Global are picking up new shares, according to documents seen by TechCrunch. A source tells us Founders Fund is also investing. Altogether the VCs have put in just over $300 million at a valuation of $27 billion to $29 billion. This is separate to a big investment from Microsoft announced earlier this year, a person familiar with the development told TechCrunch, which closed in January. The size of Microsoft’s investment is believed to be around $10 billion, a figure we confirmed with our source.\\n\\nApril 25, 2023\\n\\nCalled ChatGPT Business, OpenAI describes the forthcoming offering as “for professionals who need more control over their data as well as enterprises seeking to manage their end users.”\\n\\n“ChatGPT Business will follow our API’s data usage policies, which means that end users’ data won’t be used to train our models by default,” OpenAI wrote in a blog post. “We plan to make ChatGPT Business available in the coming months.”\\n\\nApril 24, 2023\\n\\nOpenAI applied for a trademark for “GPT,” which stands for “Generative Pre-trained Transformer,” last December. Last month, the company petitioned the USPTO to speed up the process, citing the “myriad infringements and counterfeit apps” beginning to spring into existence.\\n\\nUnfortunately for OpenAI, its petition was dismissed last week. According to the agency, OpenAI’s attorneys neglected to pay an associated fee as well as provide “appropriate documentary evidence supporting the justification of special action.”\\n\\nThat means a decision could take up to five more months.\\n\\nApril 22, 2023\\n\\nAuto-GPT is an open source app created by game developer Toran Bruce Richards that uses OpenAI’s latest text-generating models, GPT-3.5 and GPT-4, to interact with software and services online, allowing it to “autonomously” perform tasks.\\n\\nDepending on what objective the tool’s provided, Auto-GPT can behave in very… unexpected ways. One Reddit user claims that, given a budget of $100 to spend within a server instance, Auto-GPT made a wiki page on cats, exploited a flaw in the instance to gain admin-level access and took over the Python environment in which it was running — and then “killed” itself.\\n\\nApril 18, 2023\\n\\nFTC chair Lina Khan and fellow commissioners warned House representatives of the potential for modern AI technologies, like ChatGPT, to be used to “turbocharge” fraud in a congressional hearing.\\n\\n“AI presents a whole set of opportunities, but also presents a whole set of risks,” Khan told the House representatives. “And I think we’ve already seen ways in which it could be used to turbocharge fraud and scams. We’ve been putting market participants on notice that instances in which AI tools are effectively being designed to deceive people can place them on the hook for FTC action,” she stated.\\n\\nApril 17, 2023\\n\\nThe company behind the popular iPhone customization app Brass, sticker maker StickerHub and others is out today with a new AI chat app called SuperChat, which allows iOS users to chat with virtual characters powered by OpenAI’s ChatGPT. However, what makes the app different from the default ChatGPT experience or the dozens of generic AI chat apps now available are the characters offered which you can use to engage with SuperChat’s AI features.\\n\\nApril 12, 2023\\n\\nItaly’s data protection watchdog has laid out what OpenAI needs to do for it to lift an order against ChatGPT issued at the end of last month — when it said it suspected the AI chatbot service was in breach of the EU’s GSPR and ordered the U.S.-based company to stop processing locals’ data.\\n\\nThe DPA has given OpenAI a deadline — of April 30 — to get the regulator’s compliance demands done. (The local radio, TV and internet awareness campaign has a slightly more generous timeline of May 15 to be actioned.)\\n\\nApril 12, 2023\\n\\nA study co-authored by scientists at the Allen Institute for AI shows that assigning ChatGPT a “persona” — for example, “a bad person,” “a horrible person” or “a nasty person” — through the ChatGPT API increases its toxicity sixfold. Even more concerning, the co-authors found having ChatGPT pose as certain historical figures, gendered people and members of political parties also increased its toxicity — with journalists, men and Republicans in particular causing the machine learning model to say more offensive things than it normally would.\\n\\nThe research was conducted using the latest version of ChatGPT, but not the model currently in preview based on OpenAI’s GPT-4.\\n\\nApril 4, 2023\\n\\nYC Demo Day’s Winter 2023 batch features no fewer than four startups that claim to be building “ChatGPT for X.” They’re all chasing after a customer service software market that’ll be worth $58.1 billion by 2023, assuming the rather optimistic prediction from Acumen Research comes true.\\n\\nHere are the YC-backed startups that caught our eye:\\n\\nYuma, whose customer demographic is primarily Shopify merchants, provides ChatGPT-like AI systems that integrate with help desk software, suggesting drafts of replies to customer tickets.\\n\\nBaselit, which uses one of OpenAI’s text-understanding models to allow businesses to embed chatbot-style analytics for their customers.\\n\\nLasso customers send descriptions or videos of the processes they’d like to automate and the company combines ChatGPT-like interface with robotic process automation (RPA) and a Chrome extension to build out those automations.\\n\\nBerriAI, whose platform is designed to help developers spin up ChatGPT apps for their organization data through various data connectors.\\n\\nApril 1, 2023\\n\\nOpenAI has started geoblocking access to its generative AI chatbot, ChatGPT, in Italy.\\n\\nItaly’s data protection authority has just put out a timely reminder that some countries do have laws that already apply to cutting edge AI: it has ordered OpenAI to stop processing people’s data locally with immediate effect. The Italian DPA said it’s concerned that the ChatGPT maker is breaching the European Union’s General Data Protection Regulation (GDPR), and is opening an investigation.\\n\\nMarch 29, 2023\\n\\nThe letter’s signatories include Elon Musk, Steve Wozniak and Tristan Harris of the Center for Humane Technology, among others. The letter calls on “all AI labs to immediately pause for at least 6 months the training of AI systems more powerful than GPT-4.”\\n\\nThe letter reads:\\n\\nContemporary AI systems are now becoming human-competitive at general tasks,[3] and we must ask ourselves: Should we let machines flood our information channels with propaganda and untruth? Should we automate away all the jobs, including the fulfilling ones? Should we develop nonhuman minds that might eventually outnumber, outsmart, obsolete and replace us? Should we risk loss of control of our civilization? Such decisions must not be delegated to unelected tech leaders. Powerful AI systems should be developed only once we are confident that their effects will be positive and their risks will be manageable.\\n\\nMarch 23, 2023\\n\\nOpenAI launched plugins for ChatGPT, extending the bots functionality by granting it access to third-party knowledge sources and databases, including the web. Available in alpha to ChatGPT users and developers on the waitlist, OpenAI says that it’ll initially prioritize a small number of developers and subscribers to its premium ChatGPT Plus plan before rolling out larger-scale and API access.\\n\\nMarch 14, 2023\\n\\nGPT-4 is a powerful image- and text-understanding AI model from OpenAI. Released March 14, GPT-4 is available for paying ChatGPT Plus users and through a public API. Developers can sign up on a waitlist to access the API.\\n\\nMarch 9, 2023\\n\\nChatGPT is generally available through the Azure OpenAI Service, Microsoft’s fully managed, corporate-focused offering. Customers, who must already be “Microsoft managed customers and partners,” can apply here for special access.\\n\\nMarch 1, 2023\\n\\nOpenAI makes another move toward monetization by launching a paid API for ChatGPT. Instacart, Snap (Snapchat’s parent company) and Quizlet are among its initial customers.\\n\\nFebruary 7, 2023\\n\\nAt a press event in Redmond, Washington, Microsoft announced its long-rumored integration of OpenAI’s GPT-4 model into Bing, providing a ChatGPT-like experience within the search engine. The announcement spurred a 10x increase in new downloads for Bing globally, indicating a sizable consumer demand for new AI experiences.\\n\\nOther companies beyond Microsoft joined in on the AI craze by implementing ChatGPT, including OkCupid, Kaito, Snapchat and Discord — putting the pressure on Big Tech’s AI initiatives, like Google.\\n\\nFebruary 1, 2023\\n\\nAfter ChatGPT took the internet by storm, OpenAI launched a new pilot subscription plan for ChatGPT called ChatGPT Plus, aiming to monetize the technology starting at $20 per month.\\n\\nDecember 8, 2022\\n\\nA week after ChatGPT was released into the wild, two developers — Steven Tey and Dom Eccleston — made a Chrome extension called ShareGPT to make it easier to capture and share the AI’s answers with the world.\\n\\nNovember 30, 2022\\n\\nGPT-3.5 broke cover with ChatGPT, a fine-tuned version of GPT-3.5 that’s essentially a general-purpose chatbot. ChatGPT can engage with a range of topics, including programming, TV scripts and scientific concepts.\\n\\nWriters everywhere rolled their eyes at the new technology, much like artists did with OpenAI’s DALL-E model, but the latest chat-style iteration seemingly broadened its appeal and audience.\\n\\nFAQs:\\n\\nWhat is ChatGPT? How does it work?\\n\\nChatGPT is a general-purpose chatbot that uses artificial intelligence to generate text after a user enters a prompt, developed by tech startup OpenAI. The chatbot uses GPT-4, a large language model that uses deep learning to produce human-like text.\\n\\nWhen did ChatGPT get released?\\n\\nNovember 30, 2022 is when ChatGPT was released for public use.\\n\\nWhat is the latest version of ChatGPT?\\n\\nBoth the free version of ChatGPT and the paid ChatGPT Plus are regularly updated with new GPT models. The most recent model is GPT-4.\\n\\nIs ChatGPT free?\\n\\nThere is a free version of ChatGPT that only requires a sign-in in addition to the paid version, ChatGPT Plus.\\n\\nWho uses ChatGPT?\\n\\nAnyone can use ChatGPT! More and more tech companies and search engines are utilizing the chatbot to automate text or quickly answer user questions/concerns.\\n\\nWhat is the difference between ChatGPT and a chatbot?\\n\\nA chatbot can be any software/system that holds dialogue with you/a person but doesn’t necessarily have to be AI-powered. For example, there are chatbots that are rules-based in the sense that they’ll give canned responses to questions.\\n\\nChatGPT is AI-powered and utilizes LLM technology to generate text after a prompt.\\n\\nCan ChatGPT write essays?\\n\\nYes.\\n\\nCan ChatGPT commit libel?\\n\\nDue to the nature of how these models work, they don’t know or care whether something is true, only that it looks true. That’s a problem when you’re using it to do your homework, sure, but when it accuses you of a crime you didn’t commit, that may well at this point be libel.\\n\\nWe will see how handling troubling statements produced by ChatGPT will play out over the next few months as tech and legal experts attempt to tackle the fastest moving target in the industry.\\n\\nDoes ChatGPT have an app?\\n\\nThere is not an app available for iPhone or Android, but users have options to enable the chatbot on their mobile devices via their browser or a third-party app that uses ChatGPT’s public API.\\n\\nWhat is the ChatGPT character limit?\\n\\nIt’s not documented anywhere that ChatGPT has a character limit. However, users have noted that there are some character limitations after around 500 words.\\n\\nDoes ChatGPT have an API?\\n\\nYes, it was released March 1, 2023.\\n\\nWhat are some sample everyday uses for ChatGPT?\\n\\nEveryday examples include programing, scripts, email replies, listicles, blog ideas, summarization, etc.\\n\\nWhat are some advanced uses for ChatGPT?\\n\\nAdvanced use examples include debugging code, programming languages, scientific concepts, complex problem solving, etc.\\n\\nHow good is ChatGPT at writing code?\\n\\nIt depends on the nature of the program. While ChatGPT can write workable Python code, it can’t necessarily program an entire app’s worth of code. That’s because ChatGPT lacks context awareness — in other words, the generated code isn’t always appropriate for the specific context in which it’s being used.\\n\\nCan you save a ChatGPT chat?\\n\\nYes. OpenAI allows users to save chats in the ChatGPT interface, stored in the sidebar of the screen. There are no built-in sharing features yet.\\n\\nAre there alternatives to ChatGPT?\\n\\nYes. There are multiple AI-powered chatbot competitors such as Together, Google’s Bard and Anthropic’s Claude, and developers are creating open source alternatives. But the latter are harder — if not impossible — to run today.\\n\\nHow does ChatGPT handle data privacy?\\n\\nOpenAI has said that individuals in “certain jurisdictions” (such as the EU) can object to the processing of their personal information by its AI models by filling out this form. This includes the ability to make requests for deletion of AI-generated references about you. Although OpenAI notes it may not grant every request since it must balance privacy requests against freedom of expression “in accordance with applicable laws”.\\n\\nThe web form for making a deletion of data about you request is entitled “OpenAI Personal Data Removal Request”.\\n\\nIn its privacy policy, the ChatGPT maker makes a passing acknowledgement of the objection requirements attached to relying on “legitimate interest” (LI), pointing users towards more information about requesting an opt out — when it writes: “See here for instructions on how you can opt out of our use of your information to train our models.”\\n\\nWhat controversies have surrounded ChatGPT?\\n\\nRecently, Discord announced that it had integrated OpenAI’s technology into its bot named Clyde where two users tricked Clyde into providing them with instructions for making the illegal drug methamphetamine (meth) and the incendiary mixture napalm.\\n\\nAn Australian mayor has publicly announced he may sue OpenAI for defamation due to ChatGPT’s false claims that he had served time in prison for bribery. This would be the first defamation lawsuit against the text-generating service.\\n\\nCNET found itself in the midst of controversy after Futurism reported the publication was publishing articles under a mysterious byline completely generated by AI. The private equity company that owns CNET, Red Ventures, was accused of using ChatGPT for SEO farming, even if the information was incorrect.\\n\\nSeveral major school systems and colleges, including New York City Public Schools, have banned ChatGPT from their networks and devices. They claim that the AI impedes the learning process by promoting plagiarism and misinformation, a claim that not every educator agrees with.\\n\\nThere have also been cases of ChatGPT accusing individuals of false crimes.\\n\\nWhere can I find examples of ChatGPT prompts?\\n\\nSeveral marketplaces host and provide ChatGPT prompts, either for free or for a nominal fee. One is PromptBase. Another is ChatX. More launch every day.\\n\\nCan ChatGPT be detected?\\n\\nPoorly. Several tools claim to detect ChatGPT-generated text, but in our tests, they’re inconsistent at best.\\n\\nAre ChatGPT chats public?\\n\\nNo. But OpenAI recently disclosed a bug, since fixed, that exposed the titles of some users’ conversations to other people on the service.\\n\\nWho owns the copyright on ChatGPT-created content or media?\\n\\nThe user who requested the input from ChatGPT is the copyright owner.\\n\\nWhat lawsuits are there surrounding ChatGPT?\\n\\nNone specifically targeting ChatGPT. But OpenAI is involved in at least one lawsuit that has implications for AI systems trained on publicly available data, which would touch on ChatGPT.\\n\\nAre there issues regarding plagiarism with ChatGPT?\\n\\nYes. Text-generating AI models like ChatGPT have a tendency to regurgitate content from their training data.', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content=\"<PERSON><PERSON> on the ‘exhilarating mix’ of thrill and hazard at the frontiers of tech 'That's always an exciting place to be — a place where nobody's ever been before.'\\n\\nVint <PERSON><PERSON> has been a near-constant influence on the internet since the days when he was helping create it in the first place. Today he wears many hats, among them VP and chief internet evangelist at Google. He is to be awarded the IEEE’s Medal of Honor at a gala in Atlanta, and ahead of the occasion he spoke with TechCrunch in a wide-ranging interview touching on his work, AI, accessibility and interplanetary internet.\\n\\nTechCrunch: To start out with, can you tell us how Google has changed in your time there?\\n\\nCerf: Well, when I joined the company in 2005, there were 5,000 people already, which is pretty damn big. And of course, my normal attire is three piece suits. The important thing is that I thought I would be raising the sartorial quotient of the company by joining. And now, almost 18 years later, there are 170-some-odd thousand people, and I have failed miserably. So I hope you don’t mind if I take my jacket off.\\n\\nGo right ahead.\\n\\nSo as you might have noticed, <PERSON> has come back to do a little bit more on the artificial intelligence side of things, which is something he’s always been interested in; I would say historically, we’ve always had an interest in artificial intelligence. But that has escalated significantly over the past decade or so. The acquisition of DeepMind was a brilliant choice. And you can see some of the outcomes first of the spectacular stuff, like playing Go and winning. And then the more productive stuff, like figuring out how 200 million proteins are folded up.\\n\\nThen there’s the large language models and the chatbots. And I think we’re still in a very peculiar period of time, where we’re trying to characterize what these things can and can’t do, and how they go off the rails, and how do you take advantage of them to do useful work? How do we get them to distinguish fact from fiction? All of that is in my view open territory, but then that’s always an exciting place to be — a place where nobody’s ever been before. The thrill of discovery and the risk of hazard create a fairly exciting mix — an exhilarating mix.\\n\\nYou gave a talk recently about, I don’t want to say the dangers of the large language models, but…\\n\\nWell, I did say there are hazards there. I was talking to a bunch of investment bankers, or VCs, and I said, you know, don’t try to sell stuff to your investors just because it’s flashy and shiny. Be cautious about going too fast and trying to apply it without figuring out how to put guardrails in place.\\n\\nI raised a question of hazard and wanting people to be more thoughtful about which applications made sense. I even suggested an analogy: you know how the Society of Automotive Engineers, they have different risk levels for the self driving cars — a risk level idea could apply to artificial intelligence and machine learning.\\n\\nFor entertainment purposes, perhaps it’s not too concerning, unless it goes down some dark path, in which case, you might want to put some friction into the system to deal with that, especially a younger user. But then, as you get to the point where you’re training these things to do medical diagnosis or make investment advice, or make decisions about whether somebody gets out of jail… now suddenly, the risk factors are extremely high.\\n\\nWe shouldn’t be unaware of those risk factors. We can, as we build applications, be prepared to detect excursions away from safe territory, so that we don’t accidentally inflict some harm by the use of these kinds of technologies.\\n\\nSo we need some kind of guardrails.\\n\\nAgain, I’m not expert in this space, but I am beginning to wonder whether we need something kind of like that in order to provide a “super-ego” for the natural language network. So when it starts to go off the rails somewhere, we can observe that that’s happening. And a second network that’s observing both the input and the output might intervene, somehow, and stop the the production of the output.\\n\\nSort of a conscience function?\\n\\nWell, it’s not quite conscience, it’s closer to executive function — the prefrontal cortex. I want to be careful, I’m only reasoning by metaphor here.\\n\\nI know that Microsoft has embarked on something like this. Their version of GPT-4 has an intermediary model like that, they call it Prometheus.\\n\\nPurely as an observation, I had the impression that the Prometheus natural language model would detect and intervene if it thought that the interactions were going down with dark path. I thought that they would implement it in such a way that before you actually say something to the interlocutor that is going down the dark path, you intervene and prevent it from going there at all.\\n\\nMy impression, though, is that it actually produces the output and then discovers that it’s produced it, but and then it says, “Oh, I shouldn’t have done that. Oh, dear, I take that back,” or “I don’t want to talk to you anymore about that.” It’s a little bit like the email that you get occasionally from the Microsoft Outlook system that says, “This person would like to withdraw the message.”\\n\\nI love when that happens… it makes me want to read the original message so badly, even if I wouldn’t have before.\\n\\nYeah, exactly. It’s sort of like putting a big red flag in there saying, boy there’s something juicy in here.\\n\\nYou mentioned the AI models, that it’s an interesting place to work. Do you get the same sort of foundational flavor that you got from working on protocols and other big shared things over the years?\\n\\nWell, what we are seeing is emergent properties of these large language models, that are not necessarily anticipated. And there have been emergent properties showing up in the protocol world. Flow control in particular is a vast headache in the online packet switch environment, and people have been tackling these problems inside and outside of Google for years.\\n\\nOne of the examples of emergent properties that I think very few of us thought about is the domain name business. Once they had value, suddenly, all kinds of emergent properties show up, people with interests that conflict and have to be resolved. Same for internet address space, it’s an even more weird environment where people actually buy IPv4 addresses for like $50 each.\\n\\nI confess to you that as I watched the auctions for IPv4 address space, I was thinking how stupid I was. When I was at the Defense Department in charge of all this, I should have allocated the slash eight, which is 16 million addresses, to myself, and just sit on it, you know, for 50 years, then sell it and retire.\\n\\nEven simple systems have the ability to surprise you. Especially when you have simple systems when a large number of them are interacting with each other. I’ve found myself not necessarily recognizing when these emergent properties will come, but I will say that whenever something gets monetized, you should anticipate there will be emergent properties and possibly unexpected behavior, all driven by greed.\\n\\nLet me ask you about some some other stuff you’re working on. I’m always happy when I see cutting-edge tech being applied to people who need it, people with disabilities, people who like just have not been addressed by the current use cases of tech. Are you still working in the accessibility community?\\n\\nI am very active in the accessibility space. At Google, we have a number of what we call employee resource groups, or ERGs. Yeah, some of them I, executive sponsor for one for Googlers who have hearing problems. And there is a disabilities oriented group, which involves employees who either have disabilities or family members that have disabilities, and they share their stories with each other because often people have similar problems, but don’t know what the solutions were for other people. Also, it’s just nice to know that you’re not alone in some of these challenges. There’s another group called the Grayglers for people that have a little gray in their hair, and I’m the executive sponsor for that. And of course, the focus of attention there is the challenges that arise as you get older, even as you think about retirement and things like that.\\n\\nWhen a lot of so-called Web 2.0 stuff came out 10 years ago, it was totally inaccessible, broke all the screen readers, all this kind of stuff. Somebody has to step in and say, look, we need to have this standard, or else you’re leaving out millions of people. So I’m always interested to hear about what interesting projects or organizations or people are out there.\\n\\nWhat I have come to believe is that engineers, being just given a set of specs that say if you do it this way, it will meet this level of the standard… that doesn’t necessarily produce intuition. You really have to have some intuition in order to make things accessible.\\n\\nSo I’ve come to the conclusion that what we really need is to show people examples of something which is not accessible, and something that is, and let them ingest as many examples as we can give them, because their neural networks will eventually figure out, what is it about this design that makes it accessible? And how do I apply that insight into the next design that I do? So, seeing what works and what doesn’t work is really important. And you often learn a lot more from what doesn’t work than you do from what does.\\n\\nThere’s a guy named Gregg Vanderheiden, who’s at the University of Maryland, he and I did a two-day event [the Future of Interface Workshop] looking at research on accessibility and trying to frame what this is going to look like over the next 10 or 20 years. It really is quite astonishing what the technology might be able to do to act as an augmenting capability for people that that need assistance. There’s great excitement, but at the same time great disappointment, because we haven’t used it as effectively as I think we could have. It’s kind of like how Alexander Graham Bell invented a telephone that can’t be used by people who are deaf, which is why he was working on it in the first place.\\n\\nIt is a funny contradiction of priorities. One thing where I do see some of the the large language and multimodal AI models helping out is that they can describe what they are seeing, even if you can’t see it. I know that one of GPT-4’s first applications was in an application for blind people to view the world around them.\\n\\nWe’re experiencing something close to that right this minute. Since I wear hearing aids, I’m making use of the captioning capability. And at the moment since this is Zoom rather than a Google Meet, there isn’t any setting on this one for closed captioning. I’m exercising the Zoom application through the Chrome browser, and Google has developed a capability for the Chrome browser to detect speech in the incoming sound.\\n\\nSo packets are coming in and they’re known to be sound, it passes through an identification system that produces a caption bar, which you can move around on the screen. And that’s been super helpful for me. For cases like this, where the application doesn’t have captioning, or for random video streaming video that might be coming in and hasn’t been captioned, the caption window automatically pops up. In theory, I think we can do this in 100 different languages, although I don’t know that we’ve activated it for more than four or five. As you say, these tools will become more and more normal, and as time goes on, people will expect the system to adapt to their needs.\\n\\nSo language translation, and speech recognition is quite powerful, but I do want to mention something that I found vaguely unsettling. Recently, I encountered an example of a conversation between a reporter and a chatbot. But he chose deliberately to take the output of the chat bot and have it spoken by the system. And he chose the style of a famous British explorer [David Attenborough].\\n\\nThe text itself was quite well formed, but coming with Attenborough’s accent just added to the weight of the assertions even when they were wrong. The confidence levels, as I’m sure you’ve seen, are very high, even when the thing doesn’t know what it’s talking about.\\n\\nThe reason I bring this up is that we are allowing in these indicators of, how should we say this, of quality, to fool us. Because in the past, they really did mean it was David Attenborough. But here it’s not, it’s just his voice. I got to thinking about this, and I realized there was an ancient example of exactly this problem that showed up 50 years ago at Xerox PARC.\\n\\nThey had a laser printer, and they had the Alto workstation, and the Bravo text editor, it meant the first draft of anything you type to be printed out beautifully formatted with lovely forms and everything else. Normally, you would never see that production quality until after everything had been edited, you know, wrestled with by everybody to get the text formatted, picture-perfect stuff. That meant the first draft stuff came out looking like it was final draft. People didn’t didn’t understand that they were nuts, that they were seeing first-round stuff, and that it wasn’t complete, or necessarily even satisfactory.\\n\\nSo it occurred to me that we’ve reached a point now where technology is fooling us into giving it more weight than it deserves, because of certain indicia that used to be indicative of the investment made in producing it. And… I’m not quite sure what to do about that.\\n\\nI don’t think anyone is!\\n\\nI think somehow or another, we need to make it clear what the provenance is of the thing that we’re looking at. Like how we needed to say this is first-draft material, you know, don’t make any assumptions. So provenance turns out to be a very important concept, especially in a world where we have the ability to imbue content with attributes that we would normally interpret in one way. Like, it’s David Attenborough speaking, and we should listen to that. And yet, which have to be, we have to think more critically about them. Because in fact, the attribute is being delivered artificially.\\n\\nAnd perhaps maliciously.\\n\\nCertainly that too. And this is why critical thinking has become an important skill. But it doesn’t work very well, unless you have enough information to understand the provenance of the material that you’re looking at. I think we are going to have to invest more in provenance and identity in order to evaluate the quality of that which we are experiencing.\\n\\nI wanted to ask you about interplanetary internet, because that whole area is extremely interesting to me.\\n\\nWell, this one, of course, gets started way back in 1998. But I’m a science fiction reader from way back way to age 10 or something, so I got quite excited when it was possible to even think about the possibility of designing and building a communication system that would span the solar system.\\n\\nThe team got started very small, and now 25 years later involves many of the space agencies around the world: JAXA, the Korean Space Agency, NASA and so on. And a growing team of people who are either government funded to do space-based research, or volunteers. There’s a special interest group called the interplanetary networking Special Interest Group, which is part of the Internet Society — that thing got started in 1998. But it has now grown to like 900 people around the world who are interested in this stuff.\\n\\nWe’ve standardized this stuff, we’re on version seven of it, we’re running it up in the International Space Station. It’s intended to be available for the return to the moon and Artemis missions. I’m not going to see the end result of all this, but I’m going to see the first couple of chapters. And I’m very excited about that, because it’s not crazy to actually think about. Like all my other projects, it takes a long time. Patience and persistence!\\n\\nFor something like this it must have been a real challenge, but also a very familiar one. In some ways building something like this is what you’ve been doing your whole career. This is just a different set of restraints and capabilities.\\n\\nYou put your finger on it, exactly right. This is in a different parametric space than the one that works for TCP/IP. And we’re still bumping into some really interesting problems, especially where you have TCP/IP networks running on the moon, for example, locally and interconnecting with other internets on other planets, going through the interplanetary protocol. What does that look like? You know, which IP addresses should be used? We have to figure out, well, how the hell does the Domain Name System work in the context of internets that aren’t on the planet? And it’s really fun!\", metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='Partners of 3one4 Capital, a venture capital firm in India, recently went on a road show to raise a new fund. Within two and a half months, at the height of the worsening global economy, they had secured $200 million. It’s the fourth marquee fund for the Bengaluru-headquartered fund, whose portfolio includes four unicorn startups.\\n\\nThe fund, sixth overall for 3one4 Capital, was oversubscribed to $250 million but the firm is accepting only $200 million to keep itself lean and disciplined, said <PERSON><PERSON><PERSON>, co-founder and partner at 3one4 Capital. The firm’s decision to limit the fund size is emblematic of its strategic choices, which have set it apart from other Indian venture firms.\\n\\n“We are known to give good returns. Our performance has been benchmarked among the best leading performing funds in the space. So we asked ourselves the hard questions, can we continue our performance with a larger fund size? Do we even need that much capital for the early-stage?” said <PERSON><PERSON> in an interview with TechCrunch.\\n\\nIn recent years, a surge of venture capital firms in India have raised unprecedentedly large funds, sparking concerns about the responsible allocation of this capital, particularly for early-stage startups. Critics question whether there are enough viable companies in the Indian market to absorb and effectively utilize such significant investments.\\n\\nPai, pictured above, asserts that there is ample room for more Indian companies to pursue IPOs, as the nation’s IPO market has proven successful and well-regulated for institutional investors. He anticipates a transformation in India’s stock index, with an increasing number of tech companies, apps, services, fintech, and payment solutions becoming part of the index.\\n\\nDespite this, Pai acknowledges that the Indian market has yet to fully realize its potential for mergers and acquisitions. Although there has been growth in M&A activity—increasing three to four times in the past five years—it remains below expectations. For the Indian market to flourish, Pai emphasizes the need for a more robust M&A landscape.\\n\\nOver the last half-decade, numerous Indian venture firms have shifted their attention to early-stage investments. Despite this increased focus, the market continues to depend on international investors to support mid- and growth-stage deals, highlighting the need for further growth in India’s venture capital ecosystem. “We have high performing mutual funds and PEs. We hope that more of these firms will launch dedicated funds for Indian startups,” he said.\\n\\nHalf of the capital in the new fund for 3one4 has come from Indian investors, another aspect that differentiates the firm from many of its peers. All the systemically important Indian banks, and the top five local banks by market cap overall have invested in the new fund. Eight of the top 10 mutual fund operators are also LPs in the new fund, said Pai. “We are also proud to have leading global endowments, sovereigns and insurance companies as LPs,” he said.\\n\\n“We want to be India’s leading homegrown venture capital firm. We are based here, we invest here – we don’t want to invest in Southeast Asia – and our fund size and strategy are aligned with opportunities in India. As our companies have IPO-ed over the years, we have seen the importance of having India’s largest institutions working with us to help build those companies. It would be difficult if we didn’t have banks to help our companies from everything from revenue collection to payrolls. And mutual funds are buyers, book runners and market makers for IPOs and them buying the stock gives a vote of confidence to the market,” he said.\\n\\n3one4, which focuses largely on early-stage and in sectors including direct-to-consumer tech, media and content, fintech, deep technology and SaaS and enterprise automation, today manages about $750 million in AUM and its portfolio includes HR platform Darwinbox, business-to-business focused neobank Open, consumer-focused neobank Jupiter, Licious, a direct-to-consumer brand that sells meat, local social networks Koo and Lokal, entertainment service Kuku FM, fintech Raise Financial, and gaming firm Loco.\\n\\n3one4 Capital has gained a reputation for its contrarian investment approach, as exemplified by its early investment in Licious. Over five years ago, the prevailing opinion held that India’s price-sensitive market would not pay a premium for online meat delivery. However, Licious has since grown into one of South Asia’s largest direct-to-consumer brands, with a presence in approximately two dozen cities across India.\\n\\nAnother example of 3one4’s daring investments is Darwinbox, a bet made at a time when most investors doubted the ability of Indian SaaS companies to expand internationally or garner sufficient local business subscriptions.\\n\\n3one4 Capital’s contrarian approach extends to the investments it has deliberately avoided as well. In 2021, amidst a frenzy of investment activity in the crypto space, nearly every fund in India sought opportunities and backed crypto startups. However, 3one4 Capital, after thorough evaluation of the sector, chose not to make any investments in crypto.\\n\\nThe firm, which employs 28 people, is also focusing on setting new standards in transparency and governance for itself. It’s the first VC to be a signatory to UN PRI, it said. “We have to report, behave, act and look a certain way. We have to look like the fiduciary of best institutions in the world, and then and only then we quality to tell our portfolio founders that this is how we want to create best in class companies with you,” said Pai.', metadata={'source': '/content/new_articles/05-07-3one4-capital-driven-by-contrarian-bets-raises-200-million-new-fund.txt'}),\n", " Document(page_content='Databricks today announced that it has acquired Okera, a data governance platform with a focus on AI. The two companies did not disclose the purchase price. According to Crunchbase, Okera previously raised just under $30 million. Investors include Felicis, Bessemer Venture Partners, Cyber Mentor Fund, ClearSky and Emergent Ventures.\\n\\nData governance was already a hot topic, but the recent focus on AI has highlighted some of the shortcomings of the previous approach to it, Databricks notes in today’s announcement. “Historically, data governance technologies, regardless of sophistication, rely on enforcing control at some narrow waist layer and require workloads to fit into the ‘walled garden’ at this layer,” the company explains in a blog post. That approach doesn’t work anymore in the age of large language models (LLMs) because the number of assets is growing too quickly (in part because so much of it is machine-generated) and because the overall AI landscape is changing so quickly, standard access controls aren’t able to capture these changes quickly enough.\\n\\nOkera then uses an AI-powered system that can automatically discover and classify personally identifiable information, tag it and apply rules to this (with a focus on the metadata), using a no-code interface.\\n\\nAs the Databricks team stressed, that’s one of the reasons the company was interested in acquiring Okera, but the other is the service’s isolation technology, which can enforce governance control on arbitrary workloads without any major overhead. This technology is still in private preview but was likely one of the major reasons Databricks acquired the company.\\n\\nDatabricks, which launched its own LLM a few weeks ago, plans to integrate Okera’s technology into its Unity Catalog, its existing governance solution of data and AI assets. The company also noted that the acquisition will enable Databricks to expose additional APIs that its own data governance partners will be able to use to provide solutions to their customers.\\n\\nWith this acquisition, Databricks is also bringing Okera co-founder and CEO Nong Li on board. Li created the Apache Parquet data storage format and was actually briefly an engineer at Databricks between working at Cloudera and before starting Okera, where he was the founding CTO and became the CEO in February 2022.\\n\\n“As data continues to grow in volume, velocity, and variety across different applications, CIOs, CDOs, and CEOs across the board have to balance those two often conflicting initiatives – not to mention that historically, managing access policies across multiple clouds has been painful and time-consuming,” writes Li in today’s announcement. “Many organizations don’t have enough technical talent to manage access policies at scale, especially with the explosion of LLMs. What they need is a modern, AI-centric governance solution. We could not be more excited to join the Databricks team and to bring our expertise in building secure, scalable and simple governance solutions for some of the world’s most forward-thinking enterprises.”\\n\\nIf you know more about this acquisition, you can contact Frederic on Signal at (860) 208-3416 or by email (<EMAIL>). You can also reach us via SecureDrop.', metadata={'source': '/content/new_articles/05-03-databricks-acquires-ai-centric-data-governance-platform-okera.txt'}),\n", " Document(page_content='AI startup Hugging Face and ServiceNow Research, ServiceNow’s R&D division, have released StarCoder, a free alternative to code-generating AI systems along the lines of GitHub’s Copilot.\\n\\nCode-generating systems like DeepMind’s AlphaCode; Amazon’s CodeWhisperer; and OpenAI’s Codex, which powers Copilot, provide a tantalizing glimpse at what’s possible with AI within the realm of computer programming. Assuming the ethical, technical and legal issues are someday ironed out (and AI-powered coding tools don’t cause more bugs and security exploits than they solve), they could cut development costs substantially while allowing coders to focus on more creative tasks.\\n\\nAccording to a study from the University of Cambridge, at least half of developers’ efforts are spent debugging and not actively programming, which costs the software industry an estimated $312 billion per year. But so far, only a handful of code-generating AI systems have been made freely available to the public — reflecting the commercial incentives of the organizations building them (see: Replit).\\n\\nStarCoder, which by contrast is licensed to allow for royalty-free use by anyone, including corporations, was trained on over 80 programming languages as well as text from GitHub repositories, including documentation and programming notebooks. StarCoder integrates with Microsoft’s Visual Studio Code code editor and, like OpenAI’s ChatGPT, can follow basic instructions (e.g., “create an app UI”) and answer questions about code.\\n\\nCongratulations to all the @BigCodeProject contributors that worked tirelessly over the last 6+ months to bring the vision of releasing a responsibly developed 15B parameter Code LLM to fruition. We cannot thank you enough for the collaboration & contributions to the community. https://t.co/282sCRJq3k — ServiceNow Research (@ServiceNowRSRCH) May 4, 2023\\n\\nLeandro von Werra, a machine learning engineer at Hugging Face and a co-lead on StarCoder, claims that StarCoder matches or outperforms the AI model from OpenAI that was used to power initial versions of Copilot.\\n\\n“One thing we learned from releases such as Stable Diffusion last year is the creativity and capability of the open-source community,” von Werra told TechCrunch in an email interview. “Within weeks of the release the community had built dozens of variants of the model as well as custom applications. Releasing a powerful code generation model allows anybody to fine-tune and adapt it to their own use-cases and will enable countless downstream applications.”\\n\\nBuilding a model\\n\\nStarCoder is a part of Hugging Face’s and ServiceNow’s over-600-person BigCode project, launched late last year, which aims to develop “state-of-the-art” AI systems for code in an “open and responsible” way. Hugging Face supplied an in-house compute cluster of 512 Nvidia V100 GPUs to train the StarCoder model.\\n\\nVarious BigCode working groups focus on subtopics like collecting datasets, implementing methods for training code models, developing an evaluation suite and discussing ethical best practices. For example, the Legal, Ethics and Governance working group explored questions on data licensing, attribution of generated code to original code, the redaction of personally identifiable information (PII), and the risks of outputting malicious code.\\n\\nInspired by Hugging Face’s previous efforts to open source sophisticated text-generating systems, BigCode seeks to address some of the controversies arising around the practice of AI-powered code generation. The nonprofit Software Freedom Conservancy among others has criticized GitHub and OpenAI for using public source code, not all of which is under a permissive license, to train and monetize Codex. Codex is available through OpenAI’s and Microsoft’s paid APIs, while GitHub recently began charging for access to Copilot.\\n\\nFor their parts, GitHub and OpenAI assert that Codex and Copilot — protected by the doctrine of fair use, at least in the U.S. — don’t run afoul of any licensing agreements.\\n\\n“Releasing a capable code-generating system can serve as a research platform for institutions that are interested in the topic but don’t have the necessary resources or know-how to train such models,” von Werra said. “We believe that in the long run this leads to fruitful research on safety, capabilities and limits of code-generating systems.”\\n\\nUnlike Copilot, the 15-billion-parameter StarCoder was trained over the course of several days on an open source dataset called The Stack, which has over 19 million curated, permissively licensed repositories and more than six terabytes of code in over 350 programming languages. In machine learning, parameters are the parts of an AI system learned from historical training data and essentially define the skill of the system on a problem, such as generating code.\\n\\nBecause it’s permissively licensed, code from The Stack can be copied, modified and redistributed. But the BigCode project also provides a way for developers to “opt out” of The Stack, similar to efforts elsewhere to let artists remove their work from text-to-image AI training datasets.\\n\\nThe BigCode team also worked to remove PII from The Stack, such as names, usernames, email and IP addresses, and keys and passwords. They created a separate dataset of 12,000 files containing PII, which they plan to release to researchers through “gated access.”\\n\\nBeyond this, the BigCode team used Hugging Face’s malicious code detection tool to remove files from The Stack that might be considered “unsafe,” such as those with known exploits.\\n\\nThe privacy and security issues with generative AI systems, which for the most part are trained on relatively unfiltered data from the web, are well-established. ChatGPT once volunteered a journalist’s phone number. And GitHub has acknowledged that Copilot may generate keys, credentials and passwords seen in its training data on novel strings.\\n\\n“Code poses some of the most sensitive intellectual property for most companies,” von Werra said. “In particular, sharing it outside their infrastructure poses immense challenges.”\\n\\nTo his point, some legal experts have argued that code-generating AI systems could put companies at risk if they were to unwittingly incorporate copyrighted or sensitive text from the tools into their production software. As Elaine Atwell notes in a piece on Kolide’s corporate blog, because systems like Copilot strip code of its licenses, it’s difficult to tell which code is permissible to deploy and which might have incompatible terms of use.\\n\\nIn response to the criticisms, GitHub added a toggle that lets customers prevent suggested code that matches public, potentially copyrighted content from GitHub from being shown. Amazon, following suit, has CodeWhisperer highlight and optionally filter the license associated with functions it suggests that bear a resemblance to snippets found in its training data.\\n\\nCommercial drivers\\n\\nSo what does ServiceNow, a company that deals mostly in enterprise automation software, get out of this? A “strong-performing model and a responsible AI model license that permits commercial use,” said Harm de Vries, the lead of the Large Language Model Lab at ServiceNow Research and the co-lead of the BigCode project.\\n\\nOne imagines that ServiceNow will eventually build StarCoder into its commercial products. The company wouldn’t reveal how much, in dollars, it’s invested in the BigCode project, save that the amount of donated compute was “substantial.”\\n\\n“The Large Language Models Lab at ServiceNow Research is building up expertise on the responsible development of generative AI models to ensure the safe and ethical deployment of these powerful models for our customers,” de Vries said. “The open-scientific research approach to BigCode provides ServiceNow developers and customers with full transparency into how everything was developed and demonstrates ServiceNow’s commitment to making socially responsible contributions to the community.”\\n\\nStarCoder isn’t open source in the strictest sense. Rather, it’s being released under a licensing scheme, OpenRAIL-M, that includes “legally enforceable” use case restrictions that derivatives of the model — and apps using the model — are required to comply with.\\n\\nFor example, StarCoder users must agree not to leverage the model to generate or distribute malicious code. While real-world examples are few and far between (at least for now), researchers have demonstrated how AI like StarCoder could be used in malware to evade basic forms of detection.\\n\\nWhether developers actually respect the terms of the license remains to be seen. Legal threats aside, there’s nothing at the base technical level to prevent them from disregarding the terms to their own ends.\\n\\nThat’s what happened with the aforementioned Stable Diffusion, whose similarly restrictive license was ignored by developers who used the generative AI model to create pictures of celebrity deepfakes.\\n\\nBut the possibility hasn’t discouraged von Werra, who feels the downsides of not releasing StarCoder aren’t outweighed by the upsides.\\n\\n“At launch, StarCoder will not ship as many features as GitHub Copilot, but with its open-source nature, the community can help improve it along the way as well as integrate custom models,” he said.\\n\\nThe StarCoder code repositories, model training framework, dataset-filtering methods, code evaluation suite and research analysis notebooks are available on GitHub as of this week. The BigCode project will maintain them going forward as the groups look to develop more capable code-generating models, fueled by input from the community.\\n\\nThere’s certainly work to be done. In the technical paper accompanying StarCoder’s release, Hugging Face and ServiceNow say that the model may produce inaccurate, offensive, and misleading content as well as PII and malicious code that managed to make it past the dataset filtering stage.', metadata={'source': '/content/new_articles/05-04-hugging-face-and-servicenow-release-a-free-code-generating-model.txt'}),\n", " Document(page_content=\"Microsoft doubles down on AI with new Bing features The company's betting the farm on generative AI\\n\\nMicrosoft is embarking on the next phase of Bing’s expansion. And — no surprise — it heavily revolves around AI.\\n\\nAt a preview event this week in New York City, Microsoft execs including <PERSON>, the CVP and consumer chief marketing officer, gave members of the press, including this reporter, a look at the range of features heading to Bing over the next few days, weeks and months.\\n\\nThey don’t so much reinvent the wheel as they build on what Microsoft has injected into the Bing experience over the past three months or so. Since launching Bing Chat, its AI-powered chatbot powered by OpenAI’s GPT-4 and DALL-E 2 models, Microsoft says that visitors to Bing — which has grown to exceed 100 million daily active users — have engaged in over half a billion chats and created more than 200 million images.\\n\\nLooking ahead, <PERSON> will become more visual, thanks to more image- and graphic-centric answers in Bing Chat. It’ll also become more personalized, with capabilities that’ll allow users to export their Bing Chat histories and draw in content from third-party plugins (more on those later). And it’ll embrace multimodality, at least in the sense that <PERSON> will be able to answer questions within the context of images.\\n\\n“I think it’s safe to say that we’re underway with the transformation of search,” <PERSON><PERSON> said in prepared remarks. “In our minds, we think that today will be the start of the next generation of this ‘search mission.'”\\n\\nOpen, and visual\\n\\nAs of today, the new Bing — the one with Bing Chat — is now available waitlist-free. Anyone can try it out by signing in with a Microsoft Account.\\n\\nIt’s more or less the experience that launched several months ago. But as alluded to earlier, Bing Chat will soon respond with images — at least where it makes sense. Answers to questions (e.g. “Where is Machu Picchu?”) will be accompanied by relevant images if any exist, much like the standard Bing search flow but condensed into a card-like interface.\\n\\nIn a demo at the event, a spokesperson typed the question “Does the saguaro cactus grow flowers?” and Bing Chat pulled up a paragraph-long response alongside an image of the cactus in question. For me, it evoked the “knowledge panels” in Google Search.\\n\\nMicrosoft isn’t saying which categories of content, exactly, might trigger an image. But it does have filtering in place to prevent explicit images from appearing — or so it claims.\\n\\nSarah Bird, the head of responsible AI at Microsoft, told me that Bing Chat benefits from the filtering and moderation already in place with Bing search. Beyond this, Bing Chat uses a combination of “toxicity classifiers,” or AI models trained to detect potentially harmful prompts, and blacklists to keep the chat relatively clean.\\n\\nThose measures didn’t prevent Bing Chat from going off the rails when it first rolled out in preview in early February, it’s worth noting. Our coverage found the chatbot spouting vaccine misinformation and writing a hateful screed from the perspective of Adolf Hitler. Other reporters got it to make threats, claim multiple identities and even shame them for admonishing it.\\n\\nIn another knock against Microsoft, the company just a few months ago laid off the ethics and society team within its larger AI organization. The move left Microsoft without a dedicated team to ensure its AI principles are closely tied to product design.\\n\\nBird, though, asserts that meaningful progress has been made and that these sorts of AI issues aren’t solved overnight — public though Bing Chat may be. Among other measures, a team of human moderators is in place to watch for abuse, she said, such as users attempting to use Bing Chat to generate phishing emails.\\n\\nBut — as members of the press weren’t given the chance to interact with the latest version of Bing beyond curated demos — I can’t say to what extent all that’s made a difference. It’ll doubtless become clear once more folks get their hands on it.\\n\\nOne aspect of Bing Chat that is improving is the transparency around its responses — specifically responses of a fact-based nature. Soon, when asked to summarize a document or about the contents a document (e.g. “what does this page say about the Brooklyn Bridge?”), whether a 20-page PDF or a Wikipedia article, Bing Chat will include citations indicating from where in the text the information came from. Clicking on them will highlight the corresponding passage.\\n\\nProductivity emergent\\n\\nIn another new feature on the visual front, Bing Chat will be able to create charts and graphs when fed the right prompt and data. Previously, asking something like “Which are the most populous cities in Brazil?” would yield a basic list of results. But in a near-future preview, Bing Chat will present those results visually and in the chart type of a user’s choosing.\\n\\nThis seemingly represents a step for Bing toward a full-blown productivity platform, particularly when paired with the enhanced text-to-image generation capabilities coming down the pipeline.\\n\\nIn the coming weeks, Bing Image Creator — Microsoft’s tool that can generate images from text prompts, powered by DALL-E 2 — will understand more languages aside from English (over 100 total). As with English, users will be able to refine the images they generate with follow-up prompts (e.g. “Make an image of a bunny rabbit,” followed by “now make the fur pink”).\\n\\nGenerative art AI has been in the headlines a lot, lately — and not for the most optimistic of reasons necessarily.\\n\\nPlaintiffs have brought several lawsuits against OpenAI and its rival vendors, alleging that copyrighted data — mostly art — was used without their permission to train generative models like DALL-E 2. Generative models “learn” to create art and more by “training” on sample images and text, usually scraped indiscriminately from the public web.\\n\\nI asked Bird about whether Microsoft is exploring ways to compensate creators whose work was swept up in training data, even if the company’s official position is that it’s a matter of fair use. Several platforms launching generative AI tools, including Shutterstock, have kick-started creators funds along these lines. Others, like Spawning, are creating mechanisms to let artists opt out of AI model training altogether.\\n\\nBird implied that these issues will eventually have to be confronted — and that content creators deserve some form of recompense. But she wasn’t willing to commit to anything concrete this week.\\n\\nMultimodal search\\n\\nElsewhere on the image front, Bing Chat is gaining the ability to understand images as well as text. Users will be able to upload images and search the web for related content, for example copying a link to an image of a crocheted octopus and asking Bing Chat the question “how do I make that?” to get step-by-step instructions.\\n\\nMultimodality powers the new page context function in the Edge app for mobile, as well. Users will be able to ask questions in Bing Chat related to the mobile page they’re viewing.\\n\\nMicrosoft wouldn’t say either way, but it seems likely that these new multimodal abilities stem from GPT-4, which can understand images in addition to text. When OpenAI announced GPT-4, it didn’t make the model’s image understanding capabilities available to all customers — and still hasn’t. I’d wager that Microsoft, though, being a major investor in and close collaborator with OpenAI, has some sort of privileged access.\\n\\nAny image upload tool can be abused, of course, which is why Microsoft is employing automated filtering and hashing to block illicit uploads, according to Bird. The jury’s out on how well these work, though — we weren’t given the chance to test image uploads ourselves.\\n\\nNew chat features\\n\\nMultimodality and new visual features aren’t all that’s coming to Bing Chat.\\n\\nSoon, Bing Chat will store users’ chat histories, letting them pick up where they left off and return to previous chats when they wish. It’s an experience akin to the chat history feature OpenAI recently brought to ChatGPT, showing a list of chats and the bot’s responses to each of those chats.\\n\\nThe specifics of the chat history feature have yet to be ironed out, like how long chats will be stored, exactly. But users will be able to delete their history at any time regardless, Microsoft says — addressing the criticisms several European Union governments had against ChatGPT.\\n\\nBing Chat will also gain export and share functionalities, letting users share conversations on social media or to a Word document. Dena Saunders, a partner GM in Microsoft’s web experiences team, told TechCrunch that a more robust copy-and-paste system is in the works — but not in preview just yet — for graphs and images created through Bing Chat.\\n\\nPerhaps the most transformative addition to Bing Chat, though, is plugins. From partners like OpenTable and Wolfram Alpha, plugins greatly extend what Bing Chat can do, for example helping users book a reservation or create visualizations and get answers to challenging science and math questions.\\n\\nLike chat history, the not-yet-live plugins functionality is in the very preliminary stages. There’s no plugins marketplace to speak of; plugins can be toggled on or off from the Bing Chat web interface.\\n\\nSaunders hinted, but wouldn’t confirm, that the Bing Chat plugins scheme was associated with — or perhaps identical to — OpenAI’s recently introduced plugins for ChatGPT. That’d certainly make sense, given the similarities between the two.\\n\\nEdge, refreshed\\n\\nBing Chat is available through Edge as well as the web, of course. And Edge is getting a fresh coat of paint alongside Bing Chat.\\n\\nFirst previewed in February, the new and improved Edge features rounded corners in line with Microsoft’s Windows 11 design philosophy. Elements in the browser are now more “containerized,” as one Microsoft spokesperson put it, and there’s subtle tweaks throughout, like the Microsoft Account image moving left-of-center.\\n\\nIn Compose, Edge’s Bing Chat-powered tool that can write emails and more given a basic prompt (e.g. “write an invitation to my dog’s birthday party”), a new option lets users adjust the length, phrasing and tone of the generated text to nearly anything they’d like. Type in the desired tone, and Bing Chat will write a message to match — Bird says filters are in place to prevent the use of clearly problematic tones, like “hateful” or “racist.”\\n\\nFar more intriguing than Compose, though — at least to me — are actions in Edge, which translate certain Bing Chat prompts into automations.\\n\\nTyping a command like “bring my passwords from another browser” in Bing Chat in the Edge sidebar opens Edge’s browsing data settings page, while the prompt “play ‘The Devil Wears Prada'” pulls up a list of streaming options including Vudu and (predictably) the Microsoft Store. There’s even an action that automatically organizes — and color-coordinates — browsing tabs.\\n\\nActions are in a primitive stage at present. But it’s clear where Microsoft’s going, here. One imagines actions eventually expanding beyond Edge to reach other Microsoft products, like Office 365, and perhaps one day the whole Windows desktop.\\n\\nSaunders wouldn’t confirm or deny that this is the endgame. “Stay tuned for Microsoft Build,” she told me, referring to Microsoft’s upcoming developer conference. We shall.\", metadata={'source': '/content/new_articles/05-04-microsoft-doubles-down-on-ai-with-new-bing-features.txt'}),\n", " Document(page_content=\"Generative AI is pretty impressive in terms of its fidelity these days, as viral memes like <PERSON><PERSON><PERSON><PERSON> would suggest. The latest systems can conjure up scenescapes from city skylines to cafes, creating images that appear startlingly realistic — at least on first glance.\\n\\nBut one of the longstanding weaknesses of text-to-image AI models is, ironically, text. Even the best models struggle to generate images with legible logos, much less text, calligraphy or fonts.\\n\\nBut that might change.\\n\\nLast week, DeepFloyd, a research group backed by Stability AI, unveiled DeepFloyd IF, a text-to-image model that can “smartly” integrate text into images. Trained on a dataset of more than a billion images and text, DeepFloyd IF, which requires a GPU with at least 16GB of RAM to run, can create an image from a prompt like “a teddy bear wearing a shirt that reads ‘Deep Floyd'” — optionally in a range of styles.\\n\\nDeepFloyd IF is available in open source, licensed in a way that prohibits commercial use — for now. The restriction was likely motivated by the current tenuous legal status of generative AI art models. Several commercial model vendors are under fire from artists who allege the vendors are profiting from their work without compensating them by scraping that work from the web without permission.\\n\\nBut NightCafe, the generative art platform, was granted early access to DeepFloyd IF.\\n\\nNightCafe CEO <PERSON> spoke to TechCrunch about what makes DeepFloyd IF different from other text-to-image models and why it might represent a significant step forward for generative AI.\\n\\nAccording to Russell, DeepFloyd IF’s design was heavily inspired by Google’s Imagen model, which was never released publicly. In contrast to models like OpenAI’s DALL-E 2 and Stable Diffusion, DeepFloyd IF uses multiple different processes stacked together in a modular architecture to generate images.\\n\\nWith a typical diffusion model, the model learns how to gradually subtract noise from a starting image made almost entirely of noise, moving it closer step by step to the target prompt. DeepFloyd IF performs diffusion not once but several times, generating a 64x64px image then upscaling the image to 256x256px and finally to 1024x1024px.\\n\\nWhy the need for multiple diffusion steps? DeepFloyd IF works directly with pixels, Russell explained. Diffusion models are for the most part latent diffusion models, which essentially means they work in a lower-dimensional space that represents a lot more pixels but in a less accurate way.\\n\\nThe other key difference between DeepFloyd IF and models such as Stable Diffusion and DALL-E 2 is that the former uses a large language model to understand and represent prompts as a vector, a basic data structure. Due to the size of the large language model embedded in DeepFloyd IF’s architecture, the model is particularly good at understanding complex prompts and even spatial relationships described in prompts (e.g. “a red cube on top of a pink sphere”).\\n\\n“It’s also very good at generating legible and correctly spelled text in images, and can even understand prompts in multiple languages,” Russell added. “Of these capabilities, the ability to generate legible text in images is perhaps the biggest breakthrough to make DeepFloyd IF stand out from other algorithms.”\\n\\nBecause DeepFloyd IF can pretty capably generate text in images, Russell expects it to unlock a wave of new generative art possibilities — think logo design, web design, posters, billboards and even memes. The model should also be much better at generating things like hands, he says, and — because it can understand prompts in other languages — it might be able to create text in those languages, too.\\n\\n“NightCafe users are excited about DeepFloyd IF largely because of the possibilities that are unlocked by generating text in images,” Russell said. “Stable Diffusion XL was the first open source algorithm to make headway on generating text — it can accurately generate one or two words some of the time — but it’s still not good enough at it for use cases where text is important.”\\n\\nThat’s not to suggest DeepFloyd IF is the holy grail of text-to-image models. Russell notes that the base model doesn’t generate images that are quite as aesthetically pleasing as some diffusion models, although he expects fine-tuning will improve that.\\n\\nBut the bigger question, to me, is to what degree DeepFloyd IF suffers from the same flaws as its generative AI brethren.\\n\\nA growing body of research has turned up racial, ethnic, gender and other forms of stereotyping in image-generating AI, including Stable Diffusion. Just this month, researchers at AI startup Hugging Face and Leipzig University published a tool demonstrating that models including Stable Diffusion and OpenAI’s DALL-E 2 tend to produce images of people that look white and male, especially when asked to depict people in positions of authority.\\n\\nThe DeepFloyd team, to their credit, note the potential for biases in the fine print accompanying DeepFloyd IF:\\n\\nTexts and images from communities and cultures that use other languages are likely to be insufficiently accounted for. This affects the overall output of the model, as white and western cultures are often set as the default.\\n\\nAside from this, DeepFloyd IF, like other open source generative models, could be used for harm, like generating pornographic celebrity deepfakes and graphic depictions of violence. On the official webpage for DeepFloyd IF, the DeepFloyd team says that they used “custom filters” to remove watermarked, “NSFW” and “other inappropriate content” from the training data.\\n\\nBut it’s unclear exactly which content was removed — and how much might’ve been missed. Ultimately, time will tell.\", metadata={'source': '/content/new_articles/05-05-with-deepfloyd-generative-ai-art-gets-a-text-upgrade.txt'}),\n", " Document(page_content='Welcome to The Interchange! If you received this in your inbox, thank you for signing up and your vote of confidence. If you’re reading this as a post on our site, sign up here so you can receive it directly in the future. Every week, we’ll take a look at the hottest fintech news of the previous week. This will include everything from funding rounds to trends to an analysis of a particular space to hot takes on a particular company or phenomenon. There’s a lot of fintech news out there and it’s our job to stay on top of it — and make sense of it — so you can stay in the know. — <PERSON> and <PERSON>\\n\\nBusy, busy, busy\\n\\nIt was a busy week in startup and venture lands, and the fintech space was no exception.\\n\\nIn the venture world, I reported on <PERSON>’s departure from Fin Capital earlier this year and the fact that he has since started a new venture firm called Audere Capital. The circumstances around his departure remain fuzzy, but one source speculated that tension arose between <PERSON><PERSON><PERSON> and <PERSON> founding partner <PERSON> over some of the goings-on at alternative financing startup Pipe last year. More details here.\\n\\nWe also wrote about Tellus, a startup that raised $16 million in an <PERSON><PERSON><PERSON>–led seed round of funding last year that is now being scrutinized by the U.S. government. When I interviewed the company’s co-founder, <PERSON>, last year, I admit I was a little bit skeptical of any company that would bet on people agreeing to high-interest mortgage rates to upgrade their homes (think 9%!) and using customer savings deposits to fund such loans. When I asked Lee if this was risky, he admitted it was but insisted that Tellus utilized “very strict underwriting criteria” and had not yet seen any defaults “because the majority of its borrowers go on to soon refinance their loans at more favorable terms.” Well, last week U.S. Senator Sherrod Brown, chairman of the U.S. Senate Committee on Banking, Housing, and Urban Affairs, wrote a letter to FDIC chairman Martin Gruenberg expressing concerns about Tellus’s claims. In that letter, Brown pressed the FDIC to review Tellus’s business practices “to ensure that customers are protected from financial fraud and abuse.” In a twist, I discovered that Lee was married to a16z general partner Connie Chan (not sure if he still is). Neither he nor the venture firm commented on the senator’s concerns but Tellus CEO/CTO Jeromee Johnson did provide me with a statement via email. Read more here.\\n\\nInfrastructure continues to be resilient, even in a downturn. This week alone, I wrote about two payments infrastructure companies making moves, and my colleague Ingrid Lunden wrote about Stripe’s latest customer win. For starters, I covered Finix officially becoming a payments processor — a natural evolution really for a company that has slowly been expanding its offerings. In case you forgot, Finix is a startup that Sequoia backtracked on investing in after Stripe (an existing portfolio company) expressed concerns about being too competitive. (Finix got to keep its $21 million, though!) Now that it directly connects to all major U.S. card networks — American Express, Discover, Mastercard and Visa — and no longer relies on a third-party processor, Finix says it’s able to offer businesses “instant onboarding, improved economics and opportunities for lowering interchange fees.” I talked with CEO and co-founder Richie Serna all about it, and why he thinks what Finix has built is different from what legacy players and Stripe have on the market. I also wrote about Liquido, a Mountain View, California–based startup aiming to be the “Stripe of Latin America,” and more. Index Ventures’ Mark Fiorentino led two funding rounds totaling $26 million into the company in 2021. Interestingly, prior to joining Index, Fiorentino helped build and lead business strategy and finance at Stripe from 2015 to 2019. And Ingrid wrote about Stripe landing Uber as a customer, which was a bit unexpected considering that rival Lyft has been a longtime marquee customer of the company.\\n\\nAnd, last but not least, corporate card and spend management startup Brex announced last week a global expansion of its Empower product into new markets so that companies that are its customers now “can spend globally and operate locally” in countries such as Brazil, Canada, Israel, Japan, Mexico, Singapore, South Africa, and the Philippines, as well as in 36 European countries. In an interview with TechCrunch, Brex co-founder and co-CEO Henrique Dubugras said that the company believes the move “will really open up TAM” for Brex since so many existing and prospective U.S. clients “have some sort of global operations.”\\n\\n“One of the big problems that companies have when they operate globally is that they actually need to open up an account in all these different countries where they might have employees. It becomes really complicated to set up all your financial systems on a country by country basis,” he added. “Now, if you use Brex, you can actually operate as if you were a local company with a local card.”\\n\\nIn other words, companies using Brex that have employees who work in other countries are giving those workers the ability to use a corporate card freely in their home countries, while also giving the company the ability to pay the statements in local currency from the local bank.\\n\\n“It’s something that we’ve been trying to do for a while,” Dubugras added, noting that insurtech Lemonade is a customer. — Mary Ann\\n\\nOther weekly news\\n\\nChristine, Mary Ann and Natasha Mascarenhas teamed up to write about the collapse of First Republic Bank, speaking with tech founders and investors who had money in the bank about what happens next. We also spoke with an FRB competitor about what all of these startup bank collapses mean for business. More here.\\n\\nReports Carly Page: “Hackers have published a trove of sensitive data stolen from payment software company AvidXchange after the company fell victim to ransomware for the second time this year. AvidXchange provides cloud-based software that helps organizations automate invoice processing and payment management processes. A ransomware group called RansomHouse claimed responsibility for the recent cyberattack on AvidXchange.” More here.\\n\\nChristine wrote about the launch of former Bolt CEO Ryan Breslow’s new company, Love, which is a wellness marketplace that features an initial 200 curated products, like supplements, health testing kits and essential oils, among such categories as reducing stress and gut health. All of the products on the site pass a set of compliance processes and reviews developed in partnership with clinical trials company Radicle Science, which Breslow said is unique to the company. More here.\\n\\nBritish neobank Revolut launched in Brazil, its first country in Latin America, offering customers a global bank account and crypto investments, Silicon Republic reported. The company already had a presence in the country after hiring Glauber Mota as the CEO of its Brazil business in March 2022. Alex Wilhelm and Anna Heim reported in April that Revolut “saw its valuation decline by some 46% in the eyes of one of its backers.” More here.\\n\\nTage Kene-Okafor reported on Fingo, a YC-backed Kenyan fintech, which launched a neobank — the first of its kind in the East African country, according to the company — in collaboration with Pan-African financial institution Ecobank Kenya. “It’s taken a while for Fingo to get here since CEO Kiiru Muhoya and his co-founders James da Costa, Ian Njuguna and Gitari Tirima founded the Kenyan outfit in January 2021 to provide financial services that appeal to a fast-growing African youthful population that happens to be the youngest globally but the most financially marginalized. After a $200,000 pre-seed round, Fingo got into YC S21 and raised $4 million in seed funding toward the end of that year.” More here.\\n\\nManish Singh reported that Paytm, India’s leading mobile payments firm, reported a 13.2% surge in revenue to $285.7 million in the quarter ending March and pared its loss by 57% to $20.5 million “in a sharp turnaround for the company that is increasingly trying to become profitable following a tremulous year and a half after its public debut.” More here.\\n\\nMore headlines\\n\\nApple and fintechs like Robinhood chase yield-hungry depositors as Fed rate hikes continue. Similarly, Arta Finance, a company providing access to alternative assets, debuted the Harvest Treasuries AI-Managed Portfolio, which offers a 4.62% APY (annual percentage yield), and Wealthfront’s cash account now offers 4.55% for all clients and 5.05% APY for clients who refer a friend.\\n\\nFintech projected to become a $1.5 trillion industry by 2030, according to a new report from Boston Consulting Group and QED Investors\\n\\nOpendoor tech earnings beat by $0.77, revenue topped estimates\\n\\nEveree joins Visa’s Fintech Fast Track Program with launch of Everee Visa® pay card\\n\\nFunding and M&A\\n\\nSeen on TechCrunch\\n\\nAfrican payment service provider Nomba raises $30M, backed by Base10 Partners and Shopify\\n\\nBend is taking on Brex and Ramp with a green twist and a $2.5M seed round\\n\\nAnd elsewhere\\n\\nDigital wallet for insurance Marble bags $4.2M. Speaking about the raise to TechCrunch, CEO Stuart Winchester said via email, “American households are under a lot of financial strain right now, and insurance expenses are no small part of that. We will continue to put out features that make it easier to not only save money and maximize value, but also to reduce the mental load of managing multiple insurance policies. We expect to see the insurance industry in general adopt more of the consumer friendly features that we’ve helped pioneer.”\\n\\nInsurtech startup Novidea raises $50 million Series C\\n\\nExclusive: Former Venmo COO raises $20M for Vera Equity\\n\\nTarabut Gateway raises $32 million to expand Saudi open banking\\n\\nMusic financing startup Duetti raises $32 million to buy old songs\\n\\nBilling platform Inbox Health raises $22.5M and more digital health fundings\\n\\nGoogle’s VC firm just led a $12 million Series A investment in Range, a startup that’s training AI to give financial advice\\n\\nOpenEnvoy raises $15 million to grow AP automation solution\\n\\nMiami-based startup Kiddie Kredit raises $1.4M with support from Dwyane Wade and Baron Davis\\n\\nBlack-owned tech firm Greenwood acquires digital banking rival. TechCrunch covered Greenwood’s last raise in March of 2021 here.\\n\\nJoin us at TechCrunch Disrupt 2023 in San Francisco this September as we explore the impact of fintech on our world today. New this year, we will have a whole day dedicated to all things fintech featuring some of today’s leading fintech figures. Save up to $800 when you buy your pass now through May 15, and save 15% on top of that with promo code INTERCHANGE. Learn more.\\n\\nWe are done for this week and it’s a good thing because we are also TIRED! See you next week — same time, same place. Until then, take good care! xoxo, Mary Ann and Christine', metadata={'source': '/content/new_articles/05-07-fintech-space-continues-to-be-competitive-and-drama-filled.txt'}),\n", " Document(page_content='SpaceX’s super-heavy launch system Starship is poised to fundamentally reshape the space economy. The 394-foot-tall vehicle, which took to the skies for the first time last month, is designed to carry a staggering amount of mass to low Earth orbit and into deep space.\\n\\nTechCrunch+ spoke with three pure-play space VCs — Space Capital founder and managing partner <PERSON>, Space.VC founder and general partner <PERSON> and E2MC Ventures founder <PERSON> — to learn more about how they advise founders to think through Starship’s super-heavy implications.\\n\\nWhile the trio diverges on many fine points, they all agreed that founders should be thinking now about how Starship could affect their operations, for better or worse.\\n\\n“Starship has such high importance to the space sector that probably almost everyone who has a space company has to war game what that means for their business,” <PERSON><PERSON><PERSON> said.\\n\\nChanging the face of launch …\\n\\nThe most obvious way in which Starship is likely to revolutionize the industry is by continuing the trend SpaceX firmly established with the debut of Falcon 9: further lowering the cost of launching mass to space. Starship will be capable of carrying 100 to 150 tons of stuff to orbit, a paradigm-shifting quantity that far outstrips the payload capacity of any rocket that humans have ever designed.', metadata={'source': '/content/new_articles/05-07-spacex-starship-startups-future.txt'}),\n", " Document(page_content='In the must-watch final season of “Succession,” <PERSON> enters a conference room with his siblings. As the scene opens, he takes a seat and declares: “Who will be the successor? Me.”\\n\\nOf course, that scene didn’t appear on HBO’s hit show, but it’s a good illustration of generative AI’s level of sophistication compared to the real thing. Yet as the Writers Guild of America goes on strike in pursuit of livable working conditions and better streaming residuals, the networks won’t budge on writers’ demands to regulate the use of AI in writers’ rooms.\\n\\n“Our proposal is that we not be required to adapt something that’s output by AI, and that the output of an AI not be considered writers’ work,” comedy writer <PERSON> told TechCrunch. “That doesn’t entirely exclude that technology from the production process, but it does mean that our working conditions wouldn’t be undermined by AI.”\\n\\nBut the Alliance of Motion Picture and Television Producers (AMPTP) refused to engage with that proposal, instead offering a yearly meeting to discuss “advances in technology.”\\n\\n“When we first put [the proposal] in, we thought we were covering our bases — you know, some of our members are worried about this, the area is moving quickly, we should get ahead of it,” <PERSON><PERSON> said. “We didn’t think it’d be a contentious issue because the fact of the matter is, the current state of the text-generation technology is completely incapable of writing any work that could be used in a production.”\\n\\nThe text-generating algorithms behind tools like ChatGPT are not built to entertain us. Instead, they analyze patterns in massive datasets to respond to requests by determining what is most likely the desired output. So, ChatGPT knows that “Succession” is about an aging media magnate’s children fighting for control of his company, but it is unlikely to come up with any dialogue more nuanced than, “Who will be the successor? Me.”\\n\\nAccording to Ben Zhao, a University of Chicago professor and faculty lead of art anti-mimicry tool Glaze, AI advancements can be used as an excuse for corporations to devalue human labor.\\n\\n“It’s to the advantage of the studios and bigger corporations to basically over-claim ChatGPT’s abilities, so they can, in negotiations at least, undermine and minimize the role of human creatives,” Zhao told TechCrunch. “I’m not sure how many people at these larger companies actually believe what they’re saying.”\\n\\nConover emphasized that some parts of a writer’s job are less obvious than literal scriptwriting but equally difficult to replicate with AI.\\n\\n“It’s going and meeting with the set decoration department that says, ‘Hey, we can’t actually build this prop that you’re envisioning, could you do this instead?’ and then you talk to them and go back and rewrite,” he said. “This is a human enterprise that involves working with other people, and that simply cannot be done by an AI.”\\n\\nComedian Yedoye Travis sees how AI could be useful in a writers’ room.\\n\\n“What we do in writers’ rooms is ultimately bouncing ideas around,” he told TechCrunch. “Even if it’s not good per se, an AI can throw together a script in however many minutes, compared to a week for human writers, and then it’s easier to edit than to write.”\\n\\nBut even if there may be some promise for how humans can leverage this technology, he worries that studios see it merely as a way to demand more from writers over a shorter period of time.\\n\\n“It says to me that they’re only concerned with things being made,” Travis said. “They’re not concerned with people being paid for things being made.”\\n\\nWriters are also advocating to regulate the use of AI in entertainment because it remains a legal grey area.\\n\\n“It’s not clear that the work that it outputs is copyrightable, and a movie studio is not going to spend $50 to $100 million shooting a script that they don’t know that they own the copyright to,” Conover said. “So we figured this would be an easy give for [the AMPTP], but they completely stonewalled on it.”\\n\\nAs the Writers Guild of America strikes for the first time since its historic 100-day action in 2007, Conover said he thinks the debate over AI technology is a “red herring.” With generative AI in such a rudimentary stage, writers are more immediately concerned with dismal streaming residuals and understaffed writing teams. Yet studios’ pushback on the union’s AI-related requests only further reinforces the core issue: The people who power Hollywood aren’t being paid their fair share.\\n\\n“I’m not worried about the technology,” Conover said. “I’m worried about the companies using technology, that is not in fact very good, to undermine our working conditions.”', metadata={'source': '/content/new_articles/05-03-ai-replace-tv-writers-strike.txt'}),\n", " Document(page_content='After Google cut all but three of the projects at its in-house incubator Area 120 and shifted it to work on AI projects across Google, one of the legacy efforts — coincidentally also an AI project — is now officially exiting to Google. Checks, an AI-powered tool to check mobile apps for compliance with various privacy rules and regulations, is moving into Google proper as a privacy product aimed at mobile developers.\\n\\nChecks originally made its debut in February 2022, although it was in development for some time before that. In its time at Area 120, it became one of the largest projects in the group, co-founders <PERSON> and <PERSON><PERSON> told me, with 10 people fully dedicated to it and a number of others contributing less formally. The founders’ job titles under Google will now be GM and Legal Lead, respectively, for Checks.\\n\\nThe amount that Google invested in the project was never disclosed, nor was the valuation of the exit to the parent company from the incubator, but the company has confirmed that there was a valuation and that it had grown since launch.\\n\\nThe company is not disclosing how many customers it has in total but notes that they are in the sectors of gaming, health, finance, education and retail. A sampling includes Miniclip, Rovio, Kongregate, Crayola and Yousician and in total the number of customers represented by its customers is over 3 billion.\\n\\nChecks will sit in the Developer X division. “What <PERSON>, <PERSON><PERSON>, and the entire Google Checks team have accomplished is one of the hardest things to do. Their focus on customer needs and nimble execution has served them well, and we’re eager to push ahead in this next phase of Checks,” said Jeanine Banks in a statement.\\n\\nChecks is one of those ideas that feels incredibly timely in that it speaks to an issue that’s growing in importance for consumers — who will vote with their feet when they feel that their privacy is in jeopardy. That in turn also puts more pressure on developers to get things right on the privacy front. App publishers these days are faced with a growing array of rules and regulations around data protection and privacy, not just rules like GDPR in Europe and CCPA in California (and the U.S.) set across different countries and jurisdictions, but also by companies that operate platforms within their own compliance efforts.\\n\\nWhen translated into how those regulations impact apps, there are potential issues at the front end, as well as on the back end, with how apps are coded and information moves from one place to another to consider. It’s a spaghetti bowl of issues, with fixes in one area potentially impacting another and making user experience less smooth to boot.\\n\\nChecks leans on artificial intelligence and machine learning to scan apps and their code to identify areas where there might be violations of privacy and data protection rules, and provides remediation to suggest how to fix it — tasks that would be far more difficult for a team of humans to execute on their own. It’s already integrated with Google’s large language models and what it describes as “app understanding technologies” to power what it identifies and make suggestions for fixing issues.\\n\\nA dashboard lets users monitor and triage issues in the areas of compliance monitoring, data monitoring and store disclosure support (which is focused specifically on Google Play data safety). With the service also aimed at iOS developers, it’s not clear if it will add Apple App Store data safety at any point into that mix. All of this can be monitored in real time on live apps, as well as when they are still in development.\\n\\nWe have reached out to Google to get an update on the status of the other two projects that were spared all-out closure after Area 120 changed focus. They include video dubbing solution Aloud and an as-yet unnamed consumer product from the team that had previously built a bookmarking app Liist (which got acquired by Google).\\n\\nAs of right now, Liist’s co-founder David Friedl still describes himself on LinkedIn as working on a stealth product at Area 120, and Aloud is still using an Area 120 URL, so it seems that they remain in a holding pattern. (We’ll update this if and when we hear more.)\\n\\nIn the meantime, Area 120 itself is also seeing some revolving doors. Clay Bavor, who was running Area 120 among other things and who messaged the big changes to staff in January, was out the door just a month later. He has now teamed up with Bret Taylor — another ex-Googler who has an outsized track record that includes being the CTO of Facebook and the co-CEO of Salesforce — to work on a mystery startup.\\n\\nUpdated with more information about Checks’ valuation and quote from Google.', metadata={'source': '/content/new_articles/05-03-checks-the-ai-powered-data-protection-project-incubated-in-area-120-officially-exits-to-google.txt'}),\n", " Document(page_content='Slack has evolved from a pure communications platform to one that enables companies to link directly to enterprise applications without having to resort to dreaded task switching. Today, at the Salesforce World Tour event in NYC, the company announced the next step in its platform’s evolution where it will be putting AI at the forefront of the user experience, making it easier to get information and build workflows.\\n\\nIt’s important to note that these are announcements, and many of these features are not available yet.\\n\\nRob <PERSON>man says that rather than slapping on an AI cover, they are working to incorporate it in a variety of ways across the platform. That started last month with a small step, a partnership with OpenAI to bring a ChatGPT app into Slack, the first piece of a much broader vision for AI on the platform. That part is in beta at the moment.\\n\\nToday’s announcement involves several new integrations, including SlackGPT, the company’s own flavor of generative AI built on top of the Slack platform, which users and developers can tap into to build AI-driven experiences. The content in Slack provides a starting point for building models related to the platform.\\n\\n“We think Slack has a unique advantage when it comes to generative AI. A lot of the institutional knowledge on every topic, team, work item and project is already in Slack through the messages, the files and the clips that are shared every day,” he said.\\n\\nWhen you combine that with Slack’s Partner ecosystem and platform, customers have a lot of options for integrating AI into their workflows. He says that <PERSON><PERSON><PERSON> is thinking about this in three ways right now.\\n\\n“For starters, Slack is going to bring AI natively into the user experience with SlackGPT to help customers work faster, communicate better, learn faster, etc. And an example of that is AI-powered conversation summaries and writing assistance for composition that’s going to be directly available in Slack,” he said.\\n\\nThe former could as an example help employees get caught up on a long thread without having to read every message to get the gist of what was being discussed. The latter could help generate Slack messages or content for linked Slack applications. That’s a little less obvious use case. It’s probably easier to write a Slack message yourself unless it’s an automated message that’s part of a workflow, but if you are creating content for Slack Canvas, you could let the generative AI help you.\\n\\nDevelopers can get in on the action too, building AI steps into workflows, giving them the option of tapping into external apps and large language models to build generative AI experiences themselves. Just last week the company made its updated developer experience generally available, and this should make it easier to incorporate generative AI into the platform in customized ways, Seaman says.\\n\\n“So this gives us the foundation to give users choice and flexibility to bring AI into their work in their business whenever they’re ready, and however they like. We’ve got 2,600 apps in the ecosystem right now, which includes a lot of the leading LLMs, and we see a lot of customers already choosing to integrate generative AI into Slack themselves,” he said.\\n\\nFinally EinsteinGPT, the Salesforce flavor of generative AI announced in March, will also be incorporated into Slack, letting employees ask questions directly about Salesforce content, like the users most likely to churn or the accounts most likely to buy, and so forth. This is really about more directly integrating Salesforce content into Slack, the company Salesforce paid $27 billion for a couple of years ago.\\n\\n“Slack is really becoming the conversational interface for Salesforce. So that’s bringing those EinsteinGPT-powered insights from the real-time customer data that exists in Salesforce into Slack to enrich every team’s understanding of the customer,” he said.\\n\\nAs with most of the generative AI tooling we’ve seen being added to enterprise software, Slack is announcing these capabilities long before they release them, but this should give customers a sense of what’s coming, and how AI could be transforming Slack in the future. SlackGPT and EinsteinGPT integration are still in the development phase, but developers can build custom integrations with a variety of LLMs, today. Workflow Builder with SlackGPT AI connectors (which will allow customers to instantly connect ChatGPT or Claude to a workflow or build custom connectors that plug in their own LLMs) will be available this summer.', metadata={'source': '/content/new_articles/05-04-slack-updates-aim-to-put-ai-at-the-center-of-the-user-experience.txt'})]"]}, "metadata": {}, "execution_count": 10}]}, {"cell_type": "code", "source": ["from langchain.text_splitter import RecursiveCharacterTextSplitter"], "metadata": {"id": "zzJh-irguoxF"}, "execution_count": 11, "outputs": []}, {"cell_type": "code", "source": ["text_splitter = RecursiveCharacterTextSplitter(chunk_size = 1000, chunk_overlap = 200)\n", "text = text_splitter.split_documents(document)"], "metadata": {"id": "ON8O7cw4w-0e"}, "execution_count": 12, "outputs": []}, {"cell_type": "code", "source": ["text"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jx6nzQuaxZk-", "outputId": "67a2fa8c-47cc-4279-9fe7-1d63c10ddbc3"}, "execution_count": 13, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='Google I/O 2023 is next week; here’s what we’re expecting A whole bunch of new hardware, coupled with a lot of AI and the best look yet at Android 14\\n\\nGoogle’s annual developer conference, Google I/O, returns to Mountain View’s Shoreline Amphitheater next week, and for the first time in four years, we’ll be returning along with it. The kickoff keynote is always jammed-packed full of information, debuting all of the different software projects the company has been working on for the past year.\\n\\nUpdate: Google just went ahead and announced the Pixel Fold over on Twitter. The company gave a good look at the upcoming foldable smartphone from just about every angle. That means all three of the expected pieces of hardware – including the Pixel 7a and Pixel Tablet – have officially been announced.', metadata={'source': '/content/new_articles/05-05-google-i-o-2023-is-next-week-heres-what-were-expecting.txt'}),\n", " Document(page_content='The event, which kicks off May 10 at 10 AM PT will be a big showcase for everything that’s on the way for Android 14. The company has, arguably, missed a step when it comes to the current generative AI land rush — hell, who could have predicted after all of these years that <PERSON> would finally have a moment?\\n\\nCEO Sundar Pichai will no doubt be making the case that the company continues to lead the way in the world of artificial intelligence. There’s always been a fair bit of the stuff at the event largely focused on practical real-world applications like mobile imaging and dealing with customer service. This year, however, I’d say it’s safe to say the company is going to go bonkers with the stuff.', metadata={'source': '/content/new_articles/05-05-google-i-o-2023-is-next-week-heres-what-were-expecting.txt'}),\n", " Document(page_content='Hardware, meanwhile, is always a bit of a crapshoot at developer conferences. But after an off-year for the industry at large, a deluge of rumors are aligning, pointing to what’s likely to be an unusually consumer electronics-focused keynote. Given the fact that the last bit is my focus at TechCrunch, I’m going to start the list there.\\n\\nThe Pixel 7a is about as sure as bets get. Google has settled into a comfortable release cadence: releasing a flagship in the fall, followed by a budget device in the spring. The former is designed to be an ideal showcase for its latest mobile operating system and first-party silicon, while the latter makes some compromises for price, while maintaining as many of its predecessors as possible.\\n\\nHow to show excitement without shouting? Asking for a friend Coming to @Flipkart on 11th May. pic.twitter.com/il6GUx3MmR — Google India (@GoogleIndia) May 2, 2023', metadata={'source': '/content/new_articles/05-05-google-i-o-2023-is-next-week-heres-what-were-expecting.txt'}),\n", " Document(page_content='How to show excitement without shouting? Asking for a friend Coming to @Flipkart on 11th May. pic.twitter.com/il6GUx3MmR — Google India (@GoogleIndia) May 2, 2023\\n\\nIt’s a good system that works, and Google’s newly focused mobile hardware team has created some surprisingly good devices at extremely reasonable prices. Never one to be outdone by the deluge of rumors, the company went ahead and announced via Twitter its next device is due out on May 11 — the day after Google I/O and, perhaps not coincidentally, my birthday. It was Google India that specifically made the announcement — perhaps not surprising, as the company is likely to aggressively target the world’s number one smartphone market with the product. The image points to a very similar design as the 7 — not really a surprise as these things go. Though it does stop short of actually mentioning the name, as it’s done in the past.', metadata={'source': '/content/new_articles/05-05-google-i-o-2023-is-next-week-heres-what-were-expecting.txt'}),\n", " Document(page_content='Basically expect the 7 with cheaper materials. Rumors point to a 6.1-inch device featuring a 90Hz refresh rate, coupled with a 64-megapixel rear camera. The 7’s Tensor G2 returns for a command performance, likely bringing with it many of the software features it enabled the first time around.', metadata={'source': '/content/new_articles/05-05-google-i-o-2023-is-next-week-heres-what-were-expecting.txt'}),\n", " Document(page_content='We know for sure that a Pixel Tablet is coming…at some point. Google confirmed the device’s existence at last year’s event, providing a broad 2023 release date, along with a render alongside the rest of the current Pixel lineup. Effectively there are two points this year Google is likely to officially announce the thing: next week or September/October. I would be shocked if the company’s long-awaited (?) reentry into the category doesn’t, at the very least, get a bit of stage time. As a category, the Android tablet has been very hit or miss over the years — presumably/hopefully the company’s got a unique spin here. I would be surprised if Google jumped back into the space without some sort of novel angle.', metadata={'source': '/content/new_articles/05-05-google-i-o-2023-is-next-week-heres-what-were-expecting.txt'}),\n", " Document(page_content='The leaks point to a design that would effectively turn the system into one giant Nest dock. It’s not entirely original, as Amazon tried something similar with its Fire tablets, but it would certainly buck the iPad model, which is so pervasive in the industry. Other rumors include the aforementioned Tensor G2, coupled with 8GB of RAM.\\n\\nHere’s your wildcard, folks: the Pixel Fold. Google has seemingly been laying the groundwork for its own foldable for years. Here’s what I wrote a couple of weeks ago:', metadata={'source': '/content/new_articles/05-05-google-i-o-2023-is-next-week-heres-what-were-expecting.txt'}),\n", " Document(page_content='Some important background here. First, Google announced foldable screen support for Android back in 2018. Obviously, Samsung was both the big partner and recipient in those days, and Google wanted to make Android development as frictionless as possible for other OEMs in exploring the form factor. The following year, Google foldable patents surfaced. Now, we’re all adults here, who implicitly understand that patents don’t mean a company is working on a product. That said, it’s another key data point in this story. In the intervening years, foldables have begun gathering steam, even outside of the Samsung orbit. I was genuinely amazed by how many different models there were populating the halls of MWC back in March. The leaked renders point to a form factor that is more Samsung Galaxy Z Fold than Samsung Galaxy Z Flip. It also looks like it shares some common design DNA with Oppo’s recently foldable, which is frankly the right direction. EV Leaks says the foldable is half an inch thick', metadata={'source': '/content/new_articles/05-05-google-i-o-2023-is-next-week-heres-what-were-expecting.txt'}),\n", " Document(page_content='than Samsung Galaxy Z Flip. It also looks like it shares some common design DNA with Oppo’s recently foldable, which is frankly the right direction. EV Leaks says the foldable is half an inch thick when folded and 0.2 inches unfolded, weight in at 283 grams.', metadata={'source': '/content/new_articles/05-05-google-i-o-2023-is-next-week-heres-what-were-expecting.txt'}),\n", " Document(page_content='As evidenced by our trip to MWC back in February, foldables are no longer fringe devices. It’s true that they’re still cost-prohibitive for most, but it’s getting to the point soon where nearly ever Android manufacturer will have their take on the category. So why shouldn’t Google?\\n\\nOther less likely hardware rumors include a Google/Nest AirTag competitor (the company announced yesterday that it’s working with Apple to create a standard for the category), new Pixel Buds and a Pixel Watch 2. I’d say all are unlikely — that last one in particular. We didn’t get much in terms of Nest products last year, but so far not much is forthcoming in terms of rumors for home products.', metadata={'source': '/content/new_articles/05-05-google-i-o-2023-is-next-week-heres-what-were-expecting.txt'}),\n", " Document(page_content='Android is always a tentpole of Google I/O for obvious reasons. We’ve already caught some major glimpses of the mobile operating system, by way of beta releases. <PERSON> <PERSON> noted in March, “So far, most of the features Google has talked about have also been developer-centric, with only a few user-facing features exposed to far. That also holds true for this second preview, which mostly focuses on added new security and privacy features.”\\n\\nThe operating system, which is apparently named Upside Down Cake internally, is likely set for a summer release in late-July or August. At the top of the list of potential features are a boost to battery life (can always use one of those), additional accessibility features and privacy/security features, which include blocking users from installing ancient apps over malware concerns.', metadata={'source': '/content/new_articles/05-05-google-i-o-2023-is-next-week-heres-what-were-expecting.txt'}),\n", " Document(page_content='AI is going to be everywhere. Expect generative AI (Bard) in particular to make appearances in virtually every existing piece of Google consumer software, following the lead of Gmail and Docs. Search and the Chrome browser are prime targets here.\\n\\nA preview of a new Wear OS seems likely. I don’t anticipate a ton of news on the AR/VR side of things, but I would also be surprised if it doesn’t at least get a nod, given what Apple reportedly has in the works for June.\\n\\nThe keynote kicks off at 10 AM PT on May 10. As ever, TechCrunch will be bringing you the news as it breaks.', metadata={'source': '/content/new_articles/05-05-google-i-o-2023-is-next-week-heres-what-were-expecting.txt'}),\n", " Document(page_content='Signaling that investments in the supply chain sector remain robust, Pando, a startup developing fulfillment management technologies, today announced that it raised $30 million in a Series B round, bringing its total raised to $45 million.\\n\\nIron Pillar and Uncorrelated Ventures led the round, with participation from existing investors Nexus Venture Partners, Chiratae Ventures and Next47. CEO and founder <PERSON><PERSON> says that the new capital will be put toward expanding Pando’s global sales, marketing and delivery capabilities.\\n\\n“We will not expand into new industries or adjacent product areas,” he told TechCrunch in an email interview. “Great talent is the foundation of the business — we will continue to augment our teams at all levels of the organization. Pando is also open to exploring strategic partnerships and acquisitions with this round of funding.”', metadata={'source': '/content/new_articles/05-03-ai-powered-supply-chain-startup-pando-lands-30m-investment.txt'}),\n", " Document(page_content='Pando was co-launched by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>, who previously worked together at iDelivery, an India-based freight tech marketplace — and their first startup. The two saw firsthand manufacturers, distributors and retailers were struggling with legacy tech and point solutions to understand, optimize and manage their global logistics operations — or at least, that’s the story <PERSON><PERSON><PERSON><PERSON><PERSON> tells.\\n\\n“Supply chain leaders were trying to build their own tech and throwing people at the problem,” he said. “This caught our attention — we spent months talking to and building for enterprise users at warehouses, factories, freight yards and ports and eventually, in 2018, decided to start Pando to solve for global logistics through a software-as-a-service platform offering.”', metadata={'source': '/content/new_articles/05-03-ai-powered-supply-chain-startup-pando-lands-30m-investment.txt'}),\n", " Document(page_content='There’s truth to what <PERSON><PERSON><PERSON><PERSON><PERSON>’s expressing about pent-up demand. According to a recent McKinsey survey, supply chain companies had — and have — a strong desire for tools that deliver greater supply chain visibility. Sixty-seven percent of respondents to the survey say that they’ve implemented dashboards for this purpose, while over half say that they’re investing in supply chain visibility services more broadly.\\n\\nPando aims to meet the need by consolidating supply chain data that resides in multiple silos within and outside of the enterprise, including data on customers, suppliers, logistics service providers, facilities and product SKUs. The platform provides various tools and apps for accomplishing different tasks across freight procurement, trade and transport management, freight audit and payment and document management, as well as dispatch planning and analytics.', metadata={'source': '/content/new_articles/05-03-ai-powered-supply-chain-startup-pando-lands-30m-investment.txt'}),\n", " Document(page_content='Customers can customize the tools and apps or build their own using Pando’s APIs. This, along with the platform’s emphasis on no-code capabilities, differentiates Pando from incumbents like SAP, Oracle, Blue Yonder and E2Open, <PERSON><PERSON><PERSON><PERSON><PERSON> asserts.\\n\\n“Pando comes pre-integrated with leading enterprise resource planning (ERPs) systems and has ready APIs and a professional services team to integrate with any new ERPs and enterprise systems,” he added. “Pando’s no-code capabilities enable business users to customize the apps while maintaining platform integrity — reducing the need for IT resources for each customization.”', metadata={'source': '/content/new_articles/05-03-ai-powered-supply-chain-startup-pando-lands-30m-investment.txt'}),\n", " Document(page_content='Pando also taps algorithms and forms of machine learning to make predictions around supply chain events. For example, the platform attempts to match customer orders with suppliers, customers through the “right” channel (in terms of aspects like cost and carbon footprint) and fulfillment strategy (e.g. mode of freight, carrier, etc.). Beyond this, Pando can detect anomalies among deliveries, orders and freight invoices and anticipate supply chain risk given demand and supply trends.\\n\\nPando isn’t the only vendor doing this. Altana, which bagged $100 million in venture capital last October, uses an AI system to connect to and learn from logistics and business-to-business data — creating a shared view of supply chain networks. Everstream, another Pando rival, offers its own dashboards for data analysis, integrated with existing ERP, transportation management and supplier relationship management systems.', metadata={'source': '/content/new_articles/05-03-ai-powered-supply-chain-startup-pando-lands-30m-investment.txt'}),\n", " Document(page_content='But Pando has a compelling sales pitch, judging by its momentum. The company counts Fortune 500 manufacturers and retailers — including P&G, J&J, Valvoline, Castrol, Cummins, Siemens, Danaher and Accuride — among its customer base. Since the startup’s Series A in 2020, revenue has grown 8x while the number of customers has increased 5x, <PERSON><PERSON><PERSON><PERSON><PERSON> said.\\n\\nAsked whether he expects expansion to continue well into the future, given the signs of potential trouble on the horizon, <PERSON><PERSON><PERSON><PERSON><PERSON> seemed fairly optimistic. He pointed to a Deloitte survey that found that more than 70% of manufacturing companies have been impacted by supply chain disruptions in the past year, with 90% of those companies experiencing increased costs and declining productivity.', metadata={'source': '/content/new_articles/05-03-ai-powered-supply-chain-startup-pando-lands-30m-investment.txt'}),\n", " Document(page_content='The result of those major disruptions? The digital logistics market is estimated to climb to $46.5 billion by 2025, per Markets and Markets — up from $17.4 billion in 2019. Crunchbase reports that investors poured more than $7 billion in seed through growth-stage rounds globally for supply chain-focused startups from January to October 2022, nearly eclipsing 2021’s record-setting levels.\\n\\n“Pando has a strong balance sheet and profit and loss statement, with an eye on profitable growth,<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> said. “We’re are scaling operations in North America, Europe and India with marquee customer wins and a network of strong partners … Pando is well-positioned to ride this growth wave, and drive supply chain agility for the 2030 economy.”', metadata={'source': '/content/new_articles/05-03-ai-powered-supply-chain-startup-pando-lands-30m-investment.txt'}),\n", " Document(page_content='As brands incorporate generative AI into their creative workflows to generate new content associated with the company, they need to tread carefully to be sure that the new material adheres to the company’s style and brand guidelines.\\n\\nNova is an early-stage startup building a suite of generative AI tools designed to protect brand integrity, and today, the company is announcing two new products to help brands police AI-generated content: BrandGuard and BrandGPT.\\n\\nWith BrandGuard, you ingest your company’s brand guidelines and style guide, and with a series of models Nova has created, it can check the content against those rules to make sure it’s in compliance, while BrandGPT lets you ask questions about the brand’s content rules in ChatGPT style.', metadata={'source': '/content/new_articles/05-03-nova-is-building-guardrails-for-generative-ai-content-to-protect-brand-integrity.txt'}),\n", " Document(page_content='<PERSON>, founder and CEO at the company, who previously founded Backupify, a cloud backup startup that was acquired by <PERSON><PERSON> back in 2014, recognized that companies wanted to start taking advantage of generative AI technology to create content faster, but they still worried about maintaining brand integrity, so he came up with the idea of building a guard rail system to protect the brand from generative AI mishaps.\\n\\n“We heard from multiple CMOs who were worried about ‘how do I know this AI-generated content is on brand?’ So we built this architecture that we’re launching called BrandGuard, which is a really interesting series of models, along with BrandGPT, which acts as an interface on top of the models,” <PERSON> told TechCrunch.', metadata={'source': '/content/new_articles/05-03-nova-is-building-guardrails-for-generative-ai-content-to-protect-brand-integrity.txt'}),\n", " Document(page_content='BrandGuard is like the back end for this brand protection system. Nova built five models that look for things that might seem out of whack. They run checks for brand safety, quality checking, whether it’s on brand, whether it adheres to style and whether it’s on campaign. Then it assigns each piece with a content score, and each company can decide what the threshold is for calling in a human to check the content before publishing.\\n\\n“When you have generative AI creating stuff, you can now score it on a continuum. And then you can set thresholds, and if something’s below, say 85% on brand, you can have the system flag it so that a human can take a look at it,” he said. Companies can decide whatever threshold they’re comfortable with.', metadata={'source': '/content/new_articles/05-03-nova-is-building-guardrails-for-generative-ai-content-to-protect-brand-integrity.txt'}),\n", " Document(page_content='BrandGPT is designed for working with third parties like an agency or a contractor, who can ask questions about the company’s brand guidelines to make sure they are complying with them, <PERSON> said. “We’re launching BrandGPT, which is meant to be the interface to all this brand-related security stuff that we’re doing, and as people interact with brands, they can access the style guides and better understand the brand, whether they’re a part of the company or not.\\n\\nThese two products are available in public beta starting today. The company launched last year and has raised $2.4 million from Bee Partners, Fyrfly Ventures and Argon Ventures.', metadata={'source': '/content/new_articles/05-03-nova-is-building-guardrails-for-generative-ai-content-to-protect-brand-integrity.txt'}),\n", " Document(page_content='Welcome back to This Week in Apps, the weekly TechCrunch series that recaps the latest in mobile OS news, mobile applications and the overall app economy.\\n\\nThe app economy in 2023 hit a few snags, as consumer spending last year dropped for the first time by 2% to $167 billion, according to data.ai’s “State of Mobile” report. However, downloads are continuing to grow, up 11% year-over-year in 2022 to reach 255 billion. Consumers are also spending more time in mobile apps than ever before. On Android devices alone, hours spent in 2022 grew 9%, reaching 4.1 trillion.\\n\\nThis Week in Apps offers a way to keep up with this fast-moving industry in one place with the latest from the world of apps, including news, updates, startup fundings, mergers and acquisitions, and much more.\\n\\nDo you want This Week in Apps in your inbox every Saturday? Sign up here: techcrunch.com/newsletters\\n\\nTop Stories\\n\\nDorsey criticizes Twitter, Musk on the alternative social networks he’s backing', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='Do you want This Week in Apps in your inbox every Saturday? Sign up here: techcrunch.com/newsletters\\n\\nTop Stories\\n\\nDorsey criticizes Twitter, <PERSON><PERSON> on the alternative social networks he’s backing\\n\\nAs demand for <PERSON><PERSON>, the <PERSON>-backed decentralized Twitter rival grows, the former Twitter CEO took to the app to share his thoughts on Twitter’s future, <PERSON><PERSON>sk and the decision to take the company private. As TechCrunch’s <PERSON> reported, <PERSON><PERSON> responded to questions posed to him from other users and reporters on Bluesky, including one where he was asked if <PERSON><PERSON> has proven to be the best possible steward for the social network.\\n\\nDorsey said he had not:\\n\\nNo. Nor do I think he acted right after realizing his timing was bad. Nor do I think the board should have forced the sale. It all went south. But it happened and all we can do now is build something to avoid that ever happening again. So I’m happy <PERSON> and team and nostr devs exist and building it.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='However, the Twitter co-founder stressed that Twitter would have never survived as a public company and defended himself from an accusation that he was deflecting blame for Twitter’s current situation.\\n\\nThough <PERSON> is having a moment, particularly as a haven for marginalized groups, sex workers and trans users, it’s not the only Twitter alternative <PERSON><PERSON> is now backing. In fact, he’s been more active in recent days on the social network nostr (which he also financially backed), where he’s also been critical of some of <PERSON><PERSON>’s recent decisions. For example, as The NYT reported, <PERSON><PERSON> posted last month “This is weak,” in response to Musk’s move to stop Twitter users from linking to Substack after it launched a Twitter-like service for its own community of writers and readers.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='<PERSON><PERSON> also touted his belief in these platforms during <PERSON>’s recent earnings call, suggesting on his nostr profile this may be the first time the network’s name had been mentioned during a public earnings event.\\n\\n“Open protocols represent another fork in the road moment for people and companies,” <PERSON><PERSON> told investors. “Bitcoin, nostr, <PERSON><PERSON>, <PERSON>5 and others are all working to level the playing field for competition and give individuals and organizations entirely new capabilities,” he added.\\n\\nOver the past few weeks, <PERSON><PERSON> has been gaining traction, but the network has been difficult to access due to its invite-only nature. That’s turned Bluesky invites into hot commodities, where they’re even selling for hundreds of dollars on eBay, as most users have to wait to receive only one invite every two weeks.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='<PERSON><PERSON> leadership will also sometimes gift a user with a larger number of invites in order to have them invite members of a specific community. Developers who can demonstrate they’re building a Bluesky app may also request additional invites, we understand.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='The network has received outsized press coverage relative to its size — just 50,000+ users — possibly because of the heavy infusion of tech journalists on there and <PERSON><PERSON>’s name attached. But the reality is that <PERSON><PERSON>’s future remains uncertain. The company, for now, is able to build and grow thanks to the $13 million in initial funds it received from Twitter, where it was incubated under <PERSON><PERSON>’s leadership. It has since spun out into its own, independent company (a public benefit LLC). It’s unclear how Bluesky intends to maintain its operations in the long term, not to mention its freewheeling culture and accepting community. Networks can often be pleasant and welcoming when small, like Bluesky — or early Twitter, for that matter — but face challenges once they scale to millions of users.\\n\\nNewFronts round-up', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='NewFronts round-up\\n\\nThis week was IAB’s NewFronts, where digital media companies and social networks pitched their platforms to advertisers looking to reach online audiences. The event saw major brands introducing a range of new offerings, including both ad products and formats, as well as touting their latest features, in some cases, as Snap did with its My AI integration.\\n\\nHere’s what you may have missed from the app makers’ NewFronts this week:\\n\\nSnap said it’s beginning to test a feature that lets partners leverage its new My AI chatbot to place sponsored links in front of users. Snap also announced new ad slots, including the option to reserve the first video ad seen in Snapchat’s Friend Stories and the ability to advertise within its TikTok-like Spotlight feature.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='Snap also announced including the option to reserve the and the ability to YouTube introduced new ad opportunities for Shorts, including the expansion of Shorts into Video reach campaigns that leverage Google AI to serve the best combination of ads and improve reach on YouTube. Plus, YouTube Select is now coming to Shorts, allowing advertisers to place their ads alongside the most popular YouTube Shorts’ content, similar to TikTok Pulse. Another option, First Position on Shorts, will let advertisers be the first ad Shorts users see in their viewing session.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='including the that leverage Google AI to serve the best combination of ads and improve reach on YouTube. Plus, allowing advertisers to place their ads alongside the most popular YouTube Shorts’ content, similar to TikTok Pulse. Another option, will let advertisers be the first ad Shorts users see in their viewing session. TikTok announced partnerships with big-name publishers, including NBCU, Condé Nast, DotDash Meredith, BuzzFeed and others, in an effort to pull in more premium ad dollars. The new premium ad product, Pulse Premiere, would allow marketers, for the first time, to position their brand ads directly after TikTok’s publisher and media partners’ content in over a dozen categories, including lifestyle, sports, entertainment, education and more. Publisher partners would receive a rev share as a result.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='The would allow marketers, for the first time, to position their brand ads directly after TikTok’s publisher and media partners’ content in over a dozen categories, including lifestyle, sports, entertainment, education and more. Publisher partners would receive a rev share as a result. Meta announced AR would become available to Reels Ads and Facebook Stories. They had previously been available only to the Facebook Feed, Instagram Feed and Instagram Stories. It also announced features to make Reels Ads more interactive, including a t est of a larger “call to action” button with additional advertiser information on Facebook and Instagram Reels ads. Other updates included multi-destination product ads, the ability to pause a video ad to preview a link’s destination and support for Reels Ads campaigns with select third-party measurement firms .', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='They had previously been available only to the Facebook Feed, Instagram Feed and Instagram Stories. It also announced features to make Reels Ads more interactive, including a t with additional advertiser information on Facebook and Instagram Reels ads. Other updates included the ability to and support for . NBCU will let Peacock users shop products that appear in its content through “Must ShopTV,” which puts a QR code on the screen when a shoppable product appears.\\n\\nApple & Google team up on Bluetooth tracker safety', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='Apple & Google team up on Bluetooth tracker safety\\n\\nAfter numerous cases of Bluetooth trackers like Apple’s AirTag being used for stalking or other criminal apps, Apple and Google this week released a joint announcement saying they will work together to lead an industry-wide initiative to draft a specification that would alert users in the case of unwanted tracking from Bluetooth devices. The companies said they’re seeking input from other industry participants and advocacy groups in the matter, and noted that other tracker makers like Samsung, Tile, Chipolo, eufy Security and Pebblebee have also expressed interest in the draft.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='The companies submitted a proposed specification as an Internet-Draft via a standards development organization, the Internet Engineering Task Force (IETF). Other interested parties are now being invited to review and comment over the next three months. After this time, Apple and Google will offer feedback and will release a production implementation of the specification by year’s end that will be supported in future versions of iOS and Android, they said.\\n\\nThe spec would build on the AirTag protections Apple had already released but also, critically, would ensure that users would be able to combat unwanted tracking by offering tools across both iOS and Android platforms.\\n\\nGoogle’s participation could signal more than a desire to protect its users — it’s been rumored the company may also be developing an AirTag rival.\\n\\nPlatforms\\n\\nApple\\n\\nGoogle — I/O Preview', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='Google’s participation could signal more than a desire to protect its users — it’s been rumored the company may also be developing an AirTag rival.\\n\\nPlatforms\\n\\nApple\\n\\nGoogle — I/O Preview\\n\\nGoogle I/O kicks off next week and we already know at least one of the announcements — because Google leaked it. The company plans to introduce its first foldable smartphone with the Pixel Fold. The device shares Pixel’s familiar camera bar and features an interface that showcases Material UI design. We expect to learn more at the event.\\n\\nIn addition, Google I/O 2023 should bring a Pixel 7a , a budget device that could also help address Pixel demand in emerging markets, plus possibly a Pixel tablet, an AirTag rival, a Wear OS update, and a lot of new developer tools and features. We also expect to hear quite a bit about Google’s AI plans, with generative AI (like Bard) appearing across Google’s line of products.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content=', a budget device that could also help address Pixel demand in emerging markets, plus possibly a Pixel tablet, an AirTag rival, a Wear OS update, and a lot of new developer tools and features. We also expect to hear quite a bit about Google’s AI plans, with generative AI (like Bard) appearing across Google’s line of products. To get ready for I/O, even if you’re attending virtually, Google offered a new planning guide and a playlist of developer content to help attendees prepare.\\n\\nto help attendees prepare. Checks, Google’s AI-powered data protection project, exited to Google from its in-house incubator Area 120. The tool uses AI to check mobile apps for compliance with various privacy rules and regulations.\\n\\nApp Updates\\n\\nSocial', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='App Updates\\n\\nSocial\\n\\nSocial networking app IRL’s CEO <PERSON> stepped down following allegations he used bots to inflate the number of users IRL reported publicly and to its investors , The Information reported. A former employee had alleged he was fired after expressing concern over the use of bots. The SEC is now investigating if the company violated securities laws. IRL raised around $200 million from SoftBank Vision Fund, Founders Fund and others.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content=', The Information reported. A former employee had alleged he was fired after expressing concern over the use of bots. The SEC is now investigating if the company violated securities laws. IRL raised around $200 million from SoftBank Vision Fund, Founders Fund and others. After laying off 50% of staff, declining audio social network Clubhouse says it’s building “Clubhouse 2.0,” but hasn’t shared exactly what that plan may involve. Last year, the company began shifting its focus away from public audio to private rooms but it’s not clear there’s much demand for audio social networking in the post-pandemic market.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='but hasn’t shared exactly what that plan may involve. Last year, the company began shifting its focus away from public audio to private rooms but it’s not clear there’s much demand for audio social networking in the post-pandemic market. Once-hot viral app <PERSON><PERSON><PERSON> shuts down and returns remaining funds to investors. The app had let friends tag others to build out their social profiles of real moments, not polished images, but had been on the decline, with only a few thousand MAUs down from a height of 4 million MAUs previously.\\n\\nA Twitter bug saw users able to regain their blue Verification checks just by editing their bio. Shortly afterward, the Twitter desktop website began randomly logging out users. Later in the week, the mobile website was also down.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='Shortly afterward, the Twitter desktop website began randomly logging out users. Later in the week, the mobile website was also down. As <PERSON><PERSON> gains attention, rival decentralized social platform Mastodon announced a new, simpler onboarding experience that provides new users with an account on mastodon.social by default , instead of requiring them to pick a server. This doesn’t eliminate server choice, it simply means that joining another server requires a few extra clicks.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content=', instead of requiring them to pick a server. This doesn’t eliminate server choice, it simply means that joining another server requires a few extra clicks. Neighborhood social network Nextdoor added new features powered by generative AI, including an Assistant feature aimed at helping users write posts that are more likely to drive positive community engagement. The Assistant will offer writing suggestions that users can review and optionally adopt. The company says it will also use AI to better match content to users when providing recommendations.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='including an Assistant feature aimed at helping users write posts that are more likely to drive positive community engagement. The Assistant will offer writing suggestions that users can review and optionally adopt. The company says it will also use AI to better match content to users when providing recommendations. BeReal is testing another new feature in the U.K., “RealPeople,” that shows users a timeline of the “world’s most interesting people” — that is, athletes, artists, activists and other public figures. The company also recently began testing the option to post more often as usage has declined.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='and other public figures. The company also recently began testing the option to post more often as usage has declined. Meta introduced new discovery and personalization options for Facebook Reels. Users can now choose “Show More” or “Show Less” options to control what sort of Reels they want to see. Facebook will also explain why it’s showing you a Reel, like if a friend viewed it, and is adding Reels to the main navigation at the top of Facebook Watch.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='WordPress drops Twitter integration, says sharing to Instagram and Mastodon is coming instead. The Automattic-owned publishing platform said the Twitter connection on Jetpack and WordPress.com will cease to work, meaning users’ blog posts will no longer be auto-shared to Twitter as before. The company said Elon Musk’s decision to “dramatically change the terms and pricing” for Twitter’s API was to blame for this decision. The API now starts at $42,000/month for 50 million tweets. The move will likely hurt Twitter more than WordPress, as the latter powers over 40% of the global internet, including WordPress.com blogs.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='The Automattic-owned publishing platform said the Twitter connection on Jetpack and WordPress.com will cease to work, meaning users’ blog posts will no longer be auto-shared to Twitter as before. The company said Elon Musk’s decision to “dramatically change the terms and pricing” for Twitter’s API was to blame for this decision. The API now starts at $42,000/month for 50 million tweets. The move will likely hurt Twitter more than WordPress, as the latter powers over 40% of the global internet, including WordPress.com blogs. Mozilla announced it’s opening up its own Mastodon server — or “instance,” in Mastodon lingo — into private beta testing. The company had said last year it planned to create and begin testing a publicly accessible instance at mozilla.social. It explains its approach to Mastodon will involve high levels of moderation.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='The company had said last year it planned to create and begin testing a publicly accessible instance at mozilla.social. It explains its approach to Mastodon will involve high levels of moderation. Twitter announced it would make its API free for public service announcements after New York’s Metro Transit Service (MTS) abandoned the service and the National Weather Services (NWS) said it would no longer auto-post warnings.\\n\\nafter New York’s Metro Transit Service (MTS) abandoned the service and the National Weather Services (NWS) said it would no longer auto-post warnings. TikTok’s U.S. head of trust and safety <PERSON> is leaving the company on May 12 as lawmakers weigh a TikTok ban. <PERSON> had played a key role in TikTok’s strategy to avoid a U.S. ban.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='as lawmakers weigh a TikTok ban. Han had played a key role in TikTok’s strategy to avoid a U.S. ban. Discord is making all users change their usernames, the company announced this week. Originally, Discord users had been identified by a name and random number separated by a hash sign, but now it wants to adopt a simpler format so people can more easily share their usernames with others. The new plan will include a unique alphanumeric username with the @ symbol in front of it, plus a freely assignable display name that can be changed at any time.\\n\\nAI\\n\\nSlack introduced SlackGPT, its own generative AI built on Slack’s platform which developers can use to create AI-driven experiences.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='AI\\n\\nSlack introduced SlackGPT, its own generative AI built on Slack’s platform which developers can use to create AI-driven experiences.\\n\\nwhich developers can use to create AI-driven experiences. Microsoft launched its Bing chatbot to all users globally, meaning there’s no more waitlist to get started. It’s also adding more image- and graphic-centric answers in Bing Chat, including by creating graphs and charts and generating images from text prompts. It will also allow users to export their Bing Chat histories. And it will embrace multimodality, meaning it can understand queries with images and text combined. Bing now sees more than 100 million daily active users and says visitors have engaged in over half a billion chats.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='It’s also adding more image- and graphic-centric answers in Bing Chat, including by creating graphs and charts and generating images from text prompts. It will also allow users to export their Bing Chat histories. And it will embrace multimodality, meaning it can understand queries with images and text combined. Bing now sees more than 100 million daily active users and says visitors have engaged in over half a billion chats. Plexamp, the music player originally incubated by the Labs division of media company Plex, is tapping into ChatGPT with its latest update. The new feature called “Sonic Sage,” powered by OpenAI’s ChatGPT, will build unique music playlists by scanning users’ libraries and leveraging their TIDAL subscription.\\n\\nMedia & Entertainment\\n\\nFintech', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='Media & Entertainment\\n\\nFintech\\n\\nYC-backed Kenyan fintech Fingo launched its neobanking app, developed in collaboration with Pan-African financial institution Ecobank Kenya. The company raised $4 million in seed funding after its YC S21 participation. Fingo offers users a bank account, paired with free peer-to-peer transactions and access to savings, financial education and smart spending analytics.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='The company raised $4 million in seed funding after its YC S21 participation. Fingo offers users a bank account, paired with free peer-to-peer transactions and access to savings, financial education and smart spending analytics. The FDIC is looking into Tellus, an Andreessen <PERSON>itz-backed fintech company that claims it can offer people higher yields on their savings balances by using that money to fund certain U.S. single-family-home loans. U.S. Senator <PERSON><PERSON><PERSON>, chairman of the Senate Banking, Housing, and Urban Affairs Committee, wrote a letter to FDIC Chairman <PERSON> expressing concerns about Tellus, and asking the FDIC to review Tellus’s business practices which may put customers at risk.\\n\\nMessaging\\n\\nWhatsApp now lets users create single-vote polls and forward media with captions, Meta announced this week. Single-vote polls let users run a poll where people are only allowed to vote once, including multiple choice, as has been the default.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='<PERSON>a announced this week. Single-vote polls let users run a poll where people are only allowed to vote once, including multiple choice, as has been the default. Reddit’s latest update provides link previews for messaging apps. Now, when you share a Reddit link via a messaging app, it will include a visual preview of the content, the subreddit name, the total upvotes tally and the number of comments. The update also includes the ability to share directly to IG Stories and other tools for publishers.\\n\\nTravel & Transportation\\n\\nFollowing its acquisition by Via, Citymapper said it’s lowering the paywall for its premium features while also introducing a new subscription plan ($1.49/mo) purely for removing ads.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='Following its acquisition by Via, Citymapper said it’s lowering the paywall for its premium features while also introducing a new subscription plan ($1.49/mo) purely for removing ads.\\n\\nwhile also introducing a new subscription plan ($1.49/mo) purely for removing ads. Uber reported a Q1 earnings beat with its revenue up 29% YoY to $8.82 billion, gross bookings up 19% YoY to $31.4 billion and adjusted EBITDA up 353% YoY to $761 million. It also reported a $157 million net loss.\\n\\ngross bookings up 19% YoY to $31.4 billion and adjusted EBITDA up 353% YoY to $761 million. It also reported a $157 million net loss. Uber Eats is also planning to offer support for Live Activities and Dynamic Island on iPhone and integrated with Alexa for order updates.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='for order updates. Lyft shared worrisome Q2 guidance sending its stock down after Q1 earnings where it had reported a 14% YoY increase in revenue to $1 billion and a net loss drop of 5% to $187.6 million. Ridership was up 9.8% YoY to 19.5 million.\\n\\nGaming\\n\\nSnowman, the mobile game studio behind Alto’s Adventure and Alto’s Odyssey, launched its newest title, Laya’s Horizon, exclusively with Netflix. The wingsuit game sees players mastering the art of flying, diving off mountains, weaving across forests and gliding over rivers to unlock new abilities as they explore a vast and peaceful world.\\n\\nCross-platform game engine Unity announced layoffs of 8% of its workforce, or around 600 jobs, after laying off 500+ in January and last June.\\n\\nafter laying off 500+ in January and last June. Amazon announced that customers in the United States, Canada, Germany and the United Kingdom can now play Fortnite on their Fire TVs via its Amazon Luna cloud gaming service.\\n\\nCommerce & Food Delivery', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='Commerce & Food Delivery\\n\\nAmazon Inspire, the e-commerce giant’s in-app TikTok-like shopping feed has rolled out to all customers in the United States. The company had been experimenting since last year with the new feed, which features content creators by influencers.\\n\\nThe company had been experimenting since last year with the new feed, which features content creators by influencers. DoorDash revenue was up 40% YoY in Q1, reaching $2.04 billion, beating estimates of $1.93 billion. Its net loss also declined 3% to $162 million and orders were up 27% to 512 million.\\n\\nEtc.\\n\\nAmazon rolled out a Matter update for Alexa that includes support for Thread, setup on iOS, and a new version of its Works with Alexa program.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='Etc.\\n\\nAmazon rolled out a Matter update for Alexa that includes support for Thread, setup on iOS, and a new version of its Works with Alexa program.\\n\\nand a new version of its Works with Alexa program. Match Group posted a Q1 earnings miss with revenue down by 1% YoY to $787 million and paying users down 3% to 15.9 million. The company, however, said it’s “very possible” the recent Apple-Epic court decision could result in App Store fee relief.\\n\\nMedtech startup Healthy.io, which provides urine analysis through a mobile app, is laying off a third of its staff, or around 70 people. The company had just raised $50 million in Series D funding.\\n\\nThe company had just raised $50 million in Series D funding. Airbnb announced Rooms, a feature that focuses on the ability to book single rooms averaging $67 per night as users complain about excessive fees, onerous checkout procedures and rising Airbnb prices.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='averaging $67 per night as users complain about excessive fees, onerous checkout procedures and rising Airbnb prices. Google’s smart home app, Google Home, added support for smart garage door openers.\\n\\nSecurity\\n\\nGoogle announced that passkeys are now rolling out to Google Account users globally. Passkey let users sign in to websites and apps using the same biometrics or screen-lock PIN they use to unlock their devices.\\n\\nPasskey let users sign in to websites and apps using the same biometrics or screen-lock PIN they use to unlock their devices. Google announced that in 2022, it prevented 1.43 million policy-violating apps from being published on Google Play “in part due to new and improved security features and policy enhancements.”\\n\\nGovernment, Policy and Lawsuits', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='Government, Policy and Lawsuits\\n\\nThe EU’s Digital Markets Act (DMA) became applicable on May 2, but enforcement is not expected until spring 2024. The act focused on gatekeepers like Apple, Google, Meta and Microsoft. It limits how they can use third-party data, bans self-preferencing, introduces interoperability requirements, bans tracking users for targeted ads without consent and more. It also says app stores can’t require the use of their own payment services and permits app sideloading.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='Bipartisan U.S. lawmakers reintroduced the Kids Online Safety Act with updates aimed at fixing earlier issues. The bill says platforms have to take reasonable steps to stop the spread of posts that promote eating disorders, suicide, substance abuse and more and undergo independent analysis about their safety for minors. It now also includes protections for support services, like the National Suicide Hotline, substance abuse groups and LGBTQ+ youth centers. However, critics, including the ACLU, say the changes are not enough and they remain opposed to the increased surveillance of kids this bill would require and other matters.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='The bill says platforms have to take reasonable steps to stop the spread of posts that promote eating disorders, suicide, substance abuse and more and undergo independent analysis about their safety for minors. It now also includes protections for support services, like the National Suicide Hotline, substance abuse groups and LGBTQ+ youth centers. However, critics, including the ACLU, say the changes are not enough and they remain opposed to the increased surveillance of kids this bill would require and other matters. France’s competition watchdog announced interim measures against Meta, saying it suspects Meta of abusing its dominant position in the French market for ads on social media and across the broader (non-search-related) online ads market.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='saying it suspects Meta of abusing its dominant position in the French market for ads on social media and across the broader (non-search-related) online ads market. The U.S. Federal Trade Commission (FTC) says Meta has “repeatedly violated” privacy rules and proposed to tighten its 2020 privacy order against the company, which would completely bar it from monetizing data from anyone under 18 in any way, among other new restrictions. The FTC also accused Meta of COPPA, a children’s privacy law, by misrepresenting its Messenger Kids parental controls, which allowed group chats and group calls with unapproved contacts.\\n\\nFunding and M&A\\n\\nAmazon acquired a small audio-focused artificial intelligence firm called Snackable.AI in 2022, The Post reported. Deal terms weren’t disclosed but <PERSON>, the founder and CEO of Snackable, is now the artificial intelligence and machine learning product leader at Amazon.\\n\\nDownloads\\n\\nRTRO', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='Downloads\\n\\nRTRO\\n\\nNew social networking startup RTRO launched its app this week with the goal of connecting brands, creators and their fans and followers in a more positive environment focused on human connections and communities, not algorithm-driven content. To accomplish this, RTRO divides its social experience into two parts — on one side, you can keep up with friends or family in RTRO’s “circles.” On the other side, users can switch over to see content from creators and brands in their own space, dubbed RTRO TV.\\n\\nDistroKid\\n\\nMusic distribution service DistroKid this week launched its first mobile app, initially only for iPhone. The new app lets artists upload new releases, receive instant payment alerts, access stats from Apple and Spotify, edit metadata and more from their phones. The company said the mobile app had been the number one request from DistroKid members.', metadata={'source': '/content/new_articles/05-06-this-week-in-apps-apple-and-google-team-up-on-trackers-google-i-o-preview-apps-hit-newfronts.txt'}),\n", " Document(page_content='OpenAI may be synonymous with machine learning now and Google is doing its best to pick itself up off the floor, but both may soon face a new threat: rapidly multiplying open source projects that push the state of the art and leave the deep-pocketed but unwieldy corporations in their dust. This Zerg-like threat may not be an existential one, but it will certainly keep the dominant players on the defensive.\\n\\nThe notion is not new by a long shot — in the fast-moving AI community, it’s expected to see this kind of disruption on a weekly basis — but the situation was put in perspective by a widely shared document purported to originate within Google. “We have no moat, and neither does OpenAI,” the memo reads.', metadata={'source': '/content/new_articles/05-05-google-and-openai-are-walmarts-besieged-by-fruit-stands.txt'}),\n", " Document(page_content='I won’t encumber the reader with a lengthy summary of this perfectly readable and interesting piece, but the gist is that while GPT-4 and other proprietary models have obtained the lion’s share of attention and indeed income, the head start they’ve gained with funding and infrastructure is looking slimmer by the day.\\n\\nWhile the pace of OpenAI’s releases may seem blistering by the standards of ordinary major software releases, GPT-3, ChatGPT and GPT-4 were certainly hot on each other’s heels if you compare them to versions of iOS or Photoshop. But they are still occurring on the scale of months and years.', metadata={'source': '/content/new_articles/05-05-google-and-openai-are-walmarts-besieged-by-fruit-stands.txt'}),\n", " Document(page_content='What the memo points out is that in March, a leaked foundation language model from Meta, called LLaMA, was leaked in fairly rough form. Within weeks, people tinkering around on laptops and penny-a-minute servers had added core features like instruction tuning, multiple modalities and reinforcement learning from human feedback. OpenAI and Google were probably poking around the code, too, but they didn’t — couldn’t — replicate the level of collaboration and experimentation occurring in subreddits and Discords.\\n\\nCould it really be that the titanic computation problem that seemed to pose an insurmountable obstacle — a moat — to challengers is already a relic of a different era of AI development?\\n\\nS<PERSON> already noted that we should expect diminishing returns when throwing parameters at the problem. Bigger isn’t always better, sure — but few would have guessed that smaller was instead.\\n\\nGPT-4 is a Walmart, and nobody actually likes Walmart', metadata={'source': '/content/new_articles/05-05-google-and-openai-are-walmarts-besieged-by-fruit-stands.txt'}),\n", " Document(page_content='GPT-4 is a Walmart, and nobody actually likes Walmart\\n\\nThe business paradigm being pursued by OpenAI and others right now is a direct descendant of the SaaS model. You have some software or service of high value and you offer carefully gated access to it through an API or some such. It’s a straightforward and proven approach that makes perfect sense when you’ve invested hundreds of millions into developing a single monolithic yet versatile product like a large language model.\\n\\nIf GPT-4 generalizes well to answering questions about precedents in contract law, great — never mind that a huge number of its “intellect” is dedicated to being able to parrot the style of every author who ever published a work in the English language. GPT-4 is like a Walmart. No one actually wants to go there, so the company makes damn sure there’s no other option.', metadata={'source': '/content/new_articles/05-05-google-and-openai-are-walmarts-besieged-by-fruit-stands.txt'}),\n", " Document(page_content='But customers are starting to wonder, why am I walking through 50 aisles of junk to buy a few apples? Why am I hiring the services of the largest and most general-purpose AI model ever created if all I want to do is exert some intelligence in matching the language of this contract against a couple hundred other ones? At the risk of torturing the metaphor (to say nothing of the reader), if GPT-4 is the Walmart you go to for apples, what happens when a fruit stand opens in the parking lot?', metadata={'source': '/content/new_articles/05-05-google-and-openai-are-walmarts-besieged-by-fruit-stands.txt'}),\n", " Document(page_content='It didn’t take long in the AI world for a large language model to be run, in highly truncated form of course, on (fittingly) a Raspberry Pi. For a business like OpenAI, its jockey Microsoft, Google or anyone else in the AI-as-a-service world, it effectively beggars the entire premise of their business: that these systems are so hard to build and run that they have to do it for you. In fact it starts to look like these companies picked and engineered a version of AI that fit their existing business model, not vice versa!', metadata={'source': '/content/new_articles/05-05-google-and-openai-are-walmarts-besieged-by-fruit-stands.txt'}),\n", " Document(page_content='Once upon a time you had to offload the computation involved in word processing to a mainframe — your terminal was just a display. Of course that was a different era, and we’ve long since been able to fit the whole application on a personal computer. That process has occurred many times since as our devices have repeatedly and exponentially increased their capacity for computation. These days when something has to be done on a supercomputer, everyone understands that it’s just a matter of time and optimization.\\n\\nFor Google and OpenAI, the time came a lot quicker than expected. And they weren’t the ones to do the optimizing — and may never be at this rate.', metadata={'source': '/content/new_articles/05-05-google-and-openai-are-walmarts-besieged-by-fruit-stands.txt'}),\n", " Document(page_content='For Google and OpenAI, the time came a lot quicker than expected. And they weren’t the ones to do the optimizing — and may never be at this rate.\\n\\nNow, that doesn’t mean that they’re plain out of luck. Google didn’t get where it is by being the best — not for a long time, anyway. Being a Walmart has its benefits. Companies don’t want to have to find the bespoke solution that performs the task they want 30% faster if they can get a decent price from their existing vendor and not rock the boat too much. Never underestimate the value of inertia in business!', metadata={'source': '/content/new_articles/05-05-google-and-openai-are-walmarts-besieged-by-fruit-stands.txt'}),\n", " Document(page_content='Sure, people are iterating on LLaMA so fast that they’re running out of camelids to name them after. Incidentally, I’d like to thank the developers for an excuse to just scroll through hundreds of pictures of cute, tawny vicuñas instead of working. But few enterprise IT departments are going to cobble together an implementation of Stability’s open source derivative-in-progress of a quasi-legal leaked Meta model over OpenAI’s simple, effective API. They’ve got a business to run!\\n\\nBut at the same time, I stopped using Photoshop years ago for image editing and creation because the open source options like Gimp and Paint.net have gotten so incredibly good. At this point, the argument goes the other direction. Pay how much for Photoshop? No way, we’ve got a business to run!', metadata={'source': '/content/new_articles/05-05-google-and-openai-are-walmarts-besieged-by-fruit-stands.txt'}),\n", " Document(page_content='What Google’s anonymous authors are clearly worried about is that the distance from the first situation to the second is going to be much shorter than anyone thought, and there doesn’t appear to be a damn thing anybody can do about it.\\n\\nExcept, the memo argues: embrace it. Open up, publish, collaborate, share, compromise. As they conclude:', metadata={'source': '/content/new_articles/05-05-google-and-openai-are-walmarts-besieged-by-fruit-stands.txt'}),\n", " Document(page_content='The best way to avoid a down round is to found an AI startup\\n\\nAs we see unicorns slash staff and the prevalence of down rounds spike, it may seem that the startup ecosystem is chock-full of bad news and little else. That’s not precisely the case.\\n\\nWhile AI, and in particular the generative AI subcategory, are as hot as the sun, not all venture attention is going to the handful of names that you already know. Sure, OpenAI is able to land nine and 10-figure rounds from a murderer’s row of tech investors and mega-cap corporations. And rising companies like Hugging Face and Anthropic cannot stay out of the news, proving that smaller AI-focused startups are doing more than well.\\n\\nIn fact, new data from Carta, which provides cap table management and other services, indicates that AI-focused startups are outperforming their larger peer group at both the seed and Series A stage.', metadata={'source': '/content/new_articles/05-06-ai-startups-q1-investments.txt'}),\n", " Document(page_content='The dataset, which notes that AI-centered startups are raising more and at higher valuations than other startups, indicates that perhaps the best way to avoid a down round today is to build in the artificial intelligence space.\\n\\nWhat the data says\\n\\nPer Carta data relating to the first quarter of the year, seed funding to non-AI startups in the U.S. market that use its services dipped from $1.64 billion to $1.08 billion, or a decline of around 34%. That result is directionally aligned with other data that we’ve seen regarding Q1 2023 venture capital totals; the data points down.', metadata={'source': '/content/new_articles/05-06-ai-startups-q1-investments.txt'}),\n", " Document(page_content='The legal spats between artists and the companies training AI on their artwork show no sign of abating.\\n\\nWithin the span of a few months, several lawsuits have emerged over generative AI tech from companies including OpenAI and Stability AI, brought by plaintiffs who allege that copyrighted data — mostly art — was used without their permission to train the generative models. Generative AI models “learn” to create art, code and more by “training” on sample images and text, usually scraped indiscriminately from the web.\\n\\nIn an effort to grant artists more control over how — and where — their art’s used, <PERSON> and <PERSON><PERSON> co-founded the startup Spawning AI. Spawning created HaveIBeenTrained, a website that allows creators to opt out of the training dataset for one art-generating AI model, Stable Diffusion v3, due to be released in the coming months.', metadata={'source': '/content/new_articles/05-03-spawning-lays-out-its-plans-for-letting-creators-opt-out-of-generative-ai-training.txt'}),\n", " Document(page_content='As of March, artists had used HaveIBeenTrained to remove 80 million pieces of artwork from the Stable Diffusion training set. By late April, that figure had eclipsed 1 billion.\\n\\nAs the demand for Spawning’s service grew, the company — which was entirely bootstrapped up until that point — sought an outside investment. And it got it. Spawning today announced that it raised $3 million in a seed round led by True Ventures with participation from the Seed Club Ventures, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Jacob.<PERSON>h and Noise DAO.\\n\\nSpeaking to TechCrunch via email, <PERSON> said that the funding will allow Spawning to continue developing “IP standards for the AI era” and establish more robust opt-out and opt-in standards.', metadata={'source': '/content/new_articles/05-03-spawning-lays-out-its-plans-for-letting-creators-opt-out-of-generative-ai-training.txt'}),\n", " Document(page_content='Speaking to TechCrunch via email, <PERSON> said that the funding will allow <PERSON><PERSON> to continue developing “IP standards for the AI era” and establish more robust opt-out and opt-in standards.\\n\\n“We are enthusiastic about the potential of AI tooling. We developed domain expertise in the field from being passionate about new opportunities AI provides to creators, but feel that consent is a fundamental layer to make these developments something everyone can feel good about,<PERSON> <PERSON> said.\\n\\nSpawning’s metrics speak for themselves. Clearly, there’s a demand from artists for more say in how their art’s used (or scraped, as the case may be). But beyond partnerships with art platforms like Shutterstock and ArtStation, Spawning hasn’t managed to rally the industry around a common opt-out or provenance standard.', metadata={'source': '/content/new_articles/05-03-spawning-lays-out-its-plans-for-letting-creators-opt-out-of-generative-ai-training.txt'}),\n", " Document(page_content='Adobe, which recently announced generative AI tools, is pursuing its own opt-out mechanisms and tooling. So is DeviantArt, which in November launched a protection that relies on HTML tags to prohibit the software robots that crawl pages for images from downloading those images for training sets. OpenAI, the generative AI giant in the room, still doesn’t offer an opt-out tool — nor has it announced plans to anytime soon.\\n\\nSpawning has also come under criticism for the opaqueness — and vagueness — of its opt-out process. As Ars Technica noted in a recent piece, the opt-out process doesn’t appear to fit the definition of consent for personal data use in Europe’s General Data Protection Regulation, which states that consent must be actively given, not assumed by default. Also unclear is how Spawning intends to legally verify the identities of artists who make opt-out requests — or indeed, if it intends to attempt this at all.', metadata={'source': '/content/new_articles/05-03-spawning-lays-out-its-plans-for-letting-creators-opt-out-of-generative-ai-training.txt'}),\n", " Document(page_content='Spawning’s solution is multipronged. First, it plans to make it easier for AI model trainers to honor opt-out requests and streamline the process for creators. Then, Spawning will offer more services to organizations seeking to protect the work of their artists, <PERSON> says.\\n\\n“We want to build the consent layer for AI, which we feel will be a fundamentally helpful piece of infrastructure moving forward,” he added. “We plan to grow Spawning to address the many different domains touched by the AI economy, as each domain has their own particular needs.”\\n\\nIn a first step toward this ambitious vision, Spawning in March enabled “domain opt-outs,” allowing creators and content partners to quickly opt-out content from whole websites. Spawning says that 30,000 domains to date have been registered in the system.', metadata={'source': '/content/new_articles/05-03-spawning-lays-out-its-plans-for-letting-creators-opt-out-of-generative-ai-training.txt'}),\n", " Document(page_content='April will mark the release of an API and open source Python package that’ll greatly expand the breadth of content that Spawning touches. Previously, opt-out requests through Spawning only applied to the LAION-5B dataset — the dataset used to train Stable Diffusion. As of April, any website, app or service will be able to use Spawning’s API to automatically comply with opt-outs not just for image data, but for text, audio, videos and more.\\n\\nMeyer says that Spawning will aggregate every new opt-out method (e.g. Adobe’s and DeviantArt’s) into its Python package for model trainers, with the goal of cutting down on the number of accounts model creators have to manage to comply with opt-out requests.', metadata={'source': '/content/new_articles/05-03-spawning-lays-out-its-plans-for-letting-creators-opt-out-of-generative-ai-training.txt'}),\n", " Document(page_content='To boost visibility, Spawning is partnering with Hugging Face, one of the larger platforms for hosting and running AI models, to add a new info box on Hugging Face that’ll alert users to the proportion of “opted-out” data within text-to-image datasets. The box will also link to a Spawning API sign-up page so that model trainers can remove opted-out images at training time.\\n\\n“We feel that once companies and developers know that the option to honor creator wishes is available, there is little reason not to honor them,<PERSON> <PERSON> said. “We are excited about the future of generative AI, but creators and organizations alike need standards in place to have their data work in their favor.”', metadata={'source': '/content/new_articles/05-03-spawning-lays-out-its-plans-for-letting-creators-opt-out-of-generative-ai-training.txt'}),\n", " Document(page_content='Looking ahead, Spawning intends to release an “exact-duplicate” detection feature to match opted-out images with copies that the platform finds across the web, followed by a “near-duplicate” detection feature to notify artists when Spawning finds likely copies of their work that’ve been cropped, compressed or otherwise slightly modified.\\n\\nBeyond that, there’s plans for a Chrome extension to let creators pre-emptively opt out of their work posted anywhere on the web and a caption search on the HaveIBeenTrained website to directly search image descriptions. The site’s current search tool uses only approximate matches between text and images as well as URL searches to find content hosted on specific websites.\\n\\nSpawning — now beholden to investors — plans to make money by building services on top of its content infrastructure, although <PERSON> wouldn’t divulge much. How that’ll sit with content creators remains to be seen.', metadata={'source': '/content/new_articles/05-03-spawning-lays-out-its-plans-for-letting-creators-opt-out-of-generative-ai-training.txt'}),\n", " Document(page_content='“We’ve spoken to quite a few organizations, with many conversations being too premature to announce, and think that our funding announcement and increased visibility will go some way to offer assurances that what we are building is a robust and dependable standard to work with,<PERSON> <PERSON> said. “After we complete these features, we’ll begin building infrastructure to support more datasets — including music, video and text.”', metadata={'source': '/content/new_articles/05-03-spawning-lays-out-its-plans-for-letting-creators-opt-out-of-generative-ai-training.txt'}),\n", " Document(page_content='Well that was fast. The U.K.’s competition watchdog has announced an initial review of “AI foundational models”, such as the large language models (LLMs) which underpin OpenAI’s ChatGPT and Microsoft’s New Bing. Generative AI models which power AI art platforms such as OpenAI’s DALL-E or Midjourney will also likely fall in scope.\\n\\nThe Competition and Markets Authority (CMA) said its review will look at competition and consumer protection considerations in the development and use of AI foundational models — with the aim of understanding “how foundation models are developing and producing an assessment of the conditions and principles that will best guide the development of foundation models and their use in the future”.\\n\\nIt’s proposing to publish the review in “early September”, with a deadline of June 2 for interested stakeholders to submit responses to inform its work.', metadata={'source': '/content/new_articles/05-04-cma-generative-ai-review.txt'}),\n", " Document(page_content='It’s proposing to publish the review in “early September”, with a deadline of June 2 for interested stakeholders to submit responses to inform its work.\\n\\n“Foundation models, which include large language models and generative artificial intelligence (AI), that have emerged over the past five years, have the potential to transform much of what people and businesses do. To ensure that innovation in AI continues in a way that benefits consumers, businesses and the UK economy, the government has asked regulators, including the [CMA], to think about how the innovative development and deployment of AI can be supported against five overarching principles: safety, security and robustness; appropriate transparency and explainability; fairness; accountability and governance; and contestability and redress,” the CMA wrote in a press release.”', metadata={'source': '/content/new_articles/05-04-cma-generative-ai-review.txt'}),\n", " Document(page_content='Stanford University’s Human-Centered Artificial Intelligence Center’s Center for Research on Foundation Models is credited with coining the term “foundational models”, back in 2021, to refer to AI systems that focus on training one model on a huge amount of data and adapting it to many applications.\\n\\n“The development of AI touches upon a number of important issues, including safety, security, copyright, privacy, and human rights, as well as the ways markets work. Many of these issues are being considered by government or other regulators, so this initial review will focus on the questions the CMA is best placed to address — what are the likely implications of the development of AI foundation models for competition and consumer protection?” the CMA added.\\n\\nIn a statement, its CEO, <PERSON>, also said:', metadata={'source': '/content/new_articles/05-04-cma-generative-ai-review.txt'}),\n", " Document(page_content='In a statement, its CEO, <PERSON>, also said:\\n\\nAI has burst into the public consciousness over the past few months but has been on our radar for some time. It’s a technology developing at speed and has the potential to transform the way businesses compete as well as drive substantial economic growth. It’s crucial that the potential benefits of this transformative technology are readily accessible to UK businesses and consumers while people remain protected from issues like false or misleading information. Our goal is to help this new, rapidly scaling technology develop in ways that ensure open, competitive markets and effective consumer protection.\\n\\nSpecifically, the U.K. competition regulator said its initial review of AI foundational models will:\\n\\nexamine how the competitive markets for foundation models and their use could evolve\\n\\nexplore what opportunities and risks these scenarios could bring for competition and consumer protection', metadata={'source': '/content/new_articles/05-04-cma-generative-ai-review.txt'}),\n", " Document(page_content='examine how the competitive markets for foundation models and their use could evolve\\n\\nexplore what opportunities and risks these scenarios could bring for competition and consumer protection\\n\\nproduce guiding principles to support competition and protect consumers as AI foundation models develop\\n\\nWhile it may seen early for the antitrust regulator to conduct a review of such a fast-moving emerging technology the CMA is acting on government instruction.\\n\\nAn AI white paper published in March signalled ministers’ preference to avoid setting any bespoke rules (or oversight bodies) to govern uses of artificial intelligence at this stage. However ministers said existing U.K. regulators — including the CMA, which was directly name-checked — would be expected to issue guidance to encourage safe, fair and accountable uses of AI.', metadata={'source': '/content/new_articles/05-04-cma-generative-ai-review.txt'}),\n", " Document(page_content='The CMA says its initial review of foundational AI models is in line with instructions in the white paper, where the government talked about existing regulators conducting “detailed risk analysis” in order to be in a position to carry out potential enforcements, i.e. on dangerous, unfair and unaccountable applications of AI, using their existing powers.\\n\\nThe regulator also points to its core mission — to support open, competitive markets — as another reason for taking a look at generative AI now.\\n\\nNotably, the competition watchdog is set to get additional powers to regulate Big Tech in the coming years, under plans taken off the back-burner by prime minister <PERSON><PERSON>’s government last month, when ministers said it would move forward with a long-trailed (but much delayed) ex ante reform aimed at digital giants’ market power.', metadata={'source': '/content/new_articles/05-04-cma-generative-ai-review.txt'}),\n", " Document(page_content='The expectation is that the CMA’s Digital Markets Unit, up and running since 2021 in shadow form, will (finally) gain legislative powers in the coming years to apply pro-active “pro-competition” rules which are tailored to platforms that are deemed to have “strategic market status” (SMS). So we can speculate that providers of powerful foundational AI models may, down the line, be judged to have SMS — meaning they could expect to face bespoke rules on how they must operate vis-a-vis rivals and consumers in the U.K. market.\\n\\nThe U.K.’s data protection watchdog, the ICO, also has its eye on generative AI. It’s another existing oversight body which the government has tasked with paying special mind to AI under its plan for context-specific guidance to steer development of the tech through the application of existing laws.', metadata={'source': '/content/new_articles/05-04-cma-generative-ai-review.txt'}),\n", " Document(page_content='In a blog post last month, <PERSON>, the ICO’s executive director of regulatory risk, offered some tips and a little warning for developers of generative AI when it comes to compliance with U.K. data protection rules. “Organisations developing or using generative AI should be considering their data protection obligations from the outset, taking a data protection by design and by default approach,” he suggested. “This isn’t optional — if you’re processing personal data, it’s the law.”\\n\\nOver the English Channel in the European Union, meanwhile, lawmakers are in the process of deciding a fixed set of rules that are likely to apply to generative AI.\\n\\nNegotiations toward a final text for the EU’s incoming AI rulebook are ongoing — but currently there’s a focus on how to regulate foundational models via amendments to the risk-based framework for regulating uses of AI the bloc published in draft over two years ago.', metadata={'source': '/content/new_articles/05-04-cma-generative-ai-review.txt'}),\n", " Document(page_content='It remains to be seen where the EU’s co-legislators will end up on what’s sometimes also referred to as general purpose AI. But, as we reported recently, parliamentarians are pushing for a layered approach to tackle safety issues with foundational models; the complexity of responsibilities across AI supply chains; and to address specific content concerns (like copyright) which are associated with generative AI.\\n\\nAdd to that, EU data protection law already applies to AI, of course. And privacy-focused investigations of models like ChatGPT are underway in the bloc — including in Italy where an intervention by the local watchdog led to OpenAI rushing out a series of privacy disclosures and controls last month.\\n\\nThe European Data Protection Board also recently set up a task force to support coordination between different data protection authorities on investigations of the AI chatbot. Others investigating ChatGPT include Spain’s privacy watchdog.', metadata={'source': '/content/new_articles/05-04-cma-generative-ai-review.txt'}),\n", " Document(page_content='It’s that time of week again, folks — Week in Review (WiR) time. For those new to the scene, WiR is TechCrunch’s regular newsletter that recaps the biggest tech stories over the past few days. There’s no better digest for the person on the go, we’d argue — but of course, we’re a little biased.\\n\\nBefore we get into the meat of the thing, a quick reminder that TC City Spotlight: Atlanta is fast approaching. On June 7, TechCrunch is headed to Atlanta, where we’ll host a pitch competition, a talk on the economics of equality, a panel discussion on investing in the Atlanta ecosystem and more.\\n\\nElsewhere, there’s a TechCrunch Live event with Persona and Index Ventures on May 10, which will touch on how Persona keeps pace with new threats and how Index made a prescient move to spot and back Persona early on. And we have Disrupt in San Francisco from September 19–21 — our annual conference is jam-packed with expert-led sessions and interviews with movers and shakers in the tech space.', metadata={'source': '/content/new_articles/05-06-amazon-launches-free-channels-check-marks-come-to-gmail-and-openai-raises-more-moolah.txt'}),\n", " Document(page_content='Now, with that out of the way, here’s the top headlines.\\n\\nmost read\\n\\nAmazon debuts free channels: Amazon is doubling down on free, ad-supported content with this week’s introduction of Fire TV Channels. The new, free and ad-supported video experience, which came to Fire TV devices this week, will be continuously updated throughout the day and integrated into several areas across the Fire TV interface, Sarah reports.\\n\\nBio update for a check: Briefly, a bug on Twitter let legacy blue check holders get their badge back by updating their bio. Readers will recall that blue checks on Twitter once signified that a user was “verified,” but now serve as an indication that they’re paying for Twitter’s premium subscription service, Twitter Blue. Verified users who chose not to pay recently faced the prospect of blue check removal — but not necessarily permanently, judging by the bug.', metadata={'source': '/content/new_articles/05-06-amazon-launches-free-channels-check-marks-come-to-gmail-and-openai-raises-more-moolah.txt'}),\n", " Document(page_content='Google ditches passwords for passkeys: This week Google rolled out passkeys to Google Account users globally, roughly a year after the company — alongside Apple, Microsoft and the FIDO Alliance — announced a partnership to broadly advance passkey adoption. With passkeys, users’ authentication synchronizes across devices through the cloud using cryptographic key pairs, allowing them to sign in to websites and apps using the same biometrics or screen-lock PIN they use to unlock their devices.\\n\\nMicrosoft debuts Pegasus: Microsoft this week announced that it’ll extend the Startup Founders Hub, its self-service platform that provides founders with free resources, including Azure credits, with a new incubator program called the Pegasus Program. Pegasus will select startups with products that “fill a market need” and give them up to $350,000 in Azure, GitHub and LinkedIn credits plus backing from advisors, as well as “access to the best Microsoft tech,” Microsoft says.', metadata={'source': '/content/new_articles/05-06-amazon-launches-free-channels-check-marks-come-to-gmail-and-openai-raises-more-moolah.txt'}),\n", " Document(page_content='Blue check marks come to Gmail: Google is going to start displaying a blue check mark next to select senders’ names on Gmail to verify their identity, the company said on Wednesday. The check marks will automatically appear next to companies that have adopted Gmail’s existing brand indicators for message identification feature, reports <PERSON>sha.\\n\\nOpenAI rakes in the dough: OpenAI, the startup behind the widely used conversational AI model ChatGPT, has picked up new backers. In an exclusive report, <PERSON><PERSON><PERSON><PERSON> and <PERSON> reveal that VC firms, including Sequoia Capital, Andreessen Horowitz, Thrive, K2 Global and Founders Fund, have put just over $300 million into OpenAI, valuing the company at between $27 billion and $29 billion.', metadata={'source': '/content/new_articles/05-06-amazon-launches-free-channels-check-marks-come-to-gmail-and-openai-raises-more-moolah.txt'}),\n", " Document(page_content='Apple releases security fix: On Monday, Apple released its first batch of publicly available “rapid security” patches, aimed at quickly fixing security vulnerabilities that are under active exploitation or pose significant risks to its customers. Apple says that these patches, which are enabled by default, were intended to let customers update their devices faster than a typical software upgrade.\\n\\nMusk settles for less: A defamation case brought against Tesla chief executive <PERSON><PERSON> by critic <PERSON><PERSON> is coming to a close, reportedly costing the billionaire ten big ones. Lawyers representing <PERSON>hi — a vocal member of the TSLAQ short-seller community on Twitter who rose to prominence as a skeptic of Tesla’s gigafactory plans and “full self-driving” tech — said in a statement that <PERSON>sk asked to settle the nearly three-year-old case back in March.', metadata={'source': '/content/new_articles/05-06-amazon-launches-free-channels-check-marks-come-to-gmail-and-openai-raises-more-moolah.txt'}),\n", " Document(page_content='A new LLM for Alexa: Amazon is building a more “generalized and capable” large language model to power Alexa, said Amazon CEO <PERSON> during the company’s first-quarter earnings call this week. He added that although Amazon has had an LLM powering Alexa, Amazon is working on one that’s more capable than the current one.\\n\\naudio', metadata={'source': '/content/new_articles/05-06-amazon-launches-free-channels-check-marks-come-to-gmail-and-openai-raises-more-moolah.txt'}),\n", " Document(page_content='TechCrunch’s stable of podcasts grows by the day — and it’s all quality stuff. This week, the Equity folks covered First Republic Bank, <PERSON><PERSON><PERSON>’s shutdown, Databricks’ acquisition, who’s going head-to-head with <PERSON><PERSON>, the rise of down rounds and why <PERSON><PERSON> had them feeling less gray. Meanwhile, <PERSON> spoke with <PERSON> about how his company, Marker Learning, is cutting the cost of learning disability assessments by conducting them remotely. Chain Reaction interviewed <PERSON>, the chief policy officer at Blockchain Association, a nonprofit organization focused on promoting “pro-innovation” policy for the digital asset world. On The TechCrunch Podcast — which, like WiR, covers the week in tech news — <PERSON> talked about whether Meta’s cavalier approach to compliance might finally be coming to a close. And last but not least, TechCrunch Live profiled <PERSON>, the founder of ClassDojo, and <PERSON>, the founder and CEO of SignalFire, about playing the long game', metadata={'source': '/content/new_articles/05-06-amazon-launches-free-channels-check-marks-come-to-gmail-and-openai-raises-more-moolah.txt'}),\n", " Document(page_content='be coming to a close. And last but not least, TechCrunch Live profiled <PERSON>, the founder of ClassDojo, and <PERSON>, the founder and CEO of SignalFire, about playing the long game in edtech, investing in companies that aren’t rushing to monetize and the “outsider advantage.”', metadata={'source': '/content/new_articles/05-06-amazon-launches-free-channels-check-marks-come-to-gmail-and-openai-raises-more-moolah.txt'}),\n", " Document(page_content='TechCrunch+\\n\\nTC+ subscribers get access to in-depth commentary, analysis and surveys — which you know if you’re already a subscriber. If you’re not, consider signing up. Here are a few highlights from this week:\\n\\nA cloudy future: Lyft’s equity is selling off in the wake of the U.S. ride-hailing giant’s first-quarter results and its comments regarding the current quarter, and how its new strategic posture will affect its growth and economics in the coming quarters. But there’s not necessarily cause for panic. <PERSON> and <PERSON> write about Lyft’s new tack and the potential upsides, of which there are several.', metadata={'source': '/content/new_articles/05-06-amazon-launches-free-channels-check-marks-come-to-gmail-and-openai-raises-more-moolah.txt'}),\n", " Document(page_content='Down but not out: For the past year, everyone’s been predicting that the muted exit environment and bone-dry funding market would bring a reckoning for many late-stage companies. Down rounds carry a negative connotation and are often interpreted as the fault of the company or founder. But in a market where everything seems to be heading downward, they shouldn’t imply a company or its founders made a mistake — you often simply can’t help it, <PERSON> writes.\\n\\nChatGPT, meet edtech: Shares of edtech company Chegg fell off a cliff this week even after the company reported Q1 results that bested analyst expectations. In its earnings call, the company’s executives noted that ChatGPT was slowing its ability to add new subscribers, not only potentially slowing growth but also throwing uncertainty into its ability to predict its future financial results. <PERSON> and <PERSON> M dig deeper.', metadata={'source': '/content/new_articles/05-06-amazon-launches-free-channels-check-marks-come-to-gmail-and-openai-raises-more-moolah.txt'}),\n", " Document(page_content='Get your TechCrunch fix IRL. Join us at Disrupt 2023 in San Francisco this September to immerse yourself in all things startup. From headline interviews to intimate roundtables to a jam-packed startup expo floor, there’s something for everyone at Disrupt. Save up to $800 when you buy your pass now through May 15, and save 15% on top of that with promo code WIR. Learn more.', metadata={'source': '/content/new_articles/05-06-amazon-launches-free-channels-check-marks-come-to-gmail-and-openai-raises-more-moolah.txt'}),\n", " Document(page_content='ChatGPT: Everything you need to know about the AI-powered chatbot\\n\\nChatGPT, OpenAI’s text-generating AI chatbot, has taken the world by storm. It’s able to write essays, code and more given short text prompts, hyper-charging productivity. But it also has a more…nefarious side.\\n\\nIn any case, AI tools are not going away — and indeed has expanded dramatically since its launch just a few months ago. Major brands are experimenting with it, using the AI to generate ad and marketing copy, for example.\\n\\nAnd OpenAI is heavily investing in it. ChatGPT was recently super-charged by GPT-4, the latest language-writing model from OpenAI’s labs. Paying ChatGPT users have access to GPT-4, which can write more naturally and fluently than the model that previously powered ChatGPT. In addition to GPT-4, OpenAI recently connected ChatGPT to the internet with plugins available in alpha to users and developers on the waitlist.', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='Here’s a timeline of ChatGPT product updates and releases, starting with the latest, to be updated regularly. We also answer the most common FAQs (see below).\\n\\nTimeline of the most recent ChatGPT updates\\n\\nMay 3, 2023\\n\\nMeta said in a report on May 3 that malware posing as ChatGPT was on the rise across its platforms.The company said that since March 2023, its security teams have uncovered 10 malware families using ChatGPT (and similar themes) to deliver malicious software to users’ devices.\\n\\n“In one case, we’ve seen threat actors create malicious browser extensions available in official web stores that claim to offer ChatGPT-based tools,” said Meta security engineers <PERSON><PERSON> and <PERSON> in a blog post. “They would then promote these malicious extensions on social media and through sponsored search results to trick people into downloading malware.”\\n\\nApril 28, 2023', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='April 28, 2023\\n\\nVC firms including Sequoia Capital, Andreessen Horowitz, Thrive and K2 Global are picking up new shares, according to documents seen by TechCrunch. A source tells us Founders Fund is also investing. Altogether the VCs have put in just over $300 million at a valuation of $27 billion to $29 billion. This is separate to a big investment from Microsoft announced earlier this year, a person familiar with the development told TechCrunch, which closed in January. The size of Microsoft’s investment is believed to be around $10 billion, a figure we confirmed with our source.\\n\\nApril 25, 2023\\n\\nCalled ChatGPT Business, OpenAI describes the forthcoming offering as “for professionals who need more control over their data as well as enterprises seeking to manage their end users.”', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='Called ChatGPT Business, OpenAI describes the forthcoming offering as “for professionals who need more control over their data as well as enterprises seeking to manage their end users.”\\n\\n“ChatGPT Business will follow our API’s data usage policies, which means that end users’ data won’t be used to train our models by default,” OpenAI wrote in a blog post. “We plan to make ChatGPT Business available in the coming months.”\\n\\nApril 24, 2023\\n\\nOpenAI applied for a trademark for “GPT,” which stands for “Generative Pre-trained Transformer,” last December. Last month, the company petitioned the USPTO to speed up the process, citing the “myriad infringements and counterfeit apps” beginning to spring into existence.\\n\\nUnfortunately for OpenAI, its petition was dismissed last week. According to the agency, OpenAI’s attorneys neglected to pay an associated fee as well as provide “appropriate documentary evidence supporting the justification of special action.”', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='That means a decision could take up to five more months.\\n\\nApril 22, 2023\\n\\nAuto-GPT is an open source app created by game developer <PERSON><PERSON> that uses OpenAI’s latest text-generating models, GPT-3.5 and GPT-4, to interact with software and services online, allowing it to “autonomously” perform tasks.\\n\\nDepending on what objective the tool’s provided, Auto-GPT can behave in very… unexpected ways. One Reddit user claims that, given a budget of $100 to spend within a server instance, Auto-GPT made a wiki page on cats, exploited a flaw in the instance to gain admin-level access and took over the Python environment in which it was running — and then “killed” itself.\\n\\nApril 18, 2023\\n\\nFTC chair <PERSON><PERSON> and fellow commissioners warned House representatives of the potential for modern AI technologies, like ChatGPT, to be used to “turbocharge” fraud in a congressional hearing.', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='FTC chair <PERSON><PERSON> and fellow commissioners warned House representatives of the potential for modern AI technologies, like ChatGPT, to be used to “turbocharge” fraud in a congressional hearing.\\n\\n“AI presents a whole set of opportunities, but also presents a whole set of risks,” <PERSON> told the House representatives. “And I think we’ve already seen ways in which it could be used to turbocharge fraud and scams. We’ve been putting market participants on notice that instances in which AI tools are effectively being designed to deceive people can place them on the hook for FTC action,” she stated.\\n\\nApril 17, 2023', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='April 17, 2023\\n\\nThe company behind the popular iPhone customization app Brass, sticker maker StickerHub and others is out today with a new AI chat app called SuperChat, which allows iOS users to chat with virtual characters powered by OpenAI’s ChatGPT. However, what makes the app different from the default ChatGPT experience or the dozens of generic AI chat apps now available are the characters offered which you can use to engage with SuperChat’s AI features.\\n\\nApril 12, 2023\\n\\nItaly’s data protection watchdog has laid out what OpenAI needs to do for it to lift an order against ChatGPT issued at the end of last month — when it said it suspected the AI chatbot service was in breach of the EU’s GSPR and ordered the U.S.-based company to stop processing locals’ data.\\n\\nThe DPA has given OpenAI a deadline — of April 30 — to get the regulator’s compliance demands done. (The local radio, TV and internet awareness campaign has a slightly more generous timeline of May 15 to be actioned.)', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='April 12, 2023\\n\\nA study co-authored by scientists at the Allen Institute for AI shows that assigning ChatGPT a “persona” — for example, “a bad person,” “a horrible person” or “a nasty person” — through the ChatGPT API increases its toxicity sixfold. Even more concerning, the co-authors found having ChatGPT pose as certain historical figures, gendered people and members of political parties also increased its toxicity — with journalists, men and Republicans in particular causing the machine learning model to say more offensive things than it normally would.\\n\\nThe research was conducted using the latest version of ChatGPT, but not the model currently in preview based on OpenAI’s GPT-4.\\n\\nApril 4, 2023\\n\\nYC Demo Day’s Winter 2023 batch features no fewer than four startups that claim to be building “ChatGPT for X.” They’re all chasing after a customer service software market that’ll be worth $58.1 billion by 2023, assuming the rather optimistic prediction from Acumen Research comes true.', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='Here are the YC-backed startups that caught our eye:\\n\\nYuma, whose customer demographic is primarily Shopify merchants, provides ChatGPT-like AI systems that integrate with help desk software, suggesting drafts of replies to customer tickets.\\n\\nBaselit, which uses one of OpenAI’s text-understanding models to allow businesses to embed chatbot-style analytics for their customers.\\n\\nLasso customers send descriptions or videos of the processes they’d like to automate and the company combines ChatGPT-like interface with robotic process automation (RPA) and a Chrome extension to build out those automations.\\n\\nBerriAI, whose platform is designed to help developers spin up ChatGPT apps for their organization data through various data connectors.\\n\\nApril 1, 2023\\n\\nOpenAI has started geoblocking access to its generative AI chatbot, ChatGPT, in Italy.', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='April 1, 2023\\n\\nOpenAI has started geoblocking access to its generative AI chatbot, ChatGPT, in Italy.\\n\\nItaly’s data protection authority has just put out a timely reminder that some countries do have laws that already apply to cutting edge AI: it has ordered OpenAI to stop processing people’s data locally with immediate effect. The Italian DPA said it’s concerned that the ChatGPT maker is breaching the European Union’s General Data Protection Regulation (GDPR), and is opening an investigation.\\n\\nMarch 29, 2023\\n\\nThe letter’s signatories include <PERSON><PERSON>, <PERSON> and <PERSON> of the Center for Humane Technology, among others. The letter calls on “all AI labs to immediately pause for at least 6 months the training of AI systems more powerful than GPT-4.”\\n\\nThe letter reads:', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='The letter reads:\\n\\nContemporary AI systems are now becoming human-competitive at general tasks,[3] and we must ask ourselves: Should we let machines flood our information channels with propaganda and untruth? Should we automate away all the jobs, including the fulfilling ones? Should we develop nonhuman minds that might eventually outnumber, outsmart, obsolete and replace us? Should we risk loss of control of our civilization? Such decisions must not be delegated to unelected tech leaders. Powerful AI systems should be developed only once we are confident that their effects will be positive and their risks will be manageable.\\n\\nMarch 23, 2023', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='March 23, 2023\\n\\nOpenAI launched plugins for ChatGPT, extending the bots functionality by granting it access to third-party knowledge sources and databases, including the web. Available in alpha to ChatGPT users and developers on the waitlist, OpenAI says that it’ll initially prioritize a small number of developers and subscribers to its premium ChatGPT Plus plan before rolling out larger-scale and API access.\\n\\nMarch 14, 2023\\n\\nGPT-4 is a powerful image- and text-understanding AI model from OpenAI. Released March 14, GPT-4 is available for paying ChatGPT Plus users and through a public API. Developers can sign up on a waitlist to access the API.\\n\\nMarch 9, 2023\\n\\nChatGPT is generally available through the Azure OpenAI Service, Microsoft’s fully managed, corporate-focused offering. Customers, who must already be “Microsoft managed customers and partners,” can apply here for special access.\\n\\nMarch 1, 2023', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='March 1, 2023\\n\\nOpenAI makes another move toward monetization by launching a paid API for ChatGPT. Instacart, Snap (Snapchat’s parent company) and Quizlet are among its initial customers.\\n\\nFebruary 7, 2023\\n\\nAt a press event in Redmond, Washington, Microsoft announced its long-rumored integration of OpenAI’s GPT-4 model into Bing, providing a ChatGPT-like experience within the search engine. The announcement spurred a 10x increase in new downloads for Bing globally, indicating a sizable consumer demand for new AI experiences.\\n\\nOther companies beyond Microsoft joined in on the AI craze by implementing ChatGPT, including OkCupid, Kaito, Snapchat and Discord — putting the pressure on Big Tech’s AI initiatives, like Google.\\n\\nFebruary 1, 2023\\n\\nAfter ChatGPT took the internet by storm, OpenAI launched a new pilot subscription plan for ChatGPT called ChatGPT Plus, aiming to monetize the technology starting at $20 per month.\\n\\nDecember 8, 2022', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='December 8, 2022\\n\\nA week after ChatGPT was released into the wild, two developers — <PERSON> and <PERSON> — made a Chrome extension called ShareGPT to make it easier to capture and share the AI’s answers with the world.\\n\\nNovember 30, 2022\\n\\nGPT-3.5 broke cover with ChatGPT, a fine-tuned version of GPT-3.5 that’s essentially a general-purpose chatbot. ChatGPT can engage with a range of topics, including programming, TV scripts and scientific concepts.\\n\\nWriters everywhere rolled their eyes at the new technology, much like artists did with OpenAI’s DALL-E model, but the latest chat-style iteration seemingly broadened its appeal and audience.\\n\\nFAQs:\\n\\nWhat is ChatGPT? How does it work?\\n\\nChatGPT is a general-purpose chatbot that uses artificial intelligence to generate text after a user enters a prompt, developed by tech startup OpenAI. The chatbot uses GPT-4, a large language model that uses deep learning to produce human-like text.\\n\\nWhen did ChatGPT get released?', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='When did ChatGPT get released?\\n\\nNovember 30, 2022 is when ChatGPT was released for public use.\\n\\nWhat is the latest version of ChatGPT?\\n\\nBoth the free version of ChatGPT and the paid ChatGPT Plus are regularly updated with new GPT models. The most recent model is GPT-4.\\n\\nIs ChatGPT free?\\n\\nThere is a free version of ChatGPT that only requires a sign-in in addition to the paid version, ChatGPT Plus.\\n\\nWho uses ChatGPT?\\n\\nAnyone can use ChatGPT! More and more tech companies and search engines are utilizing the chatbot to automate text or quickly answer user questions/concerns.\\n\\nWhat is the difference between ChatGPT and a chatbot?\\n\\nA chatbot can be any software/system that holds dialogue with you/a person but doesn’t necessarily have to be AI-powered. For example, there are chatbots that are rules-based in the sense that they’ll give canned responses to questions.\\n\\nChatGPT is AI-powered and utilizes LLM technology to generate text after a prompt.\\n\\nCan ChatGPT write essays?\\n\\nYes.', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='ChatGPT is AI-powered and utilizes LLM technology to generate text after a prompt.\\n\\nCan ChatGPT write essays?\\n\\nYes.\\n\\nCan ChatGPT commit libel?\\n\\nDue to the nature of how these models work, they don’t know or care whether something is true, only that it looks true. That’s a problem when you’re using it to do your homework, sure, but when it accuses you of a crime you didn’t commit, that may well at this point be libel.\\n\\nWe will see how handling troubling statements produced by ChatGPT will play out over the next few months as tech and legal experts attempt to tackle the fastest moving target in the industry.\\n\\nDoes ChatGPT have an app?\\n\\nThere is not an app available for iPhone or Android, but users have options to enable the chatbot on their mobile devices via their browser or a third-party app that uses ChatGPT’s public API.\\n\\nWhat is the ChatGPT character limit?', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='What is the ChatGPT character limit?\\n\\nIt’s not documented anywhere that ChatGPT has a character limit. However, users have noted that there are some character limitations after around 500 words.\\n\\nDoes ChatGPT have an API?\\n\\nYes, it was released March 1, 2023.\\n\\nWhat are some sample everyday uses for ChatGPT?\\n\\nEveryday examples include programing, scripts, email replies, listicles, blog ideas, summarization, etc.\\n\\nWhat are some advanced uses for ChatGPT?\\n\\nAdvanced use examples include debugging code, programming languages, scientific concepts, complex problem solving, etc.\\n\\nHow good is ChatGPT at writing code?\\n\\nIt depends on the nature of the program. While ChatGPT can write workable Python code, it can’t necessarily program an entire app’s worth of code. That’s because ChatGPT lacks context awareness — in other words, the generated code isn’t always appropriate for the specific context in which it’s being used.\\n\\nCan you save a ChatGPT chat?', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='Can you save a ChatGPT chat?\\n\\nYes. OpenAI allows users to save chats in the ChatGPT interface, stored in the sidebar of the screen. There are no built-in sharing features yet.\\n\\nAre there alternatives to ChatGPT?\\n\\nYes. There are multiple AI-powered chatbot competitors such as Together, Google’s <PERSON>d and Anthropic’s <PERSON>, and developers are creating open source alternatives. But the latter are harder — if not impossible — to run today.\\n\\nHow does ChatGPT handle data privacy?\\n\\nOpenAI has said that individuals in “certain jurisdictions” (such as the EU) can object to the processing of their personal information by its AI models by filling out this form. This includes the ability to make requests for deletion of AI-generated references about you. Although OpenAI notes it may not grant every request since it must balance privacy requests against freedom of expression “in accordance with applicable laws”.', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='The web form for making a deletion of data about you request is entitled “OpenAI Personal Data Removal Request”.\\n\\nIn its privacy policy, the ChatGPT maker makes a passing acknowledgement of the objection requirements attached to relying on “legitimate interest” (LI), pointing users towards more information about requesting an opt out — when it writes: “See here for instructions on how you can opt out of our use of your information to train our models.”\\n\\nWhat controversies have surrounded ChatGPT?\\n\\nRecently, Discord announced that it had integrated OpenAI’s technology into its bot named Clyde where two users tricked <PERSON> into providing them with instructions for making the illegal drug methamphetamine (meth) and the incendiary mixture napalm.\\n\\nAn Australian mayor has publicly announced he may sue OpenAI for defamation due to ChatGPT’s false claims that he had served time in prison for bribery. This would be the first defamation lawsuit against the text-generating service.', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='CNET found itself in the midst of controversy after Futurism reported the publication was publishing articles under a mysterious byline completely generated by AI. The private equity company that owns CNET, Red Ventures, was accused of using ChatGPT for SEO farming, even if the information was incorrect.\\n\\nSeveral major school systems and colleges, including New York City Public Schools, have banned ChatGPT from their networks and devices. They claim that the AI impedes the learning process by promoting plagiarism and misinformation, a claim that not every educator agrees with.\\n\\nThere have also been cases of ChatGPT accusing individuals of false crimes.\\n\\nWhere can I find examples of ChatGPT prompts?\\n\\nSeveral marketplaces host and provide ChatGPT prompts, either for free or for a nominal fee. One is PromptBase. Another is ChatX. More launch every day.\\n\\nCan ChatGPT be detected?\\n\\nPoorly. Several tools claim to detect ChatGPT-generated text, but in our tests, they’re inconsistent at best.', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='Can ChatGPT be detected?\\n\\nPoorly. Several tools claim to detect ChatGPT-generated text, but in our tests, they’re inconsistent at best.\\n\\nAre ChatGPT chats public?\\n\\nNo. But OpenAI recently disclosed a bug, since fixed, that exposed the titles of some users’ conversations to other people on the service.\\n\\nWho owns the copyright on ChatGPT-created content or media?\\n\\nThe user who requested the input from ChatGPT is the copyright owner.\\n\\nWhat lawsuits are there surrounding ChatGPT?\\n\\nNone specifically targeting ChatGPT. But OpenAI is involved in at least one lawsuit that has implications for AI systems trained on publicly available data, which would touch on ChatGPT.\\n\\nAre there issues regarding plagiarism with ChatGPT?\\n\\nYes. Text-generating AI models like ChatGPT have a tendency to regurgitate content from their training data.', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content=\"<PERSON><PERSON> on the ‘exhilarating mix’ of thrill and hazard at the frontiers of tech 'That's always an exciting place to be — a place where nobody's ever been before.'\\n\\nVint <PERSON><PERSON> has been a near-constant influence on the internet since the days when he was helping create it in the first place. Today he wears many hats, among them VP and chief internet evangelist at Google. He is to be awarded the IEEE’s Medal of Honor at a gala in Atlanta, and ahead of the occasion he spoke with TechCrunch in a wide-ranging interview touching on his work, AI, accessibility and interplanetary internet.\\n\\nTechCrunch: To start out with, can you tell us how Google has changed in your time there?\", metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='TechCrunch: To start out with, can you tell us how Google has changed in your time there?\\n\\nCerf: Well, when I joined the company in 2005, there were 5,000 people already, which is pretty damn big. And of course, my normal attire is three piece suits. The important thing is that I thought I would be raising the sartorial quotient of the company by joining. And now, almost 18 years later, there are 170-some-odd thousand people, and I have failed miserably. So I hope you don’t mind if I take my jacket off.\\n\\nGo right ahead.', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='Go right ahead.\\n\\nSo as you might have noticed, <PERSON> has come back to do a little bit more on the artificial intelligence side of things, which is something he’s always been interested in; I would say historically, we’ve always had an interest in artificial intelligence. But that has escalated significantly over the past decade or so. The acquisition of DeepMind was a brilliant choice. And you can see some of the outcomes first of the spectacular stuff, like playing Go and winning. And then the more productive stuff, like figuring out how 200 million proteins are folded up.', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='Then there’s the large language models and the chatbots. And I think we’re still in a very peculiar period of time, where we’re trying to characterize what these things can and can’t do, and how they go off the rails, and how do you take advantage of them to do useful work? How do we get them to distinguish fact from fiction? All of that is in my view open territory, but then that’s always an exciting place to be — a place where nobody’s ever been before. The thrill of discovery and the risk of hazard create a fairly exciting mix — an exhilarating mix.\\n\\n<PERSON><PERSON> gave a talk recently about, I don’t want to say the dangers of the large language models, but…\\n\\nWell, I did say there are hazards there. I was talking to a bunch of investment bankers, or VCs, and I said, you know, don’t try to sell stuff to your investors just because it’s flashy and shiny. Be cautious about going too fast and trying to apply it without figuring out how to put guardrails in place.', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='I raised a question of hazard and wanting people to be more thoughtful about which applications made sense. I even suggested an analogy: you know how the Society of Automotive Engineers, they have different risk levels for the self driving cars — a risk level idea could apply to artificial intelligence and machine learning.\\n\\nFor entertainment purposes, perhaps it’s not too concerning, unless it goes down some dark path, in which case, you might want to put some friction into the system to deal with that, especially a younger user. But then, as you get to the point where you’re training these things to do medical diagnosis or make investment advice, or make decisions about whether somebody gets out of jail… now suddenly, the risk factors are extremely high.\\n\\nWe shouldn’t be unaware of those risk factors. We can, as we build applications, be prepared to detect excursions away from safe territory, so that we don’t accidentally inflict some harm by the use of these kinds of technologies.', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='So we need some kind of guardrails.\\n\\nAgain, I’m not expert in this space, but I am beginning to wonder whether we need something kind of like that in order to provide a “super-ego” for the natural language network. So when it starts to go off the rails somewhere, we can observe that that’s happening. And a second network that’s observing both the input and the output might intervene, somehow, and stop the the production of the output.\\n\\nSort of a conscience function?\\n\\nWell, it’s not quite conscience, it’s closer to executive function — the prefrontal cortex. I want to be careful, I’m only reasoning by metaphor here.\\n\\nI know that Microsoft has embarked on something like this. Their version of GPT-4 has an intermediary model like that, they call it Prometheus.', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='I know that Microsoft has embarked on something like this. Their version of GPT-4 has an intermediary model like that, they call it Prometheus.\\n\\nPurely as an observation, I had the impression that the Prometheus natural language model would detect and intervene if it thought that the interactions were going down with dark path. I thought that they would implement it in such a way that before you actually say something to the interlocutor that is going down the dark path, you intervene and prevent it from going there at all.\\n\\nMy impression, though, is that it actually produces the output and then discovers that it’s produced it, but and then it says, “Oh, I shouldn’t have done that. Oh, dear, I take that back,” or “I don’t want to talk to you anymore about that.” It’s a little bit like the email that you get occasionally from the Microsoft Outlook system that says, “This person would like to withdraw the message.”', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='I love when that happens… it makes me want to read the original message so badly, even if I wouldn’t have before.\\n\\nYeah, exactly. It’s sort of like putting a big red flag in there saying, boy there’s something juicy in here.\\n\\nY<PERSON> mentioned the AI models, that it’s an interesting place to work. Do you get the same sort of foundational flavor that you got from working on protocols and other big shared things over the years?\\n\\nWell, what we are seeing is emergent properties of these large language models, that are not necessarily anticipated. And there have been emergent properties showing up in the protocol world. Flow control in particular is a vast headache in the online packet switch environment, and people have been tackling these problems inside and outside of Google for years.', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='One of the examples of emergent properties that I think very few of us thought about is the domain name business. Once they had value, suddenly, all kinds of emergent properties show up, people with interests that conflict and have to be resolved. Same for internet address space, it’s an even more weird environment where people actually buy IPv4 addresses for like $50 each.\\n\\nI confess to you that as I watched the auctions for IPv4 address space, I was thinking how stupid I was. When I was at the Defense Department in charge of all this, I should have allocated the slash eight, which is 16 million addresses, to myself, and just sit on it, you know, for 50 years, then sell it and retire.', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='Even simple systems have the ability to surprise you. Especially when you have simple systems when a large number of them are interacting with each other. I’ve found myself not necessarily recognizing when these emergent properties will come, but I will say that whenever something gets monetized, you should anticipate there will be emergent properties and possibly unexpected behavior, all driven by greed.\\n\\nLet me ask you about some some other stuff you’re working on. I’m always happy when I see cutting-edge tech being applied to people who need it, people with disabilities, people who like just have not been addressed by the current use cases of tech. Are you still working in the accessibility community?', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='I am very active in the accessibility space. At Google, we have a number of what we call employee resource groups, or ERGs. Yeah, some of them I, executive sponsor for one for Googlers who have hearing problems. And there is a disabilities oriented group, which involves employees who either have disabilities or family members that have disabilities, and they share their stories with each other because often people have similar problems, but don’t know what the solutions were for other people. Also, it’s just nice to know that you’re not alone in some of these challenges. There’s another group called the Grayglers for people that have a little gray in their hair, and I’m the executive sponsor for that. And of course, the focus of attention there is the challenges that arise as you get older, even as you think about retirement and things like that.', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='When a lot of so-called Web 2.0 stuff came out 10 years ago, it was totally inaccessible, broke all the screen readers, all this kind of stuff. Somebody has to step in and say, look, we need to have this standard, or else you’re leaving out millions of people. So I’m always interested to hear about what interesting projects or organizations or people are out there.\\n\\nWhat I have come to believe is that engineers, being just given a set of specs that say if you do it this way, it will meet this level of the standard… that doesn’t necessarily produce intuition. You really have to have some intuition in order to make things accessible.', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='So I’ve come to the conclusion that what we really need is to show people examples of something which is not accessible, and something that is, and let them ingest as many examples as we can give them, because their neural networks will eventually figure out, what is it about this design that makes it accessible? And how do I apply that insight into the next design that I do? So, seeing what works and what doesn’t work is really important. And you often learn a lot more from what doesn’t work than you do from what does.', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='There’s a guy named <PERSON>, who’s at the University of Maryland, he and I did a two-day event [the Future of Interface Workshop] looking at research on accessibility and trying to frame what this is going to look like over the next 10 or 20 years. It really is quite astonishing what the technology might be able to do to act as an augmenting capability for people that that need assistance. There’s great excitement, but at the same time great disappointment, because we haven’t used it as effectively as I think we could have. It’s kind of like how <PERSON> invented a telephone that can’t be used by people who are deaf, which is why he was working on it in the first place.', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='It is a funny contradiction of priorities. One thing where I do see some of the the large language and multimodal AI models helping out is that they can describe what they are seeing, even if you can’t see it. I know that one of GPT-4’s first applications was in an application for blind people to view the world around them.\\n\\nWe’re experiencing something close to that right this minute. Since I wear hearing aids, I’m making use of the captioning capability. And at the moment since this is Zoom rather than a Google Meet, there isn’t any setting on this one for closed captioning. I’m exercising the Zoom application through the Chrome browser, and Google has developed a capability for the Chrome browser to detect speech in the incoming sound.', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='So packets are coming in and they’re known to be sound, it passes through an identification system that produces a caption bar, which you can move around on the screen. And that’s been super helpful for me. For cases like this, where the application doesn’t have captioning, or for random video streaming video that might be coming in and hasn’t been captioned, the caption window automatically pops up. In theory, I think we can do this in 100 different languages, although I don’t know that we’ve activated it for more than four or five. As you say, these tools will become more and more normal, and as time goes on, people will expect the system to adapt to their needs.', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='So language translation, and speech recognition is quite powerful, but I do want to mention something that I found vaguely unsettling. Recently, I encountered an example of a conversation between a reporter and a chatbot. But he chose deliberately to take the output of the chat bot and have it spoken by the system. And he chose the style of a famous British explorer [<PERSON>].\\n\\nThe text itself was quite well formed, but coming with Attenborough’s accent just added to the weight of the assertions even when they were wrong. The confidence levels, as I’m sure you’ve seen, are very high, even when the thing doesn’t know what it’s talking about.', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='The reason I bring this up is that we are allowing in these indicators of, how should we say this, of quality, to fool us. Because in the past, they really did mean it was <PERSON>. But here it’s not, it’s just his voice. I got to thinking about this, and I realized there was an ancient example of exactly this problem that showed up 50 years ago at Xerox PARC.', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='They had a laser printer, and they had the Alto workstation, and the Bravo text editor, it meant the first draft of anything you type to be printed out beautifully formatted with lovely forms and everything else. Normally, you would never see that production quality until after everything had been edited, you know, wrestled with by everybody to get the text formatted, picture-perfect stuff. That meant the first draft stuff came out looking like it was final draft. People didn’t didn’t understand that they were nuts, that they were seeing first-round stuff, and that it wasn’t complete, or necessarily even satisfactory.\\n\\nSo it occurred to me that we’ve reached a point now where technology is fooling us into giving it more weight than it deserves, because of certain indicia that used to be indicative of the investment made in producing it. And… I’m not quite sure what to do about that.\\n\\nI don’t think anyone is!', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='I don’t think anyone is!\\n\\nI think somehow or another, we need to make it clear what the provenance is of the thing that we’re looking at. Like how we needed to say this is first-draft material, you know, don’t make any assumptions. So provenance turns out to be a very important concept, especially in a world where we have the ability to imbue content with attributes that we would normally interpret in one way. Like, it’s <PERSON> speaking, and we should listen to that. And yet, which have to be, we have to think more critically about them. Because in fact, the attribute is being delivered artificially.\\n\\nAnd perhaps maliciously.', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='And perhaps maliciously.\\n\\nCertainly that too. And this is why critical thinking has become an important skill. But it doesn’t work very well, unless you have enough information to understand the provenance of the material that you’re looking at. I think we are going to have to invest more in provenance and identity in order to evaluate the quality of that which we are experiencing.\\n\\nI wanted to ask you about interplanetary internet, because that whole area is extremely interesting to me.\\n\\nWell, this one, of course, gets started way back in 1998. But I’m a science fiction reader from way back way to age 10 or something, so I got quite excited when it was possible to even think about the possibility of designing and building a communication system that would span the solar system.', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='The team got started very small, and now 25 years later involves many of the space agencies around the world: JAXA, the Korean Space Agency, NASA and so on. And a growing team of people who are either government funded to do space-based research, or volunteers. There’s a special interest group called the interplanetary networking Special Interest Group, which is part of the Internet Society — that thing got started in 1998. But it has now grown to like 900 people around the world who are interested in this stuff.\\n\\nWe’ve standardized this stuff, we’re on version seven of it, we’re running it up in the International Space Station. It’s intended to be available for the return to the moon and Artemis missions. I’m not going to see the end result of all this, but I’m going to see the first couple of chapters. And I’m very excited about that, because it’s not crazy to actually think about. Like all my other projects, it takes a long time. Patience and persistence!', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='For something like this it must have been a real challenge, but also a very familiar one. In some ways building something like this is what you’ve been doing your whole career. This is just a different set of restraints and capabilities.\\n\\nYou put your finger on it, exactly right. This is in a different parametric space than the one that works for TCP/IP. And we’re still bumping into some really interesting problems, especially where you have TCP/IP networks running on the moon, for example, locally and interconnecting with other internets on other planets, going through the interplanetary protocol. What does that look like? You know, which IP addresses should be used? We have to figure out, well, how the hell does the Domain Name System work in the context of internets that aren’t on the planet? And it’s really fun!', metadata={'source': '/content/new_articles/05-05-vint-cerf-on-the-exhilarating-mix-of-thrill-and-hazard-at-the-frontiers-of-tech.txt'}),\n", " Document(page_content='Partners of 3one4 Capital, a venture capital firm in India, recently went on a road show to raise a new fund. Within two and a half months, at the height of the worsening global economy, they had secured $200 million. It’s the fourth marquee fund for the Bengaluru-headquartered fund, whose portfolio includes four unicorn startups.\\n\\nThe fund, sixth overall for 3one4 Capital, was oversubscribed to $250 million but the firm is accepting only $200 million to keep itself lean and disciplined, said <PERSON><PERSON><PERSON>, co-founder and partner at 3one4 Capital. The firm’s decision to limit the fund size is emblematic of its strategic choices, which have set it apart from other Indian venture firms.', metadata={'source': '/content/new_articles/05-07-3one4-capital-driven-by-contrarian-bets-raises-200-million-new-fund.txt'}),\n", " Document(page_content='“We are known to give good returns. Our performance has been benchmarked among the best leading performing funds in the space. So we asked ourselves the hard questions, can we continue our performance with a larger fund size? Do we even need that much capital for the early-stage?” said <PERSON><PERSON> in an interview with TechCrunch.\\n\\nIn recent years, a surge of venture capital firms in India have raised unprecedentedly large funds, sparking concerns about the responsible allocation of this capital, particularly for early-stage startups. Critics question whether there are enough viable companies in the Indian market to absorb and effectively utilize such significant investments.', metadata={'source': '/content/new_articles/05-07-3one4-capital-driven-by-contrarian-bets-raises-200-million-new-fund.txt'}),\n", " Document(page_content='<PERSON><PERSON>, pictured above, asserts that there is ample room for more Indian companies to pursue IPOs, as the nation’s IPO market has proven successful and well-regulated for institutional investors. He anticipates a transformation in India’s stock index, with an increasing number of tech companies, apps, services, fintech, and payment solutions becoming part of the index.\\n\\nDespite this, <PERSON><PERSON> acknowledges that the Indian market has yet to fully realize its potential for mergers and acquisitions. Although there has been growth in M&A activity—increasing three to four times in the past five years—it remains below expectations. For the Indian market to flourish, <PERSON><PERSON> emphasizes the need for a more robust M&A landscape.', metadata={'source': '/content/new_articles/05-07-3one4-capital-driven-by-contrarian-bets-raises-200-million-new-fund.txt'}),\n", " Document(page_content='Over the last half-decade, numerous Indian venture firms have shifted their attention to early-stage investments. Despite this increased focus, the market continues to depend on international investors to support mid- and growth-stage deals, highlighting the need for further growth in India’s venture capital ecosystem. “We have high performing mutual funds and PEs. We hope that more of these firms will launch dedicated funds for Indian startups,” he said.\\n\\nHalf of the capital in the new fund for 3one4 has come from Indian investors, another aspect that differentiates the firm from many of its peers. All the systemically important Indian banks, and the top five local banks by market cap overall have invested in the new fund. Eight of the top 10 mutual fund operators are also LPs in the new fund, said <PERSON><PERSON>. “We are also proud to have leading global endowments, sovereigns and insurance companies as LPs,” he said.', metadata={'source': '/content/new_articles/05-07-3one4-capital-driven-by-contrarian-bets-raises-200-million-new-fund.txt'}),\n", " Document(page_content='“We want to be India’s leading homegrown venture capital firm. We are based here, we invest here – we don’t want to invest in Southeast Asia – and our fund size and strategy are aligned with opportunities in India. As our companies have IPO-ed over the years, we have seen the importance of having India’s largest institutions working with us to help build those companies. It would be difficult if we didn’t have banks to help our companies from everything from revenue collection to payrolls. And mutual funds are buyers, book runners and market makers for IPOs and them buying the stock gives a vote of confidence to the market,” he said.', metadata={'source': '/content/new_articles/05-07-3one4-capital-driven-by-contrarian-bets-raises-200-million-new-fund.txt'}),\n", " Document(page_content='3one4, which focuses largely on early-stage and in sectors including direct-to-consumer tech, media and content, fintech, deep technology and SaaS and enterprise automation, today manages about $750 million in AUM and its portfolio includes HR platform Darwinbox, business-to-business focused neobank Open, consumer-focused neobank Jupiter, Licious, a direct-to-consumer brand that sells meat, local social networks Koo and Lokal, entertainment service Kuku FM, fintech Raise Financial, and gaming firm Loco.\\n\\n3one4 Capital has gained a reputation for its contrarian investment approach, as exemplified by its early investment in Licious. Over five years ago, the prevailing opinion held that India’s price-sensitive market would not pay a premium for online meat delivery. However, Licious has since grown into one of South Asia’s largest direct-to-consumer brands, with a presence in approximately two dozen cities across India.', metadata={'source': '/content/new_articles/05-07-3one4-capital-driven-by-contrarian-bets-raises-200-million-new-fund.txt'}),\n", " Document(page_content='Another example of 3one4’s daring investments is Darwinbox, a bet made at a time when most investors doubted the ability of Indian SaaS companies to expand internationally or garner sufficient local business subscriptions.\\n\\n3one4 Capital’s contrarian approach extends to the investments it has deliberately avoided as well. In 2021, amidst a frenzy of investment activity in the crypto space, nearly every fund in India sought opportunities and backed crypto startups. However, 3one4 Capital, after thorough evaluation of the sector, chose not to make any investments in crypto.', metadata={'source': '/content/new_articles/05-07-3one4-capital-driven-by-contrarian-bets-raises-200-million-new-fund.txt'}),\n", " Document(page_content='The firm, which employs 28 people, is also focusing on setting new standards in transparency and governance for itself. It’s the first VC to be a signatory to UN PRI, it said. “We have to report, behave, act and look a certain way. We have to look like the fiduciary of best institutions in the world, and then and only then we quality to tell our portfolio founders that this is how we want to create best in class companies with you,” said <PERSON><PERSON>.', metadata={'source': '/content/new_articles/05-07-3one4-capital-driven-by-contrarian-bets-raises-200-million-new-fund.txt'}),\n", " Document(page_content='Databricks today announced that it has acquired Okera, a data governance platform with a focus on AI. The two companies did not disclose the purchase price. According to Crunchbase, Okera previously raised just under $30 million. Investors include Felicis, Bessemer Venture Partners, Cyber Mentor Fund, ClearSky and Emergent Ventures.', metadata={'source': '/content/new_articles/05-03-databricks-acquires-ai-centric-data-governance-platform-okera.txt'}),\n", " Document(page_content='Data governance was already a hot topic, but the recent focus on AI has highlighted some of the shortcomings of the previous approach to it, Databricks notes in today’s announcement. “Historically, data governance technologies, regardless of sophistication, rely on enforcing control at some narrow waist layer and require workloads to fit into the ‘walled garden’ at this layer,” the company explains in a blog post. That approach doesn’t work anymore in the age of large language models (LLMs) because the number of assets is growing too quickly (in part because so much of it is machine-generated) and because the overall AI landscape is changing so quickly, standard access controls aren’t able to capture these changes quickly enough.\\n\\nOkera then uses an AI-powered system that can automatically discover and classify personally identifiable information, tag it and apply rules to this (with a focus on the metadata), using a no-code interface.', metadata={'source': '/content/new_articles/05-03-databricks-acquires-ai-centric-data-governance-platform-okera.txt'}),\n", " Document(page_content='As the Databricks team stressed, that’s one of the reasons the company was interested in acquiring Okera, but the other is the service’s isolation technology, which can enforce governance control on arbitrary workloads without any major overhead. This technology is still in private preview but was likely one of the major reasons Databricks acquired the company.\\n\\nDatabricks, which launched its own LLM a few weeks ago, plans to integrate Okera’s technology into its Unity Catalog, its existing governance solution of data and AI assets. The company also noted that the acquisition will enable Databricks to expose additional APIs that its own data governance partners will be able to use to provide solutions to their customers.', metadata={'source': '/content/new_articles/05-03-databricks-acquires-ai-centric-data-governance-platform-okera.txt'}),\n", " Document(page_content='With this acquisition, Databricks is also bringing Okera co-founder and CEO <PERSON><PERSON> on board. <PERSON> created the Apache Parquet data storage format and was actually briefly an engineer at Databricks between working at Cloudera and before starting Okera, where he was the founding CTO and became the CEO in February 2022.', metadata={'source': '/content/new_articles/05-03-databricks-acquires-ai-centric-data-governance-platform-okera.txt'}),\n", " Document(page_content='“As data continues to grow in volume, velocity, and variety across different applications, CIOs, CDOs, and CEOs across the board have to balance those two often conflicting initiatives – not to mention that historically, managing access policies across multiple clouds has been painful and time-consuming,” writes <PERSON> in today’s announcement. “Many organizations don’t have enough technical talent to manage access policies at scale, especially with the explosion of LLMs. What they need is a modern, AI-centric governance solution. We could not be more excited to join the Databricks team and to bring our expertise in building secure, scalable and simple governance solutions for some of the world’s most forward-thinking enterprises.”\\n\\nIf you know more about this acquisition, you can contact Frederic on Signal at (860) 208-3416 or by email (<EMAIL>). You can also reach us via SecureDrop.', metadata={'source': '/content/new_articles/05-03-databricks-acquires-ai-centric-data-governance-platform-okera.txt'}),\n", " Document(page_content='AI startup Hugging Face and ServiceNow Research, ServiceNow’s R&D division, have released StarCoder, a free alternative to code-generating AI systems along the lines of GitHub’s Copilot.\\n\\nCode-generating systems like DeepMind’s AlphaCode; Amazon’s CodeWhisperer; and OpenAI’s Codex, which powers Copilot, provide a tantalizing glimpse at what’s possible with AI within the realm of computer programming. Assuming the ethical, technical and legal issues are someday ironed out (and AI-powered coding tools don’t cause more bugs and security exploits than they solve), they could cut development costs substantially while allowing coders to focus on more creative tasks.', metadata={'source': '/content/new_articles/05-04-hugging-face-and-servicenow-release-a-free-code-generating-model.txt'}),\n", " Document(page_content='According to a study from the University of Cambridge, at least half of developers’ efforts are spent debugging and not actively programming, which costs the software industry an estimated $312 billion per year. But so far, only a handful of code-generating AI systems have been made freely available to the public — reflecting the commercial incentives of the organizations building them (see: Replit).\\n\\nStarCoder, which by contrast is licensed to allow for royalty-free use by anyone, including corporations, was trained on over 80 programming languages as well as text from GitHub repositories, including documentation and programming notebooks. StarCoder integrates with Microsoft’s Visual Studio Code code editor and, like OpenAI’s ChatGPT, can follow basic instructions (e.g., “create an app UI”) and answer questions about code.', metadata={'source': '/content/new_articles/05-04-hugging-face-and-servicenow-release-a-free-code-generating-model.txt'}),\n", " Document(page_content='Congratulations to all the @BigCodeProject contributors that worked tirelessly over the last 6+ months to bring the vision of releasing a responsibly developed 15B parameter Code LLM to fruition. We cannot thank you enough for the collaboration & contributions to the community. https://t.co/282sCRJq3k — ServiceNow Research (@ServiceNowRSRCH) May 4, 2023\\n\\n<PERSON><PERSON><PERSON><PERSON>, a machine learning engineer at Hugging Face and a co-lead on StarCoder, claims that StarCoder matches or outperforms the AI model from OpenAI that was used to power initial versions of Copilot.', metadata={'source': '/content/new_articles/05-04-hugging-face-and-servicenow-release-a-free-code-generating-model.txt'}),\n", " Document(page_content='“One thing we learned from releases such as Stable Diffusion last year is the creativity and capability of the open-source community,<PERSON> <PERSON> told TechCrunch in an email interview. “Within weeks of the release the community had built dozens of variants of the model as well as custom applications. Releasing a powerful code generation model allows anybody to fine-tune and adapt it to their own use-cases and will enable countless downstream applications.”\\n\\nBuilding a model\\n\\nStarCoder is a part of Hugging Face’s and ServiceNow’s over-600-person BigCode project, launched late last year, which aims to develop “state-of-the-art” AI systems for code in an “open and responsible” way. Hugging Face supplied an in-house compute cluster of 512 Nvidia V100 GPUs to train the StarCoder model.', metadata={'source': '/content/new_articles/05-04-hugging-face-and-servicenow-release-a-free-code-generating-model.txt'}),\n", " Document(page_content='Various BigCode working groups focus on subtopics like collecting datasets, implementing methods for training code models, developing an evaluation suite and discussing ethical best practices. For example, the Legal, Ethics and Governance working group explored questions on data licensing, attribution of generated code to original code, the redaction of personally identifiable information (PII), and the risks of outputting malicious code.\\n\\nInspired by Hugging Face’s previous efforts to open source sophisticated text-generating systems, BigCode seeks to address some of the controversies arising around the practice of AI-powered code generation. The nonprofit Software Freedom Conservancy among others has criticized GitHub and OpenAI for using public source code, not all of which is under a permissive license, to train and monetize Codex. Codex is available through OpenAI’s and Microsoft’s paid APIs, while GitHub recently began charging for access to Copilot.', metadata={'source': '/content/new_articles/05-04-hugging-face-and-servicenow-release-a-free-code-generating-model.txt'}),\n", " Document(page_content='For their parts, GitHub and OpenAI assert that Codex and Copilot — protected by the doctrine of fair use, at least in the U.S. — don’t run afoul of any licensing agreements.\\n\\n“Releasing a capable code-generating system can serve as a research platform for institutions that are interested in the topic but don’t have the necessary resources or know-how to train such models,<PERSON> <PERSON> said. “We believe that in the long run this leads to fruitful research on safety, capabilities and limits of code-generating systems.”\\n\\nUnlike Copilot, the 15-billion-parameter StarCoder was trained over the course of several days on an open source dataset called The Stack, which has over 19 million curated, permissively licensed repositories and more than six terabytes of code in over 350 programming languages. In machine learning, parameters are the parts of an AI system learned from historical training data and essentially define the skill of the system on a problem, such as generating code.', metadata={'source': '/content/new_articles/05-04-hugging-face-and-servicenow-release-a-free-code-generating-model.txt'}),\n", " Document(page_content='Because it’s permissively licensed, code from The Stack can be copied, modified and redistributed. But the BigCode project also provides a way for developers to “opt out” of The Stack, similar to efforts elsewhere to let artists remove their work from text-to-image AI training datasets.\\n\\nThe BigCode team also worked to remove PII from The Stack, such as names, usernames, email and IP addresses, and keys and passwords. They created a separate dataset of 12,000 files containing PII, which they plan to release to researchers through “gated access.”\\n\\nBeyond this, the BigCode team used Hugging Face’s malicious code detection tool to remove files from The Stack that might be considered “unsafe,” such as those with known exploits.', metadata={'source': '/content/new_articles/05-04-hugging-face-and-servicenow-release-a-free-code-generating-model.txt'}),\n", " Document(page_content='Beyond this, the BigCode team used Hugging Face’s malicious code detection tool to remove files from The Stack that might be considered “unsafe,” such as those with known exploits.\\n\\nThe privacy and security issues with generative AI systems, which for the most part are trained on relatively unfiltered data from the web, are well-established. ChatGPT once volunteered a journalist’s phone number. And GitHub has acknowledged that Copilot may generate keys, credentials and passwords seen in its training data on novel strings.\\n\\n“Code poses some of the most sensitive intellectual property for most companies,<PERSON> <PERSON> said. “In particular, sharing it outside their infrastructure poses immense challenges.”', metadata={'source': '/content/new_articles/05-04-hugging-face-and-servicenow-release-a-free-code-generating-model.txt'}),\n", " Document(page_content='“Code poses some of the most sensitive intellectual property for most companies,<PERSON> <PERSON> said. “In particular, sharing it outside their infrastructure poses immense challenges.”\\n\\nTo his point, some legal experts have argued that code-generating AI systems could put companies at risk if they were to unwittingly incorporate copyrighted or sensitive text from the tools into their production software. As <PERSON> notes in a piece on Kolide’s corporate blog, because systems like Copilot strip code of its licenses, it’s difficult to tell which code is permissible to deploy and which might have incompatible terms of use.\\n\\nIn response to the criticisms, GitHub added a toggle that lets customers prevent suggested code that matches public, potentially copyrighted content from GitHub from being shown. Amazon, following suit, has CodeWhisperer highlight and optionally filter the license associated with functions it suggests that bear a resemblance to snippets found in its training data.', metadata={'source': '/content/new_articles/05-04-hugging-face-and-servicenow-release-a-free-code-generating-model.txt'}),\n", " Document(page_content='Commercial drivers\\n\\nSo what does ServiceNow, a company that deals mostly in enterprise automation software, get out of this? A “strong-performing model and a responsible AI model license that permits commercial use,” said <PERSON><PERSON>, the lead of the Large Language Model Lab at ServiceNow Research and the co-lead of the BigCode project.\\n\\nOne imagines that ServiceNow will eventually build StarCoder into its commercial products. The company wouldn’t reveal how much, in dollars, it’s invested in the BigCode project, save that the amount of donated compute was “substantial.”', metadata={'source': '/content/new_articles/05-04-hugging-face-and-servicenow-release-a-free-code-generating-model.txt'}),\n", " Document(page_content='“The Large Language Models Lab at ServiceNow Research is building up expertise on the responsible development of generative AI models to ensure the safe and ethical deployment of these powerful models for our customers,<PERSON> <PERSON> said. “The open-scientific research approach to BigCode provides ServiceNow developers and customers with full transparency into how everything was developed and demonstrates ServiceNow’s commitment to making socially responsible contributions to the community.”\\n\\nStarCoder isn’t open source in the strictest sense. Rather, it’s being released under a licensing scheme, OpenRAIL-M, that includes “legally enforceable” use case restrictions that derivatives of the model — and apps using the model — are required to comply with.', metadata={'source': '/content/new_articles/05-04-hugging-face-and-servicenow-release-a-free-code-generating-model.txt'}),\n", " Document(page_content='For example, StarCoder users must agree not to leverage the model to generate or distribute malicious code. While real-world examples are few and far between (at least for now), researchers have demonstrated how AI like StarCoder could be used in malware to evade basic forms of detection.\\n\\nWhether developers actually respect the terms of the license remains to be seen. Legal threats aside, there’s nothing at the base technical level to prevent them from disregarding the terms to their own ends.\\n\\nThat’s what happened with the aforementioned Stable Diffusion, whose similarly restrictive license was ignored by developers who used the generative AI model to create pictures of celebrity deepfakes.\\n\\nBut the possibility hasn’t discouraged <PERSON>, who feels the downsides of not releasing StarCoder aren’t outweighed by the upsides.', metadata={'source': '/content/new_articles/05-04-hugging-face-and-servicenow-release-a-free-code-generating-model.txt'}),\n", " Document(page_content='But the possibility hasn’t discouraged <PERSON>, who feels the downsides of not releasing StarCoder aren’t outweighed by the upsides.\\n\\n“At launch, StarCoder will not ship as many features as GitHub Copilot, but with its open-source nature, the community can help improve it along the way as well as integrate custom models,” he said.\\n\\nThe StarCoder code repositories, model training framework, dataset-filtering methods, code evaluation suite and research analysis notebooks are available on GitHub as of this week. The BigCode project will maintain them going forward as the groups look to develop more capable code-generating models, fueled by input from the community.\\n\\nThere’s certainly work to be done. In the technical paper accompanying StarCoder’s release, Hugging Face and ServiceNow say that the model may produce inaccurate, offensive, and misleading content as well as PII and malicious code that managed to make it past the dataset filtering stage.', metadata={'source': '/content/new_articles/05-04-hugging-face-and-servicenow-release-a-free-code-generating-model.txt'}),\n", " Document(page_content=\"Microsoft doubles down on AI with new Bing features The company's betting the farm on generative AI\\n\\nMicrosoft is embarking on the next phase of Bing’s expansion. And — no surprise — it heavily revolves around AI.\\n\\nAt a preview event this week in New York City, Microsoft execs including <PERSON>, the CVP and consumer chief marketing officer, gave members of the press, including this reporter, a look at the range of features heading to Bing over the next few days, weeks and months.\\n\\nThey don’t so much reinvent the wheel as they build on what Microsoft has injected into the Bing experience over the past three months or so. Since launching Bing Chat, its AI-powered chatbot powered by OpenAI’s GPT-4 and DALL-E 2 models, Microsoft says that visitors to Bing — which has grown to exceed 100 million daily active users — have engaged in over half a billion chats and created more than 200 million images.\", metadata={'source': '/content/new_articles/05-04-microsoft-doubles-down-on-ai-with-new-bing-features.txt'}),\n", " Document(page_content=\"Looking ahead, <PERSON> will become more visual, thanks to more image- and graphic-centric answers in Bing Chat. It’ll also become more personalized, with capabilities that’ll allow users to export their Bing Chat histories and draw in content from third-party plugins (more on those later). And it’ll embrace multimodality, at least in the sense that <PERSON> will be able to answer questions within the context of images.\\n\\n“I think it’s safe to say that we’re underway with the transformation of search,<PERSON> <PERSON><PERSON> said in prepared remarks. “In our minds, we think that today will be the start of the next generation of this ‘search mission.'”\\n\\nOpen, and visual\\n\\nAs of today, the new Bing — the one with Bing Chat — is now available waitlist-free. Anyone can try it out by signing in with a Microsoft Account.\", metadata={'source': '/content/new_articles/05-04-microsoft-doubles-down-on-ai-with-new-bing-features.txt'}),\n", " Document(page_content='Open, and visual\\n\\nAs of today, the new Bing — the one with <PERSON> — is now available waitlist-free. Anyone can try it out by signing in with a Microsoft Account.\\n\\nIt’s more or less the experience that launched several months ago. But as alluded to earlier, <PERSON> will soon respond with images — at least where it makes sense. Answers to questions (e.g. “Where is Machu Picchu?”) will be accompanied by relevant images if any exist, much like the standard Bing search flow but condensed into a card-like interface.\\n\\nIn a demo at the event, a spokesperson typed the question “Does the saguaro cactus grow flowers?” and <PERSON> pulled up a paragraph-long response alongside an image of the cactus in question. For me, it evoked the “knowledge panels” in Google Search.\\n\\nMicrosoft isn’t saying which categories of content, exactly, might trigger an image. But it does have filtering in place to prevent explicit images from appearing — or so it claims.', metadata={'source': '/content/new_articles/05-04-microsoft-doubles-down-on-ai-with-new-bing-features.txt'}),\n", " Document(page_content='Microsoft isn’t saying which categories of content, exactly, might trigger an image. But it does have filtering in place to prevent explicit images from appearing — or so it claims.\\n\\<PERSON><PERSON><PERSON><PERSON>, the head of responsible AI at Microsoft, told me that <PERSON>t benefits from the filtering and moderation already in place with Bing search. Beyond this, <PERSON>t uses a combination of “toxicity classifiers,” or AI models trained to detect potentially harmful prompts, and blacklists to keep the chat relatively clean.\\n\\nThose measures didn’t prevent <PERSON> Chat from going off the rails when it first rolled out in preview in early February, it’s worth noting. Our coverage found the chatbot spouting vaccine misinformation and writing a hateful screed from the perspective of <PERSON>. Other reporters got it to make threats, claim multiple identities and even shame them for admonishing it.', metadata={'source': '/content/new_articles/05-04-microsoft-doubles-down-on-ai-with-new-bing-features.txt'}),\n", " Document(page_content='In another knock against Microsoft, the company just a few months ago laid off the ethics and society team within its larger AI organization. The move left Microsoft without a dedicated team to ensure its AI principles are closely tied to product design.\\n\\nBird, though, asserts that meaningful progress has been made and that these sorts of AI issues aren’t solved overnight — public though <PERSON> may be. Among other measures, a team of human moderators is in place to watch for abuse, she said, such as users attempting to use Bing Chat to generate phishing emails.\\n\\nBut — as members of the press weren’t given the chance to interact with the latest version of Bing beyond curated demos — I can’t say to what extent all that’s made a difference. It’ll doubtless become clear once more folks get their hands on it.', metadata={'source': '/content/new_articles/05-04-microsoft-doubles-down-on-ai-with-new-bing-features.txt'}),\n", " Document(page_content='One aspect of <PERSON>t that is improving is the transparency around its responses — specifically responses of a fact-based nature. Soon, when asked to summarize a document or about the contents a document (e.g. “what does this page say about the Brooklyn Bridge?”), whether a 20-page PDF or a Wikipedia article, <PERSON> will include citations indicating from where in the text the information came from. Clicking on them will highlight the corresponding passage.\\n\\nProductivity emergent\\n\\nIn another new feature on the visual front, <PERSON> will be able to create charts and graphs when fed the right prompt and data. Previously, asking something like “Which are the most populous cities in Brazil?” would yield a basic list of results. But in a near-future preview, <PERSON> will present those results visually and in the chart type of a user’s choosing.', metadata={'source': '/content/new_articles/05-04-microsoft-doubles-down-on-ai-with-new-bing-features.txt'}),\n", " Document(page_content='This seemingly represents a step for Bing toward a full-blown productivity platform, particularly when paired with the enhanced text-to-image generation capabilities coming down the pipeline.\\n\\nIn the coming weeks, Bing Image Creator — Microsoft’s tool that can generate images from text prompts, powered by DALL-E 2 — will understand more languages aside from English (over 100 total). As with English, users will be able to refine the images they generate with follow-up prompts (e.g. “Make an image of a bunny rabbit,” followed by “now make the fur pink”).\\n\\nGenerative art AI has been in the headlines a lot, lately — and not for the most optimistic of reasons necessarily.', metadata={'source': '/content/new_articles/05-04-microsoft-doubles-down-on-ai-with-new-bing-features.txt'}),\n", " Document(page_content='Generative art AI has been in the headlines a lot, lately — and not for the most optimistic of reasons necessarily.\\n\\nPlaintiffs have brought several lawsuits against OpenAI and its rival vendors, alleging that copyrighted data — mostly art — was used without their permission to train generative models like DALL-E 2. Generative models “learn” to create art and more by “training” on sample images and text, usually scraped indiscriminately from the public web.\\n\\nI asked <PERSON> about whether Microsoft is exploring ways to compensate creators whose work was swept up in training data, even if the company’s official position is that it’s a matter of fair use. Several platforms launching generative AI tools, including Shutterstock, have kick-started creators funds along these lines. Others, like Spawning, are creating mechanisms to let artists opt out of AI model training altogether.', metadata={'source': '/content/new_articles/05-04-microsoft-doubles-down-on-ai-with-new-bing-features.txt'}),\n", " Document(page_content='<PERSON> implied that these issues will eventually have to be confronted — and that content creators deserve some form of recompense. But she wasn’t willing to commit to anything concrete this week.\\n\\nMultimodal search\\n\\nElsewhere on the image front, <PERSON> is gaining the ability to understand images as well as text. Users will be able to upload images and search the web for related content, for example copying a link to an image of a crocheted octopus and asking <PERSON> the question “how do I make that?” to get step-by-step instructions.\\n\\nMultimodality powers the new page context function in the Edge app for mobile, as well. Users will be able to ask questions in <PERSON> related to the mobile page they’re viewing.', metadata={'source': '/content/new_articles/05-04-microsoft-doubles-down-on-ai-with-new-bing-features.txt'}),\n", " Document(page_content='Multimodality powers the new page context function in the Edge app for mobile, as well. Users will be able to ask questions in Bing Chat related to the mobile page they’re viewing.\\n\\nMicrosoft wouldn’t say either way, but it seems likely that these new multimodal abilities stem from GPT-4, which can understand images in addition to text. When OpenAI announced GPT-4, it didn’t make the model’s image understanding capabilities available to all customers — and still hasn’t. I’d wager that Microsoft, though, being a major investor in and close collaborator with OpenAI, has some sort of privileged access.\\n\\nAny image upload tool can be abused, of course, which is why Microsoft is employing automated filtering and hashing to block illicit uploads, according to <PERSON>. The jury’s out on how well these work, though — we weren’t given the chance to test image uploads ourselves.\\n\\nNew chat features\\n\\nMultimodality and new visual features aren’t all that’s coming to Bing Chat.', metadata={'source': '/content/new_articles/05-04-microsoft-doubles-down-on-ai-with-new-bing-features.txt'}),\n", " Document(page_content='New chat features\\n\\nMultimodality and new visual features aren’t all that’s coming to Bing Chat.\\n\\nSoon, Bing Chat will store users’ chat histories, letting them pick up where they left off and return to previous chats when they wish. It’s an experience akin to the chat history feature OpenAI recently brought to ChatGPT, showing a list of chats and the bot’s responses to each of those chats.\\n\\nThe specifics of the chat history feature have yet to be ironed out, like how long chats will be stored, exactly. But users will be able to delete their history at any time regardless, Microsoft says — addressing the criticisms several European Union governments had against ChatGPT.', metadata={'source': '/content/new_articles/05-04-microsoft-doubles-down-on-ai-with-new-bing-features.txt'}),\n", " Document(page_content='Bing Chat will also gain export and share functionalities, letting users share conversations on social media or to a Word document. <PERSON><PERSON>, a partner GM in Microsoft’s web experiences team, told TechCrunch that a more robust copy-and-paste system is in the works — but not in preview just yet — for graphs and images created through Bing Chat.\\n\\nPerhaps the most transformative addition to Bing Chat, though, is plugins. From partners like OpenTable and Wolfram Alpha, plugins greatly extend what Bing Chat can do, for example helping users book a reservation or create visualizations and get answers to challenging science and math questions.\\n\\nLike chat history, the not-yet-live plugins functionality is in the very preliminary stages. There’s no plugins marketplace to speak of; plugins can be toggled on or off from the Bing Chat web interface.', metadata={'source': '/content/new_articles/05-04-microsoft-doubles-down-on-ai-with-new-bing-features.txt'}),\n", " Document(page_content='<PERSON> hinted, but wouldn’t confirm, that the Bing Chat plugins scheme was associated with — or perhaps identical to — OpenAI’s recently introduced plugins for ChatGPT. That’d certainly make sense, given the similarities between the two.\\n\\nEdge, refreshed\\n\\nBing Chat is available through Edge as well as the web, of course. And Edge is getting a fresh coat of paint alongside Bing Chat.\\n\\nFirst previewed in February, the new and improved Edge features rounded corners in line with Microsoft’s Windows 11 design philosophy. Elements in the browser are now more “containerized,” as one Microsoft spokesperson put it, and there’s subtle tweaks throughout, like the Microsoft Account image moving left-of-center.', metadata={'source': '/content/new_articles/05-04-microsoft-doubles-down-on-ai-with-new-bing-features.txt'}),\n", " Document(page_content=\"In Compose, Edge’s Bing Chat-powered tool that can write emails and more given a basic prompt (e.g. “write an invitation to my dog’s birthday party”), a new option lets users adjust the length, phrasing and tone of the generated text to nearly anything they’d like. Type in the desired tone, and <PERSON> Chat will write a message to match <PERSON> <PERSON> says filters are in place to prevent the use of clearly problematic tones, like “hateful” or “racist.”\\n\\nFar more intriguing than Compose, though — at least to me — are actions in Edge, which translate certain Bing Chat prompts into automations.\\n\\nTyping a command like “bring my passwords from another browser” in <PERSON> Chat in the Edge sidebar opens Edge’s browsing data settings page, while the prompt “play ‘The Devil Wears Prada'” pulls up a list of streaming options including Vudu and (predictably) the Microsoft Store. There’s even an action that automatically organizes — and color-coordinates — browsing tabs.\", metadata={'source': '/content/new_articles/05-04-microsoft-doubles-down-on-ai-with-new-bing-features.txt'}),\n", " Document(page_content='Actions are in a primitive stage at present. But it’s clear where Microsoft’s going, here. One imagines actions eventually expanding beyond Edge to reach other Microsoft products, like Office 365, and perhaps one day the whole Windows desktop.\\n\\nSaunders wouldn’t confirm or deny that this is the endgame. “Stay tuned for Microsoft Build,” she told me, referring to Microsoft’s upcoming developer conference. We shall.', metadata={'source': '/content/new_articles/05-04-microsoft-doubles-down-on-ai-with-new-bing-features.txt'}),\n", " Document(page_content=\"Generative AI is pretty impressive in terms of its fidelity these days, as viral memes like <PERSON><PERSON><PERSON><PERSON> would suggest. The latest systems can conjure up scenescapes from city skylines to cafes, creating images that appear startlingly realistic — at least on first glance.\\n\\nBut one of the longstanding weaknesses of text-to-image AI models is, ironically, text. Even the best models struggle to generate images with legible logos, much less text, calligraphy or fonts.\\n\\nBut that might change.\\n\\nLast week, DeepFloyd, a research group backed by Stability AI, unveiled DeepFloyd IF, a text-to-image model that can “smartly” integrate text into images. Trained on a dataset of more than a billion images and text, DeepFloyd IF, which requires a GPU with at least 16GB of RAM to run, can create an image from a prompt like “a teddy bear wearing a shirt that reads ‘Deep Floyd'” — optionally in a range of styles.\", metadata={'source': '/content/new_articles/05-05-with-deepfloyd-generative-ai-art-gets-a-text-upgrade.txt'}),\n", " Document(page_content='DeepFloyd IF is available in open source, licensed in a way that prohibits commercial use — for now. The restriction was likely motivated by the current tenuous legal status of generative AI art models. Several commercial model vendors are under fire from artists who allege the vendors are profiting from their work without compensating them by scraping that work from the web without permission.\\n\\nBut NightCafe, the generative art platform, was granted early access to DeepFloyd IF.\\n\\nNightCafe CEO <PERSON> spoke to TechCrunch about what makes DeepFloyd IF different from other text-to-image models and why it might represent a significant step forward for generative AI.\\n\\nAccording to <PERSON>, DeepFloyd IF’s design was heavily inspired by Google’s Imagen model, which was never released publicly. In contrast to models like OpenAI’s DALL-E 2 and Stable Diffusion, DeepFloyd IF uses multiple different processes stacked together in a modular architecture to generate images.', metadata={'source': '/content/new_articles/05-05-with-deepfloyd-generative-ai-art-gets-a-text-upgrade.txt'}),\n", " Document(page_content='With a typical diffusion model, the model learns how to gradually subtract noise from a starting image made almost entirely of noise, moving it closer step by step to the target prompt. DeepFloyd IF performs diffusion not once but several times, generating a 64x64px image then upscaling the image to 256x256px and finally to 1024x1024px.\\n\\nWhy the need for multiple diffusion steps? DeepFloyd IF works directly with pixels, <PERSON> explained. Diffusion models are for the most part latent diffusion models, which essentially means they work in a lower-dimensional space that represents a lot more pixels but in a less accurate way.', metadata={'source': '/content/new_articles/05-05-with-deepfloyd-generative-ai-art-gets-a-text-upgrade.txt'}),\n", " Document(page_content='The other key difference between DeepFloyd IF and models such as Stable Diffusion and DALL-E 2 is that the former uses a large language model to understand and represent prompts as a vector, a basic data structure. Due to the size of the large language model embedded in DeepFloyd IF’s architecture, the model is particularly good at understanding complex prompts and even spatial relationships described in prompts (e.g. “a red cube on top of a pink sphere”).\\n\\n“It’s also very good at generating legible and correctly spelled text in images, and can even understand prompts in multiple languages,<PERSON> <PERSON> added. “Of these capabilities, the ability to generate legible text in images is perhaps the biggest breakthrough to make DeepFloyd IF stand out from other algorithms.”', metadata={'source': '/content/new_articles/05-05-with-deepfloyd-generative-ai-art-gets-a-text-upgrade.txt'}),\n", " Document(page_content='Because DeepFloyd IF can pretty capably generate text in images, <PERSON> expects it to unlock a wave of new generative art possibilities — think logo design, web design, posters, billboards and even memes. The model should also be much better at generating things like hands, he says, and — because it can understand prompts in other languages — it might be able to create text in those languages, too.\\n\\n“NightCafe users are excited about DeepFloyd IF largely because of the possibilities that are unlocked by generating text in images,<PERSON> <PERSON> said. “Stable Diffusion XL was the first open source algorithm to make headway on generating text — it can accurately generate one or two words some of the time — but it’s still not good enough at it for use cases where text is important.”', metadata={'source': '/content/new_articles/05-05-with-deepfloyd-generative-ai-art-gets-a-text-upgrade.txt'}),\n", " Document(page_content='That’s not to suggest DeepFloyd IF is the holy grail of text-to-image models. <PERSON> notes that the base model doesn’t generate images that are quite as aesthetically pleasing as some diffusion models, although he expects fine-tuning will improve that.\\n\\nBut the bigger question, to me, is to what degree DeepFloyd IF suffers from the same flaws as its generative AI brethren.\\n\\nA growing body of research has turned up racial, ethnic, gender and other forms of stereotyping in image-generating AI, including Stable Diffusion. Just this month, researchers at AI startup Hugging Face and Leipzig University published a tool demonstrating that models including Stable Diffusion and OpenAI’s DALL-E 2 tend to produce images of people that look white and male, especially when asked to depict people in positions of authority.\\n\\nThe DeepFloyd team, to their credit, note the potential for biases in the fine print accompanying DeepFloyd IF:', metadata={'source': '/content/new_articles/05-05-with-deepfloyd-generative-ai-art-gets-a-text-upgrade.txt'}),\n", " Document(page_content='The DeepFloyd team, to their credit, note the potential for biases in the fine print accompanying DeepFloyd IF:\\n\\nTexts and images from communities and cultures that use other languages are likely to be insufficiently accounted for. This affects the overall output of the model, as white and western cultures are often set as the default.\\n\\nAside from this, DeepFloyd IF, like other open source generative models, could be used for harm, like generating pornographic celebrity deepfakes and graphic depictions of violence. On the official webpage for DeepFloyd IF, the DeepFloyd team says that they used “custom filters” to remove watermarked, “NSFW” and “other inappropriate content” from the training data.\\n\\nBut it’s unclear exactly which content was removed — and how much might’ve been missed. Ultimately, time will tell.', metadata={'source': '/content/new_articles/05-05-with-deepfloyd-generative-ai-art-gets-a-text-upgrade.txt'}),\n", " Document(page_content='Welcome to The Interchange! If you received this in your inbox, thank you for signing up and your vote of confidence. If you’re reading this as a post on our site, sign up here so you can receive it directly in the future. Every week, we’ll take a look at the hottest fintech news of the previous week. This will include everything from funding rounds to trends to an analysis of a particular space to hot takes on a particular company or phenomenon. There’s a lot of fintech news out there and it’s our job to stay on top of it — and make sense of it — so you can stay in the know. — <PERSON> and <PERSON>\\n\\nBusy, busy, busy\\n\\nIt was a busy week in startup and venture lands, and the fintech space was no exception.', metadata={'source': '/content/new_articles/05-07-fintech-space-continues-to-be-competitive-and-drama-filled.txt'}),\n", " Document(page_content='Busy, busy, busy\\n\\nIt was a busy week in startup and venture lands, and the fintech space was no exception.\\n\\nIn the venture world, I reported on <PERSON>’s departure from Fin Capital earlier this year and the fact that he has since started a new venture firm called Audere Capital. The circumstances around his departure remain fuzzy, but one source speculated that tension arose between <PERSON><PERSON><PERSON> and Fin founding partner <PERSON> over some of the goings-on at alternative financing startup Pipe last year. More details here.', metadata={'source': '/content/new_articles/05-07-fintech-space-continues-to-be-competitive-and-drama-filled.txt'}),\n", " Document(page_content='We also wrote about Tellus, a startup that raised $16 million in an <PERSON><PERSON><PERSON>led seed round of funding last year that is now being scrutinized by the U.S. government. When I interviewed the company’s co-founder, <PERSON>, last year, I admit I was a little bit skeptical of any company that would bet on people agreeing to high-interest mortgage rates to upgrade their homes (think 9%!) and using customer savings deposits to fund such loans. When I asked <PERSON> if this was risky, he admitted it was but insisted that Tellus utilized “very strict underwriting criteria” and had not yet seen any defaults “because the majority of its borrowers go on to soon refinance their loans at more favorable terms.” Well, last week U.S. Senator <PERSON><PERSON><PERSON>, chairman of the U.S. Senate Committee on Banking, Housing, and Urban Affairs, wrote a letter to FDIC chairman <PERSON> expressing concerns about <PERSON><PERSON>’s claims. In that letter, <PERSON> pressed the FDIC to review Tellus’s business', metadata={'source': '/content/new_articles/05-07-fintech-space-continues-to-be-competitive-and-drama-filled.txt'}),\n", " Document(page_content='on Banking, Housing, and Urban Affairs, wrote a letter to FDIC chairman <PERSON> expressing concerns about <PERSON><PERSON>’s claims. In that letter, <PERSON> pressed the FDIC to review Tellus’s business practices “to ensure that customers are protected from financial fraud and abuse.” In a twist, I discovered that <PERSON> was married to a16z general partner <PERSON> (not sure if he still is). Neither he nor the venture firm commented on the senator’s concerns but Tellus CEO/CTO <PERSON><PERSON> did provide me with a statement via email. Read more here.', metadata={'source': '/content/new_articles/05-07-fintech-space-continues-to-be-competitive-and-drama-filled.txt'}),\n", " Document(page_content='Infrastructure continues to be resilient, even in a downturn. This week alone, I wrote about two payments infrastructure companies making moves, and my colleague <PERSON> wrote about Stripe’s latest customer win. For starters, I covered Finix officially becoming a payments processor — a natural evolution really for a company that has slowly been expanding its offerings. In case you forgot, Finix is a startup that Sequoia backtracked on investing in after Stripe (an existing portfolio company) expressed concerns about being too competitive. (Finix got to keep its $21 million, though!) Now that it directly connects to all major U.S. card networks — American Express, Discover, Mastercard and Visa — and no longer relies on a third-party processor, Finix says it’s able to offer businesses “instant onboarding, improved economics and opportunities for lowering interchange fees.” I talked with CEO and co-founder <PERSON> all about it, and why he thinks what Finix has built is', metadata={'source': '/content/new_articles/05-07-fintech-space-continues-to-be-competitive-and-drama-filled.txt'}),\n", " Document(page_content='“instant onboarding, improved economics and opportunities for lowering interchange fees.” I talked with CEO and co-founder <PERSON> all about it, and why he thinks what Finix has built is different from what legacy players and Stripe have on the market. I also wrote about Liquido, a Mountain View, California–based startup aiming to be the “Stripe of Latin America,” and more. Index Ventures’ <PERSON> led two funding rounds totaling $26 million into the company in 2021. Interestingly, prior to joining Index, <PERSON><PERSON><PERSON> helped build and lead business strategy and finance at Stripe from 2015 to 2019. And <PERSON> wrote about Stripe landing Uber as a customer, which was a bit unexpected considering that rival Lyft has been a longtime marquee customer of the company.', metadata={'source': '/content/new_articles/05-07-fintech-space-continues-to-be-competitive-and-drama-filled.txt'}),\n", " Document(page_content='And, last but not least, corporate card and spend management startup Brex announced last week a global expansion of its Empower product into new markets so that companies that are its customers now “can spend globally and operate locally” in countries such as Brazil, Canada, Israel, Japan, Mexico, Singapore, South Africa, and the Philippines, as well as in 36 European countries. In an interview with TechCrunch, Brex co-founder and co-CEO <PERSON><PERSON> said that the company believes the move “will really open up TAM” for Brex since so many existing and prospective U.S. clients “have some sort of global operations.”', metadata={'source': '/content/new_articles/05-07-fintech-space-continues-to-be-competitive-and-drama-filled.txt'}),\n", " Document(page_content='“One of the big problems that companies have when they operate globally is that they actually need to open up an account in all these different countries where they might have employees. It becomes really complicated to set up all your financial systems on a country by country basis,” he added. “Now, if you use Brex, you can actually operate as if you were a local company with a local card.”\\n\\nIn other words, companies using Brex that have employees who work in other countries are giving those workers the ability to use a corporate card freely in their home countries, while also giving the company the ability to pay the statements in local currency from the local bank.\\n\\n“It’s something that we’ve been trying to do for a while,<PERSON> <PERSON><PERSON><PERSON> added, noting that insurtech Lemonade is a customer. — <PERSON>\\n\\nOther weekly news', metadata={'source': '/content/new_articles/05-07-fintech-space-continues-to-be-competitive-and-drama-filled.txt'}),\n", " Document(page_content='“It’s something that we’ve been trying to do for a while,<PERSON> <PERSON><PERSON><PERSON> added, noting that insurtech Lemonade is a customer. — <PERSON>\\n\\nOther weekly news\\n\\nChristine, <PERSON> and <PERSON> teamed up to write about the collapse of First Republic Bank, speaking with tech founders and investors who had money in the bank about what happens next. We also spoke with an FRB competitor about what all of these startup bank collapses mean for business. More here.\\n\\nReports Carly Page: “Hackers have published a trove of sensitive data stolen from payment software company AvidXchange after the company fell victim to ransomware for the second time this year. AvidXchange provides cloud-based software that helps organizations automate invoice processing and payment management processes. A ransomware group called RansomHouse claimed responsibility for the recent cyberattack on AvidXchange.” More here.', metadata={'source': '/content/new_articles/05-07-fintech-space-continues-to-be-competitive-and-drama-filled.txt'}),\n", " Document(page_content='<PERSON> wrote about the launch of former Bolt CEO <PERSON>’s new company, Love, which is a wellness marketplace that features an initial 200 curated products, like supplements, health testing kits and essential oils, among such categories as reducing stress and gut health. All of the products on the site pass a set of compliance processes and reviews developed in partnership with clinical trials company Radicle Science, which <PERSON><PERSON><PERSON> said is unique to the company. More here.\\n\\nBritish neobank Revolut launched in Brazil, its first country in Latin America, offering customers a global bank account and crypto investments, Silicon Republic reported. The company already had a presence in the country after hiring <PERSON><PERSON><PERSON> as the CEO of its Brazil business in March 2022. <PERSON> and <PERSON> reported in April that Revolut “saw its valuation decline by some 46% in the eyes of one of its backers.” More here.', metadata={'source': '/content/new_articles/05-07-fintech-space-continues-to-be-competitive-and-drama-filled.txt'}),\n", " Document(page_content='<PERSON><PERSON>afor reported on Fingo, a YC-backed Kenyan fintech, which launched a neobank — the first of its kind in the East African country, according to the company — in collaboration with Pan-African financial institution Ecobank Kenya. “It’s taken a while for Fingo to get here since CEO <PERSON><PERSON><PERSON> and his co-founders <PERSON>, <PERSON> and <PERSON><PERSON><PERSON> founded the Kenyan outfit in January 2021 to provide financial services that appeal to a fast-growing African youthful population that happens to be the youngest globally but the most financially marginalized. After a $200,000 pre-seed round, Fingo got into YC S21 and raised $4 million in seed funding toward the end of that year.” More here.', metadata={'source': '/content/new_articles/05-07-fintech-space-continues-to-be-competitive-and-drama-filled.txt'}),\n", " Document(page_content='<PERSON><PERSON> reported that Paytm, India’s leading mobile payments firm, reported a 13.2% surge in revenue to $285.7 million in the quarter ending March and pared its loss by 57% to $20.5 million “in a sharp turnaround for the company that is increasingly trying to become profitable following a tremulous year and a half after its public debut.” More here.\\n\\nMore headlines\\n\\nApple and fintechs like Robinhood chase yield-hungry depositors as Fed rate hikes continue. Similarly, Arta Finance, a company providing access to alternative assets, debuted the Harvest Treasuries AI-Managed Portfolio, which offers a 4.62% APY (annual percentage yield), and Wealthfront’s cash account now offers 4.55% for all clients and 5.05% APY for clients who refer a friend.\\n\\nFintech projected to become a $1.5 trillion industry by 2030, according to a new report from Boston Consulting Group and QED Investors\\n\\nOpendoor tech earnings beat by $0.77, revenue topped estimates', metadata={'source': '/content/new_articles/05-07-fintech-space-continues-to-be-competitive-and-drama-filled.txt'}),\n", " Document(page_content='Fintech projected to become a $1.5 trillion industry by 2030, according to a new report from Boston Consulting Group and QED Investors\\n\\nOpendoor tech earnings beat by $0.77, revenue topped estimates\\n\\nEveree joins Visa’s Fintech Fast Track Program with launch of Everee Visa® pay card\\n\\nFunding and M&A\\n\\nSeen on TechCrunch\\n\\nAfrican payment service provider Nomba raises $30M, backed by Base10 Partners and Shopify\\n\\nBend is taking on Brex and Ramp with a green twist and a $2.5M seed round\\n\\nAnd elsewhere', metadata={'source': '/content/new_articles/05-07-fintech-space-continues-to-be-competitive-and-drama-filled.txt'}),\n", " Document(page_content='African payment service provider Nomba raises $30M, backed by Base10 Partners and Shopify\\n\\nBend is taking on Brex and Ramp with a green twist and a $2.5M seed round\\n\\nAnd elsewhere\\n\\nDigital wallet for insurance Marble bags $4.2M. Speaking about the raise to TechCrunch, CEO <PERSON> said via email, “American households are under a lot of financial strain right now, and insurance expenses are no small part of that. We will continue to put out features that make it easier to not only save money and maximize value, but also to reduce the mental load of managing multiple insurance policies. We expect to see the insurance industry in general adopt more of the consumer friendly features that we’ve helped pioneer.”\\n\\nInsurtech startup Novidea raises $50 million Series C\\n\\nExclusive: Former Venmo COO raises $20M for Vera Equity\\n\\nTarabut Gateway raises $32 million to expand Saudi open banking\\n\\nMusic financing startup Duetti raises $32 million to buy old songs', metadata={'source': '/content/new_articles/05-07-fintech-space-continues-to-be-competitive-and-drama-filled.txt'}),\n", " Document(page_content='Exclusive: Former Venmo COO raises $20M for Vera Equity\\n\\nTarabut Gateway raises $32 million to expand Saudi open banking\\n\\nMusic financing startup <PERSON>tti raises $32 million to buy old songs\\n\\nBilling platform Inbox Health raises $22.5M and more digital health fundings\\n\\nGoogle’s VC firm just led a $12 million Series A investment in Range, a startup that’s training AI to give financial advice\\n\\nOpenEnvoy raises $15 million to grow AP automation solution\\n\\nMiami-based startup Kid<PERSON> Kredit raises $1.4M with support from <PERSON><PERSON><PERSON> and <PERSON>\\n\\nBlack-owned tech firm Greenwood acquires digital banking rival. TechCrunch covered Greenwood’s last raise in March of 2021 here.', metadata={'source': '/content/new_articles/05-07-fintech-space-continues-to-be-competitive-and-drama-filled.txt'}),\n", " Document(page_content='Black-owned tech firm Greenwood acquires digital banking rival. TechCrunch covered Greenwood’s last raise in March of 2021 here.\\n\\nJoin us at TechCrunch Disrupt 2023 in San Francisco this September as we explore the impact of fintech on our world today. New this year, we will have a whole day dedicated to all things fintech featuring some of today’s leading fintech figures. Save up to $800 when you buy your pass now through May 15, and save 15% on top of that with promo code INTERCHANGE. Learn more.\\n\\nWe are done for this week and it’s a good thing because we are also TIRED! See you next week — same time, same place. Until then, take good care! xox<PERSON>, <PERSON> and <PERSON>', metadata={'source': '/content/new_articles/05-07-fintech-space-continues-to-be-competitive-and-drama-filled.txt'}),\n", " Document(page_content='SpaceX’s super-heavy launch system Starship is poised to fundamentally reshape the space economy. The 394-foot-tall vehicle, which took to the skies for the first time last month, is designed to carry a staggering amount of mass to low Earth orbit and into deep space.\\n\\nTechCrunch+ spoke with three pure-play space VCs — Space Capital founder and managing partner <PERSON>, Space.VC founder and general partner <PERSON> and E2MC Ventures founder <PERSON> — to learn more about how they advise founders to think through Starship’s super-heavy implications.\\n\\nWhile the trio diverges on many fine points, they all agreed that founders should be thinking now about how Starship could affect their operations, for better or worse.\\n\\n“Starship has such high importance to the space sector that probably almost everyone who has a space company has to war game what that means for their business,” <PERSON><PERSON><PERSON> said.\\n\\nChanging the face of launch …', metadata={'source': '/content/new_articles/05-07-spacex-starship-startups-future.txt'}),\n", " Document(page_content='Changing the face of launch …\\n\\nThe most obvious way in which Starship is likely to revolutionize the industry is by continuing the trend SpaceX firmly established with the debut of Falcon 9: further lowering the cost of launching mass to space. Starship will be capable of carrying 100 to 150 tons of stuff to orbit, a paradigm-shifting quantity that far outstrips the payload capacity of any rocket that humans have ever designed.', metadata={'source': '/content/new_articles/05-07-spacex-starship-startups-future.txt'}),\n", " Document(page_content='In the must-watch final season of “Succession,” <PERSON> enters a conference room with his siblings. As the scene opens, he takes a seat and declares: “Who will be the successor? Me.”\\n\\nOf course, that scene didn’t appear on HBO’s hit show, but it’s a good illustration of generative AI’s level of sophistication compared to the real thing. Yet as the Writers Guild of America goes on strike in pursuit of livable working conditions and better streaming residuals, the networks won’t budge on writers’ demands to regulate the use of AI in writers’ rooms.\\n\\n“Our proposal is that we not be required to adapt something that’s output by AI, and that the output of an AI not be considered writers’ work,” comedy writer <PERSON> told TechCrunch. “That doesn’t entirely exclude that technology from the production process, but it does mean that our working conditions wouldn’t be undermined by AI.”', metadata={'source': '/content/new_articles/05-03-ai-replace-tv-writers-strike.txt'}),\n", " Document(page_content='But the Alliance of Motion Picture and Television Producers (AMPTP) refused to engage with that proposal, instead offering a yearly meeting to discuss “advances in technology.”\\n\\n“When we first put [the proposal] in, we thought we were covering our bases — you know, some of our members are worried about this, the area is moving quickly, we should get ahead of it,<PERSON> <PERSON><PERSON> said. “We didn’t think it’d be a contentious issue because the fact of the matter is, the current state of the text-generation technology is completely incapable of writing any work that could be used in a production.”', metadata={'source': '/content/new_articles/05-03-ai-replace-tv-writers-strike.txt'}),\n", " Document(page_content='The text-generating algorithms behind tools like ChatGPT are not built to entertain us. Instead, they analyze patterns in massive datasets to respond to requests by determining what is most likely the desired output. So, ChatGPT knows that “Succession” is about an aging media magnate’s children fighting for control of his company, but it is unlikely to come up with any dialogue more nuanced than, “Who will be the successor? Me.”\\n\\nAccording to <PERSON>, a University of Chicago professor and faculty lead of art anti-mimicry tool Glaze, AI advancements can be used as an excuse for corporations to devalue human labor.\\n\\n“It’s to the advantage of the studios and bigger corporations to basically over-claim ChatGPT’s abilities, so they can, in negotiations at least, undermine and minimize the role of human creatives,” <PERSON> told TechCrunch. “I’m not sure how many people at these larger companies actually believe what they’re saying.”', metadata={'source': '/content/new_articles/05-03-ai-replace-tv-writers-strike.txt'}),\n", " Document(page_content='Con<PERSON> emphasized that some parts of a writer’s job are less obvious than literal scriptwriting but equally difficult to replicate with AI.\\n\\n“It’s going and meeting with the set decoration department that says, ‘Hey, we can’t actually build this prop that you’re envisioning, could you do this instead?’ and then you talk to them and go back and rewrite,” he said. “This is a human enterprise that involves working with other people, and that simply cannot be done by an AI.”\\n\\nComedian <PERSON><PERSON><PERSON> sees how AI could be useful in a writers’ room.\\n\\n“What we do in writers’ rooms is ultimately bouncing ideas around,” he told TechCrunch. “Even if it’s not good per se, an AI can throw together a script in however many minutes, compared to a week for human writers, and then it’s easier to edit than to write.”', metadata={'source': '/content/new_articles/05-03-ai-replace-tv-writers-strike.txt'}),\n", " Document(page_content='But even if there may be some promise for how humans can leverage this technology, he worries that studios see it merely as a way to demand more from writers over a shorter period of time.\\n\\n“It says to me that they’re only concerned with things being made,<PERSON> <PERSON> said. “They’re not concerned with people being paid for things being made.”\\n\\nWriters are also advocating to regulate the use of AI in entertainment because it remains a legal grey area.\\n\\n“It’s not clear that the work that it outputs is copyrightable, and a movie studio is not going to spend $50 to $100 million shooting a script that they don’t know that they own the copyright to,” <PERSON><PERSON> said. “So we figured this would be an easy give for [the AMPTP], but they completely stonewalled on it.”', metadata={'source': '/content/new_articles/05-03-ai-replace-tv-writers-strike.txt'}),\n", " Document(page_content='As the Writers Guild of America strikes for the first time since its historic 100-day action in 2007, <PERSON><PERSON> said he thinks the debate over AI technology is a “red herring.” With generative AI in such a rudimentary stage, writers are more immediately concerned with dismal streaming residuals and understaffed writing teams. Yet studios’ pushback on the union’s AI-related requests only further reinforces the core issue: The people who power Hollywood aren’t being paid their fair share.\\n\\n“I’m not worried about the technology,” <PERSON><PERSON> said. “I’m worried about the companies using technology, that is not in fact very good, to undermine our working conditions.”', metadata={'source': '/content/new_articles/05-03-ai-replace-tv-writers-strike.txt'}),\n", " Document(page_content='After Google cut all but three of the projects at its in-house incubator Area 120 and shifted it to work on AI projects across Google, one of the legacy efforts — coincidentally also an AI project — is now officially exiting to Google. Checks, an AI-powered tool to check mobile apps for compliance with various privacy rules and regulations, is moving into Google proper as a privacy product aimed at mobile developers.\\n\\nChecks originally made its debut in February 2022, although it was in development for some time before that. In its time at Area 120, it became one of the largest projects in the group, co-founders <PERSON> and <PERSON><PERSON> told me, with 10 people fully dedicated to it and a number of others contributing less formally. The founders’ job titles under Google will now be GM and Legal Lead, respectively, for Checks.', metadata={'source': '/content/new_articles/05-03-checks-the-ai-powered-data-protection-project-incubated-in-area-120-officially-exits-to-google.txt'}),\n", " Document(page_content='The amount that Google invested in the project was never disclosed, nor was the valuation of the exit to the parent company from the incubator, but the company has confirmed that there was a valuation and that it had grown since launch.\\n\\nThe company is not disclosing how many customers it has in total but notes that they are in the sectors of gaming, health, finance, education and retail. A sampling includes Miniclip, Rovio, Kongregate, Crayola and Yousician and in total the number of customers represented by its customers is over 3 billion.\\n\\nChecks will sit in the Developer X division. “What <PERSON>, <PERSON><PERSON>, and the entire Google Checks team have accomplished is one of the hardest things to do. Their focus on customer needs and nimble execution has served them well, and we’re eager to push ahead in this next phase of Checks,” said <PERSON><PERSON> in a statement.', metadata={'source': '/content/new_articles/05-03-checks-the-ai-powered-data-protection-project-incubated-in-area-120-officially-exits-to-google.txt'}),\n", " Document(page_content='Checks is one of those ideas that feels incredibly timely in that it speaks to an issue that’s growing in importance for consumers — who will vote with their feet when they feel that their privacy is in jeopardy. That in turn also puts more pressure on developers to get things right on the privacy front. App publishers these days are faced with a growing array of rules and regulations around data protection and privacy, not just rules like GDPR in Europe and CCPA in California (and the U.S.) set across different countries and jurisdictions, but also by companies that operate platforms within their own compliance efforts.\\n\\nWhen translated into how those regulations impact apps, there are potential issues at the front end, as well as on the back end, with how apps are coded and information moves from one place to another to consider. It’s a spaghetti bowl of issues, with fixes in one area potentially impacting another and making user experience less smooth to boot.', metadata={'source': '/content/new_articles/05-03-checks-the-ai-powered-data-protection-project-incubated-in-area-120-officially-exits-to-google.txt'}),\n", " Document(page_content='Checks leans on artificial intelligence and machine learning to scan apps and their code to identify areas where there might be violations of privacy and data protection rules, and provides remediation to suggest how to fix it — tasks that would be far more difficult for a team of humans to execute on their own. It’s already integrated with Google’s large language models and what it describes as “app understanding technologies” to power what it identifies and make suggestions for fixing issues.\\n\\nA dashboard lets users monitor and triage issues in the areas of compliance monitoring, data monitoring and store disclosure support (which is focused specifically on Google Play data safety). With the service also aimed at iOS developers, it’s not clear if it will add Apple App Store data safety at any point into that mix. All of this can be monitored in real time on live apps, as well as when they are still in development.', metadata={'source': '/content/new_articles/05-03-checks-the-ai-powered-data-protection-project-incubated-in-area-120-officially-exits-to-google.txt'}),\n", " Document(page_content='We have reached out to Google to get an update on the status of the other two projects that were spared all-out closure after Area 120 changed focus. They include video dubbing solution <PERSON>oud and an as-yet unnamed consumer product from the team that had previously built a bookmarking app Liist (which got acquired by Google).\\n\\nAs of right now, Liist’s co-founder <PERSON> still describes himself on LinkedIn as working on a stealth product at Area 120, and <PERSON><PERSON> is still using an Area 120 URL, so it seems that they remain in a holding pattern. (We’ll update this if and when we hear more.)', metadata={'source': '/content/new_articles/05-03-checks-the-ai-powered-data-protection-project-incubated-in-area-120-officially-exits-to-google.txt'}),\n", " Document(page_content='In the meantime, Area 120 itself is also seeing some revolving doors. <PERSON>, who was running Area 120 among other things and who messaged the big changes to staff in January, was out the door just a month later. He has now teamed up with <PERSON><PERSON> — another ex-Googler who has an outsized track record that includes being the CTO of Facebook and the co-CEO of Salesforce — to work on a mystery startup.\\n\\nUpdated with more information about Checks’ valuation and quote from Google.', metadata={'source': '/content/new_articles/05-03-checks-the-ai-powered-data-protection-project-incubated-in-area-120-officially-exits-to-google.txt'}),\n", " Document(page_content='Slack has evolved from a pure communications platform to one that enables companies to link directly to enterprise applications without having to resort to dreaded task switching. Today, at the Salesforce World Tour event in NYC, the company announced the next step in its platform’s evolution where it will be putting AI at the forefront of the user experience, making it easier to get information and build workflows.\\n\\nIt’s important to note that these are announcements, and many of these features are not available yet.\\n\\nRob <PERSON> says that rather than slapping on an AI cover, they are working to incorporate it in a variety of ways across the platform. That started last month with a small step, a partnership with OpenAI to bring a ChatGPT app into Slack, the first piece of a much broader vision for AI on the platform. That part is in beta at the moment.', metadata={'source': '/content/new_articles/05-04-slack-updates-aim-to-put-ai-at-the-center-of-the-user-experience.txt'}),\n", " Document(page_content='Today’s announcement involves several new integrations, including SlackGPT, the company’s own flavor of generative AI built on top of the Slack platform, which users and developers can tap into to build AI-driven experiences. The content in Slack provides a starting point for building models related to the platform.\\n\\n“We think Slack has a unique advantage when it comes to generative AI. A lot of the institutional knowledge on every topic, team, work item and project is already in Slack through the messages, the files and the clips that are shared every day,” he said.\\n\\nWhen you combine that with Slack’s Partner ecosystem and platform, customers have a lot of options for integrating AI into their workflows. He says that <PERSON><PERSON><PERSON> is thinking about this in three ways right now.', metadata={'source': '/content/new_articles/05-04-slack-updates-aim-to-put-ai-at-the-center-of-the-user-experience.txt'}),\n", " Document(page_content='“For starters, Slack is going to bring AI natively into the user experience with SlackGPT to help customers work faster, communicate better, learn faster, etc. And an example of that is AI-powered conversation summaries and writing assistance for composition that’s going to be directly available in Slack,” he said.\\n\\nThe former could as an example help employees get caught up on a long thread without having to read every message to get the gist of what was being discussed. The latter could help generate Slack messages or content for linked Slack applications. That’s a little less obvious use case. It’s probably easier to write a Slack message yourself unless it’s an automated message that’s part of a workflow, but if you are creating content for Slack Canvas, you could let the generative AI help you.', metadata={'source': '/content/new_articles/05-04-slack-updates-aim-to-put-ai-at-the-center-of-the-user-experience.txt'}),\n", " Document(page_content='Develo<PERSON> can get in on the action too, building AI steps into workflows, giving them the option of tapping into external apps and large language models to build generative AI experiences themselves. Just last week the company made its updated developer experience generally available, and this should make it easier to incorporate generative AI into the platform in customized ways, <PERSON><PERSON> says.\\n\\n“So this gives us the foundation to give users choice and flexibility to bring AI into their work in their business whenever they’re ready, and however they like. We’ve got 2,600 apps in the ecosystem right now, which includes a lot of the leading LLMs, and we see a lot of customers already choosing to integrate generative AI into Slack themselves,” he said.', metadata={'source': '/content/new_articles/05-04-slack-updates-aim-to-put-ai-at-the-center-of-the-user-experience.txt'}),\n", " Document(page_content='Finally EinsteinGPT, the Salesforce flavor of generative AI announced in March, will also be incorporated into Slack, letting employees ask questions directly about Salesforce content, like the users most likely to churn or the accounts most likely to buy, and so forth. This is really about more directly integrating Salesforce content into Slack, the company Salesforce paid $27 billion for a couple of years ago.\\n\\n“Slack is really becoming the conversational interface for Salesforce. So that’s bringing those EinsteinGPT-powered insights from the real-time customer data that exists in Salesforce into Slack to enrich every team’s understanding of the customer,” he said.', metadata={'source': '/content/new_articles/05-04-slack-updates-aim-to-put-ai-at-the-center-of-the-user-experience.txt'}),\n", " Document(page_content='As with most of the generative AI tooling we’ve seen being added to enterprise software, Slack is announcing these capabilities long before they release them, but this should give customers a sense of what’s coming, and how AI could be transforming Slack in the future. SlackGPT and EinsteinGPT integration are still in the development phase, but developers can build custom integrations with a variety of LLMs, today. Workflow Builder with SlackGPT AI connectors (which will allow customers to instantly connect ChatGPT or Claude to a workflow or build custom connectors that plug in their own LLMs) will be available this summer.', metadata={'source': '/content/new_articles/05-04-slack-updates-aim-to-put-ai-at-the-center-of-the-user-experience.txt'})]"]}, "metadata": {}, "execution_count": 13}]}, {"cell_type": "code", "source": ["len(text)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8y-9W90zxbZu", "outputId": "6eeb3665-885a-4652-b292-25f758c4c4ff"}, "execution_count": 14, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["233"]}, "metadata": {}, "execution_count": 14}]}, {"cell_type": "code", "source": ["text[1]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8N5s6787xwu2", "outputId": "11725a5f-d401-42db-fa8d-bfe1629ef3b0"}, "execution_count": 15, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Document(page_content='The event, which kicks off May 10 at 10 AM PT will be a big showcase for everything that’s on the way for Android 14. The company has, arguably, missed a step when it comes to the current generative AI land rush — hell, who could have predicted after all of these years that <PERSON> would finally have a moment?\\n\\nCEO Sundar Pichai will no doubt be making the case that the company continues to lead the way in the world of artificial intelligence. There’s always been a fair bit of the stuff at the event largely focused on practical real-world applications like mobile imaging and dealing with customer service. This year, however, I’d say it’s safe to say the company is going to go bonkers with the stuff.', metadata={'source': '/content/new_articles/05-05-google-i-o-2023-is-next-week-heres-what-were-expecting.txt'})"]}, "metadata": {}, "execution_count": 15}]}, {"cell_type": "code", "source": ["text[2]"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cm82PO16xz6l", "outputId": "4f6fa0d8-8504-4148-cada-2ea97a0f1a8c"}, "execution_count": 16, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Document(page_content='Hardware, meanwhile, is always a bit of a crapshoot at developer conferences. But after an off-year for the industry at large, a deluge of rumors are aligning, pointing to what’s likely to be an unusually consumer electronics-focused keynote. Given the fact that the last bit is my focus at TechCrunch, I’m going to start the list there.\\n\\nThe Pixel 7a is about as sure as bets get. Google has settled into a comfortable release cadence: releasing a flagship in the fall, followed by a budget device in the spring. The former is designed to be an ideal showcase for its latest mobile operating system and first-party silicon, while the latter makes some compromises for price, while maintaining as many of its predecessors as possible.\\n\\nHow to show excitement without shouting? Asking for a friend Coming to @Flipkart on 11th May. pic.twitter.com/il6GUx3MmR — Google India (@GoogleIndia) May 2, 2023', metadata={'source': '/content/new_articles/05-05-google-i-o-2023-is-next-week-heres-what-were-expecting.txt'})"]}, "metadata": {}, "execution_count": 16}]}, {"cell_type": "markdown", "source": ["## Creating DB"], "metadata": {"id": "q7TDFJZex-6e"}}, {"cell_type": "code", "source": ["from langchain import embeddings\n", "persist_directory = 'db'\n", "\n", "embedding = OpenAIEmbeddings()\n", "\n", "vectordb = Chroma.from_documents(documents=text,\n", "                                 embedding=embedding,\n", "                                 persist_directory=persist_directory)"], "metadata": {"id": "X6_rdqv-x2bu"}, "execution_count": 19, "outputs": []}, {"cell_type": "code", "source": ["# persiste the db to disk\n", "vectordb.persist()\n", "vectordb = None"], "metadata": {"id": "e1SRIkNRylRt"}, "execution_count": 20, "outputs": []}, {"cell_type": "code", "source": ["# Now we can load the persisted database from disk, and use it as normal.\n", "vectordb = Chroma(persist_directory=persist_directory,\n", "                  embedding_function=embedding)"], "metadata": {"id": "nJhjbCLhz7FV"}, "execution_count": 21, "outputs": []}, {"cell_type": "markdown", "source": ["## Make a retriever"], "metadata": {"id": "EJzFhIMR0kR1"}}, {"cell_type": "code", "source": ["retriever = vectordb.as_retriever()"], "metadata": {"id": "RmTn7rPv0epV"}, "execution_count": 22, "outputs": []}, {"cell_type": "code", "source": ["docs = retriever.get_relevant_documents(\"How much money did Microsoft raise?\")"], "metadata": {"id": "O-n5JFnC0enF"}, "execution_count": 29, "outputs": []}, {"cell_type": "code", "source": ["len(docs)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Ik462eWC09o0", "outputId": "e0b5479b-4605-4e02-a02e-04f3fb2b3d5e"}, "execution_count": 30, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["2"]}, "metadata": {}, "execution_count": 30}]}, {"cell_type": "code", "source": ["docs"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2bm7qjSY1Z_u", "outputId": "c88df129-7740-4135-d484-c2635f426f09"}, "execution_count": 31, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[Document(page_content='April 28, 2023\\n\\nVC firms including Sequoia Capital, Andreessen Horowitz, Thrive and K2 Global are picking up new shares, according to documents seen by TechCrunch. A source tells us Founders Fund is also investing. Altogether the VCs have put in just over $300 million at a valuation of $27 billion to $29 billion. This is separate to a big investment from Microsoft announced earlier this year, a person familiar with the development told TechCrunch, which closed in January. The size of Microsoft’s investment is believed to be around $10 billion, a figure we confirmed with our source.\\n\\nApril 25, 2023\\n\\nCalled ChatGPT Business, OpenAI describes the forthcoming offering as “for professionals who need more control over their data as well as enterprises seeking to manage their end users.”', metadata={'source': '/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt'}),\n", " Document(page_content='The amount that Google invested in the project was never disclosed, nor was the valuation of the exit to the parent company from the incubator, but the company has confirmed that there was a valuation and that it had grown since launch.\\n\\nThe company is not disclosing how many customers it has in total but notes that they are in the sectors of gaming, health, finance, education and retail. A sampling includes Miniclip, Rovio, Kongregate, Crayola and Yousician and in total the number of customers represented by its customers is over 3 billion.\\n\\nChecks will sit in the Developer X division. “What <PERSON>, <PERSON><PERSON>, and the entire Google Checks team have accomplished is one of the hardest things to do. Their focus on customer needs and nimble execution has served them well, and we’re eager to push ahead in this next phase of Checks,” said <PERSON><PERSON> in a statement.', metadata={'source': '/content/new_articles/05-03-checks-the-ai-powered-data-protection-project-incubated-in-area-120-officially-exits-to-google.txt'})]"]}, "metadata": {}, "execution_count": 31}]}, {"cell_type": "code", "source": ["retriever = vectordb.as_retriever(search_kwargs={\"k\": 2})"], "metadata": {"id": "ZW9MZ2wg0_Pe"}, "execution_count": 26, "outputs": []}, {"cell_type": "code", "source": ["retriever.search_type"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "Surg8Vn81KVu", "outputId": "2606b736-19fb-4598-abd2-b9eec90348ab"}, "execution_count": 27, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'similarity'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 27}]}, {"cell_type": "code", "source": ["retriever.search_kwargs"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "v0JU9HMY1MB9", "outputId": "3d0b3458-fbed-43de-9952-2c486f947928"}, "execution_count": 28, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'k': 2}"]}, "metadata": {}, "execution_count": 28}]}, {"cell_type": "markdown", "source": ["## Make a chain"], "metadata": {"id": "a-TDhGQR16p-"}}, {"cell_type": "code", "source": ["from langchain.chains import RetrievalQA"], "metadata": {"id": "Nn3kej131QrV"}, "execution_count": 32, "outputs": []}, {"cell_type": "code", "source": ["llm=OpenAI()"], "metadata": {"id": "r9P9QxwE2OUT"}, "execution_count": 33, "outputs": []}, {"cell_type": "code", "source": ["llm"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8CA-22em2a22", "outputId": "beb2e2fb-7cc0-4649-fe37-073dc4e113c0"}, "execution_count": 36, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["OpenAI(client=<class 'openai.api_resources.completion.Completion'>, openai_api_key='***************************************************', openai_api_base='', openai_organization='', openai_proxy='')"]}, "metadata": {}, "execution_count": 36}]}, {"cell_type": "code", "source": ["# create the chain to answer questions\n", "qa_chain = RetrievalQA.from_chain_type(llm=OpenAI(),\n", "                                  chain_type=\"stuff\",\n", "                                  retriever=retriever,\n", "                                  return_source_documents=True)"], "metadata": {"id": "beXjCxaN2IJG"}, "execution_count": 37, "outputs": []}, {"cell_type": "code", "source": ["## Cite sources\n", "def process_llm_response(llm_response):\n", "    print(llm_response['result'])\n", "    print('\\n\\nSources:')\n", "    for source in llm_response[\"source_documents\"]:\n", "        print(source.metadata['source'])"], "metadata": {"id": "WROKjzKL24IX"}, "execution_count": 39, "outputs": []}, {"cell_type": "code", "source": ["# full example\n", "query = \"How much money did Microsoft raise?\"\n", "llm_response = qa_chain(query)\n", "process_llm_response(llm_response)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lzu2VvyD2qLm", "outputId": "cfa73463-d8e0-4a30-ae05-2a8163f13541"}, "execution_count": 40, "outputs": [{"output_type": "stream", "name": "stdout", "text": [" Around $10 billion.\n", "\n", "\n", "Sources:\n", "/content/new_articles/05-03-chatgpt-everything-you-need-to-know-about-the-ai-powered-chatbot.txt\n", "/content/new_articles/05-03-checks-the-ai-powered-data-protection-project-incubated-in-area-120-officially-exits-to-google.txt\n"]}]}, {"cell_type": "code", "source": ["# break it down\n", "query = \"What is the news about <PERSON><PERSON>?\"\n", "llm_response = qa_chain(query)\n", "process_llm_response(llm_response)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "RSb1Fi-C28pl", "outputId": "e5377e79-1df5-4aaf-e99f-09832d11092a"}, "execution_count": 42, "outputs": [{"output_type": "stream", "name": "stdout", "text": [" Pando has raised $30 million in a Series B round, bringing its total raised to $45 million. The new capital will be put toward expanding Pando’s global sales, marketing and delivery capabilities.\n", "\n", "\n", "Sources:\n", "/content/new_articles/05-03-ai-powered-supply-chain-startup-pando-lands-30m-investment.txt\n", "/content/new_articles/05-03-ai-powered-supply-chain-startup-pando-lands-30m-investment.txt\n"]}]}, {"cell_type": "markdown", "source": ["## Deleteing the DB\n"], "metadata": {"id": "IdW5iRZL3XEG"}}, {"cell_type": "code", "source": ["!zip -r db.zip ./db"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sLPJpkKj3Suf", "outputId": "3b02d5f9-a81e-4a2a-91ab-df32ca790562"}, "execution_count": 43, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["  adding: db/ (stored 0%)\n", "  adding: db/chroma.sqlite3 (deflated 42%)\n", "  adding: db/340c51aa-f60a-48b6-b4fd-2fea61654443/ (stored 0%)\n", "  adding: db/340c51aa-f60a-48b6-b4fd-2fea61654443/length.bin (deflated 74%)\n", "  adding: db/340c51aa-f60a-48b6-b4fd-2fea61654443/data_level0.bin (deflated 100%)\n", "  adding: db/340c51aa-f60a-48b6-b4fd-2fea61654443/header.bin (deflated 61%)\n", "  adding: db/340c51aa-f60a-48b6-b4fd-2fea61654443/link_lists.bin (stored 0%)\n"]}]}, {"cell_type": "code", "source": ["# To cleanup, you can delete the collection\n", "vectordb.delete_collection()\n", "vectordb.persist()\n", "\n", "# delete the directory\n", "!rm -rf db/"], "metadata": {"id": "ff6o8fOl3ate"}, "execution_count": 44, "outputs": []}, {"cell_type": "markdown", "source": ["## Starting again loading the db"], "metadata": {"id": "q4nRn_TC3fy1"}}, {"cell_type": "code", "source": ["!unzip db.zip"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "W183alsq3dOH", "outputId": "8e53b1d6-e3fa-4b53-81fa-cbdc155a02b5"}, "execution_count": 45, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Archive:  db.zip\n", "   creating: db/\n", "  inflating: db/chroma.sqlite3       \n", "   creating: db/340c51aa-f60a-48b6-b4fd-2fea61654443/\n", "  inflating: db/340c51aa-f60a-48b6-b4fd-2fea61654443/length.bin  \n", "  inflating: db/340c51aa-f60a-48b6-b4fd-2fea61654443/data_level0.bin  \n", "  inflating: db/340c51aa-f60a-48b6-b4fd-2fea61654443/header.bin  \n", " extracting: db/340c51aa-f60a-48b6-b4fd-2fea61654443/link_lists.bin  \n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "pTnsWx3c3iZW"}, "execution_count": null, "outputs": []}]}