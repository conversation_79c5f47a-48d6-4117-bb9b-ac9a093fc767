{"cells": [{"cell_type": "markdown", "source": ["# Fine tuning classification example\n", "\n", "We will fine-tune an ada classifier to distinguish between the two sports: Baseball and Hockey."], "metadata": {"id": "5X3uhncefxit"}}, {"cell_type": "code", "source": ["!pip install --quiet openai"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "L1Ry15q6ix-6", "executionInfo": {"status": "ok", "timestamp": 1673075017330, "user_tz": -360, "elapsed": 13215, "user": {"displayName": "colab0 ineuron", "userId": "16851312232179065356"}}, "outputId": "1138049c-3153-460b-cba0-74a35cd7aa44"}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[?25l     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/54.7 KB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m54.7/54.7 KB\u001b[0m \u001b[31m6.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n", "  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "  Building wheel for openai (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n"]}]}, {"cell_type": "code", "source": ["!pip install --upgrade openai"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jtUkbnNcgr1B", "executionInfo": {"status": "ok", "timestamp": 1673075021303, "user_tz": -360, "elapsed": 3992, "user": {"displayName": "colab0 ineuron", "userId": "16851312232179065356"}}, "outputId": "d42445b4-0f59-44e6-c2de-3ef597186450"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Looking in indexes: https://pypi.org/simple, https://us-python.pkg.dev/colab-wheels/public/simple/\n", "Requirement already satisfied: openai in /usr/local/lib/python3.8/dist-packages (0.26.0)\n", "Requirement already satisfied: requests>=2.20 in /usr/local/lib/python3.8/dist-packages (from openai) (2.25.1)\n", "Requirement already satisfied: aiohttp in /usr/local/lib/python3.8/dist-packages (from openai) (3.8.3)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.8/dist-packages (from openai) (4.64.1)\n", "Requirement already satisfied: idna<3,>=2.5 in /usr/local/lib/python3.8/dist-packages (from requests>=2.20->openai) (2.10)\n", "Requirement already satisfied: urllib3<1.27,>=1.21.1 in /usr/local/lib/python3.8/dist-packages (from requests>=2.20->openai) (1.24.3)\n", "Requirement already satisfied: chardet<5,>=3.0.2 in /usr/local/lib/python3.8/dist-packages (from requests>=2.20->openai) (4.0.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.8/dist-packages (from requests>=2.20->openai) (2022.12.7)\n", "Requirement already satisfied: charset-normalizer<3.0,>=2.0 in /usr/local/lib/python3.8/dist-packages (from aiohttp->openai) (2.1.1)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0.0a3 in /usr/local/lib/python3.8/dist-packages (from aiohttp->openai) (4.0.2)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.8/dist-packages (from aiohttp->openai) (1.3.3)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.8/dist-packages (from aiohttp->openai) (1.8.2)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.8/dist-packages (from aiohttp->openai) (1.3.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.8/dist-packages (from aiohttp->openai) (6.0.3)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.8/dist-packages (from aiohttp->openai) (22.2.0)\n"]}]}, {"cell_type": "code", "source": ["# import yaml\n", "\n", "# def read_yaml(path_to_yaml: str) -> dict:\n", "#     with open(path_to_yaml) as yaml_file:\n", "#         content = yaml.safe_load(yaml_file)\n", "    \n", "#     return content\n"], "metadata": {"id": "An6I5HiQipKR"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# SECRET = read_yaml(\"/content/api_secret.yaml\")\n", "# OPENAI_API_SECRET = str(SECRET['OPENAI_API_SECRET'])\n", "# OPENAI_API_SECRET"], "metadata": {"id": "SBH6WGjE4adQ", "executionInfo": {"status": "ok", "timestamp": 1670137113383, "user_tz": -360, "elapsed": 4, "user": {"displayName": "colab0 ineuron", "userId": "16851312232179065356"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 36}, "outputId": "dd6d40ea-45af-4962-b695-3e6d8f763e54"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'***************************************************'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 15}]}, {"cell_type": "code", "source": ["import os\n", "import openai\n", "import getpass\n", "\n", "openai.api_key = getpass.getpass(prompt='Enter OpenAI API key:')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TZrrTnqBCyWp", "executionInfo": {"status": "ok", "timestamp": 1673075055013, "user_tz": -360, "elapsed": 9264, "user": {"displayName": "colab0 ineuron", "userId": "16851312232179065356"}}, "outputId": "6f7c1609-25b7-4a40-b210-ce5d18502e04"}, "execution_count": 3, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter OpenAI API key:··········\n"]}]}, {"cell_type": "code", "execution_count": 4, "source": ["from sklearn.datasets import fetch_20newsgroups\n", "import pandas as pd\n", "import openai\n", "\n", "categories = ['rec.sport.baseball', 'rec.sport.hockey']\n", "sports_dataset = fetch_20newsgroups(subset='train', shuffle=True, random_state=42, categories=categories)"], "outputs": [], "metadata": {"id": "buvzIWISfxiw", "executionInfo": {"status": "ok", "timestamp": *************, "user_tz": -360, "elapsed": 12175, "user": {"displayName": "colab0 ineuron", "userId": "16851312232179065356"}}}}, {"cell_type": "markdown", "source": [" ## Data exploration\n", " The newsgroup dataset can be loaded using sklearn. First we will look at the data itself:"], "metadata": {"id": "DUUK3Zn2fxix"}}, {"cell_type": "code", "execution_count": 5, "source": ["print(sports_dataset['data'][0])"], "outputs": [{"output_type": "stream", "name": "stdout", "text": ["From: <EMAIL> (Doug Bank)\n", "Subject: Re: Info needed for Cleveland tickets\n", "Reply-To: <EMAIL>\n", "Organization: Motorola Land Mobile Products Sector\n", "Distribution: usa\n", "Nntp-Posting-Host: ************\n", "Lines: 17\n", "\n", "In article <<EMAIL>>, <EMAIL> (matthew bohnert) writes:\n", "\n", "|> I'm going to be in Cleveland Thursday, April 15 to Sunday, April 18.\n", "|> Does anybody know if the Tribe will be in town on those dates, and\n", "|> if so, who're they playing and if tickets are available?\n", "\n", "The tribe will be in town from April 16 to the 19th.\n", "There are ALWAYS tickets available! (Though they are playing Toronto,\n", "and many Toronto fans make the trip to Cleveland as it is easier to\n", "get tickets in Cleveland than in Toronto.  Either way, I seriously\n", "doubt they will sell out until the end of the season.)\n", "\n", "-- \n", "Doug Bank                       Private Systems Division\n", "<EMAIL>          Motorola Communications Sector\n", "<EMAIL>                   Sc<PERSON>umburg, Illinois\n", "<EMAIL>       ************                    \n", "\n"]}], "metadata": {"id": "_lzK3ClQfxix", "outputId": "e10151f0-fb06-4ed8-93c1-032b9ef7281f", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": *************, "user_tz": -360, "elapsed": 12, "user": {"displayName": "colab0 ineuron", "userId": "16851312232179065356"}}}}, {"cell_type": "code", "source": ["sports_dataset['target_names']"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "uCMUwsMsg_TJ", "executionInfo": {"status": "ok", "timestamp": *************, "user_tz": -360, "elapsed": 24, "user": {"displayName": "colab0 ineuron", "userId": "16851312232179065356"}}, "outputId": "e6540488-9de7-4741-e49a-11a628917d64"}, "execution_count": 6, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['rec.sport.baseball', 'rec.sport.hockey']"]}, "metadata": {}, "execution_count": 6}]}, {"cell_type": "code", "execution_count": 7, "source": ["sports_dataset.target_names[sports_dataset['target'][0]]\n"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'rec.sport.baseball'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 7}], "metadata": {"id": "47bjGcCbfxiy", "outputId": "2d7e1144-3f8b-47a7-c9ef-49c1a2481a8e", "colab": {"base_uri": "https://localhost:8080/", "height": 35}, "executionInfo": {"status": "ok", "timestamp": 1673075082681, "user_tz": -360, "elapsed": 22, "user": {"displayName": "colab0 ineuron", "userId": "16851312232179065356"}}}}, {"cell_type": "code", "execution_count": 8, "source": ["len_all, len_baseball, len_hockey = len(sports_dataset.data), len([e for e in sports_dataset.target if e == 0]), len([e for e in sports_dataset.target if e == 1])\n", "print(f\"Total examples: {len_all}, Baseball examples: {len_baseball}, Hockey examples: {len_hockey}\")"], "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Total examples: 1197, Baseball examples: 597, Hockey examples: 600\n"]}], "metadata": {"id": "Pqxml7Drfxiy", "outputId": "75dfc779-f0bb-4d53-96d6-20d5d7b0671f", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1673075082682, "user_tz": -360, "elapsed": 21, "user": {"displayName": "colab0 ineuron", "userId": "16851312232179065356"}}}}, {"cell_type": "markdown", "source": ["One sample from the baseball category can be seen above. It is an email to a mailing list. We can observe that we have 1197 examples in total, which are evenly split between the two sports."], "metadata": {"id": "BNoev5Bhfxiz"}}, {"cell_type": "markdown", "source": ["## Data Preparation\n", "We transform the dataset into a pandas dataframe, with a column for prompt and completion. The prompt contains the email from the mailing list, and the completion is a name of the sport, either hockey or baseball. For demonstration purposes only and speed of fine-tuning we take only 300 examples. In a real use case the more examples the better the performance."], "metadata": {"id": "--7g-vatfxiz"}}, {"cell_type": "code", "execution_count": 9, "source": ["import pandas as pd\n", "\n", "labels = [sports_dataset.target_names[x].split('.')[-1] for x in sports_dataset['target']]\n", "texts = [text.strip() for text in sports_dataset['data']]\n", "df = pd.DataFrame(zip(texts, labels), columns = ['prompt','completion']) #[:300]\n", "df.head()"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                                              prompt completion\n", "0  From: <EMAIL> (Doug Bank)\\nSubject:...   baseball\n", "1  From: <EMAIL> (<PERSON>)...     hockey\n", "2  From: <EMAIL> (<PERSON>)\\nSubject: Re...   baseball\n", "3  From: <EMAIL> (david...     hockey\n", "4  Subject: Let it be Known\\nFrom: <ISSBTL@BYUVM....   baseball"], "text/html": ["\n", "  <div id=\"df-9088d5d8-8b9c-4021-95f5-e18c6f3aa21c\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>prompt</th>\n", "      <th>completion</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>From: <EMAIL> (Doug Bank)\\nSubject:...</td>\n", "      <td>baseball</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>From: <EMAIL> (<PERSON>)...</td>\n", "      <td>hockey</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>From: <EMAIL> (<PERSON>)\\nSubject: Re...</td>\n", "      <td>baseball</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>From: <EMAIL> (david...</td>\n", "      <td>hockey</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Subject: Let it be Known\\nFrom: &lt;ISSBTL@BYUVM....</td>\n", "      <td>baseball</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-9088d5d8-8b9c-4021-95f5-e18c6f3aa21c')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.39.39.9.59 1.41.59.51 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-9088d5d8-8b9c-4021-95f5-e18c6f3aa21c button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-9088d5d8-8b9c-4021-95f5-e18c6f3aa21c');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 9}], "metadata": {"id": "AgD1oWXofxiz", "outputId": "80668ebf-acdb-4d66-ba94-4139e0d98c76", "colab": {"base_uri": "https://localhost:8080/", "height": 206}, "executionInfo": {"status": "ok", "timestamp": 1673075087890, "user_tz": -360, "elapsed": 657, "user": {"displayName": "colab0 ineuron", "userId": "16851312232179065356"}}}}, {"cell_type": "markdown", "source": ["Both baseball and hockey are single tokens. We save the dataset as a jsonl file."], "metadata": {"id": "weMfQI74fxi0"}}, {"cell_type": "code", "execution_count": 10, "source": ["df.to_json(\"sports.jsonl\", orient='records', lines=True)"], "outputs": [], "metadata": {"id": "7IUMMEt0fxi0", "executionInfo": {"status": "ok", "timestamp": 1673075102002, "user_tz": -360, "elapsed": 606, "user": {"displayName": "colab0 ineuron", "userId": "16851312232179065356"}}}}, {"cell_type": "markdown", "source": ["### Data Preparation tool\n", "We can now use a data preparation tool which will suggest a few improvements to our dataset before fine-tuning. Before launching the tool we update the openai library to ensure we're using the latest data preparation tool. We additionally specify `-q` which auto-accepts all suggestions."], "metadata": {"id": "vvIaT15Bfxi0"}}, {"cell_type": "code", "execution_count": 11, "source": ["!openai tools fine_tunes.prepare_data -f sports.jsonl -q"], "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Analyzing...\n", "\n", "- Your file contains 1197 prompt-completion pairs\n", "- Based on your data it seems like you're trying to fine-tune a model for classification\n", "- For classification, we recommend you try one of the faster and cheaper models, such as `ada`\n", "- For classification, you can estimate the expected model performance by keeping a held out dataset, which is not used for training\n", "- There are 11 examples that are very long. These are rows: [134, 200, 281, 320, 404, 595, 704, 838, 1113, 1139, 1174]\n", "For conditional generation, and for classification the examples shouldn't be longer than 2048 tokens.\n", "- Your data does not contain a common separator at the end of your prompts. Having a separator string appended to the end of the prompt makes it clearer to the fine-tuned model where the completion should begin. See https://beta.openai.com/docs/guides/fine-tuning/preparing-your-dataset for more detail and examples. If you intend to do open-ended generation, then you should leave the prompts empty\n", "- The completion should start with a whitespace character (` `). This tends to produce better results due to the tokenization we use. See https://beta.openai.com/docs/guides/fine-tuning/preparing-your-dataset for more details\n", "\n", "Based on the analysis we will perform the following actions:\n", "- [Recommended] Remove 11 long examples [Y/n]: Y\n", "- [Recommended] Add a suffix separator `\\n\\n###\\n\\n` to all prompts [Y/n]: Y\n", "- [Recommended] Add a whitespace character to the beginning of the completion [Y/n]: Y\n", "- [Recommended] Would you like to split into training and validation set? [Y/n]: Y\n", "\n", "\n", "Your data will be written to a new JSONL file. Proceed [Y/n]: Y\n", "\n", "Wrote modified files to `sports_prepared_train.jsonl` and `sports_prepared_valid.jsonl`\n", "Feel free to take a look!\n", "\n", "Now use that file when fine-tuning:\n", "> openai api fine_tunes.create -t \"sports_prepared_train.jsonl\" -v \"sports_prepared_valid.jsonl\" --compute_classification_metrics --classification_positive_class \" baseball\"\n", "\n", "After you’ve fine-tuned a model, remember that your prompt has to end with the indicator string `\\n\\n###\\n\\n` for the model to start generating completions, rather than continuing with the prompt.\n", "Once your model starts training, it'll approximately take 30.8 minutes to train a `curie` model, and less for `ada` and `babbage`. Queue will approximately take half an hour per job ahead of you.\n"]}], "metadata": {"id": "qJr7ksxYfxi1", "outputId": "8b460f89-0791-4ef2-c2da-476025ccd8e4", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1673075113955, "user_tz": -360, "elapsed": 995, "user": {"displayName": "colab0 ineuron", "userId": "16851312232179065356"}}}}, {"cell_type": "markdown", "source": ["The tool helpfully suggests a few improvements to the dataset and splits the dataset into training and validation set.\n", "\n", "A suffix between a prompt and a completion is necessary to tell the model that the input text has stopped, and that it now needs to predict the class. Since we use the same separator in each example, the model is able to learn that it is meant to predict either baseball or hockey following the separator.\n", "A whitespace prefix in completions is useful, as most word tokens are tokenized with a space prefix.\n", "The tool also recognized that this is likely a classification task, so it suggested to split the dataset into training and validation datasets. This will allow us to easily measure expected performance on new data."], "metadata": {"id": "WsVSS0JOfxi1"}}, {"cell_type": "markdown", "source": ["## Fine-tuning\n", "The tool suggests we run the following command to train the dataset. Since this is a classification task, we would like to know what the generalization performance on the provided validation set is for our classification use case. The tool suggests to add `--compute_classification_metrics --classification_positive_class \" baseball\"` in order to compute the classification metrics.\n", "\n", "We can simply copy the suggested command from the CLI tool. We specifically add `-m ada` to fine-tune a cheaper and faster ada model, which is usually comperable in performance to slower and more expensive models on classification use cases. "], "metadata": {"id": "Z9J_O6qcfxi1"}}, {"cell_type": "code", "source": ["ls"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8KKcH0WLA8uY", "executionInfo": {"status": "ok", "timestamp": 1673074564201, "user_tz": -360, "elapsed": 8, "user": {"displayName": "colab0 ineuron", "userId": "16851312232179065356"}}, "outputId": "cadb901c-dd3f-46df-b6db-b913bf1de21f"}, "execution_count": 12, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[0m\u001b[01;34msample_data\u001b[0m/                   sport222_prepared_valid.jsonl\n", "sport222_prepared_train.jsonl  sport2.jsonl\n"]}]}, {"cell_type": "code", "source": ["!openai --api-key \"***************************************************\" api fine_tunes.create -t \"sports_prepared_train.jsonl\" -v \"sports_prepared_valid.jsonl\" --compute_classification_metrics --classification_positive_class \" baseball\" -m ada"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "A0Hqk1DrlVv6", "executionInfo": {"status": "ok", "timestamp": 1673075173598, "user_tz": -360, "elapsed": 8043, "user": {"displayName": "colab0 ineuron", "userId": "16851312232179065356"}}, "outputId": "49379aac-15ef-4d12-e31e-2cd87f96e028"}, "execution_count": 13, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\rUpload progress:   0% 0.00/1.52M [00:00<?, ?it/s]\rUpload progress: 100% 1.52M/1.52M [00:00<00:00, 3.41Git/s]\n", "Uploaded file from sports_prepared_train.jsonl: file-QbiyT62c0OP0PX86reODUECR\n", "Upload progress: 100% 387k/387k [00:00<00:00, 924Mit/s]\n", "Uploaded file from sports_prepared_valid.jsonl: file-PynsXbcT3bhoonOKAQZRHEEj\n", "Created fine-tune: ft-vIHtoqY1flAHzO8jOus69WiC\n", "Streaming events until fine-tuning is complete...\n", "\n", "(Ctrl-C will interrupt the stream, but not cancel the fine-tune)\n", "\n", "Stream interrupted (client disconnected).\n", "To resume the stream, run:\n", "\n", "  openai api fine_tunes.follow -i ft-vIHtoqY1flAHzO8jOus69WiC\n", "\n", "/usr/local/lib/python3.8/dist-packages/openai/cli.py:406: RuntimeWarning: coroutine 'FineTune.stream_events' was never awaited\n", "  cls._stream_events(resp[\"id\"])\n", "RuntimeWarning: Enable tracemalloc to get the object allocation traceback\n"]}]}, {"cell_type": "markdown", "source": ["The model is successfully trained in about ten minutes. We can see the model name is `ada:ft-openai-2021-07-30-12-26-20`, which we can use for doing inference."], "metadata": {"id": "cADKk6pWfxi1"}}, {"cell_type": "markdown", "source": ["### [Advanced] Results and expected model performance\n", "We can now download the results file to observe the expected performance on a held out validation set."], "metadata": {"id": "B_om7jTyfxi2"}}, {"cell_type": "code", "execution_count": null, "source": ["!openai --api-key OPENAI_API_SECRET api fine_tunes.results -i ft-2zaA7qi0rxJduWQpdvOvmGn3 > result.csv"], "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[organization=user-zcbbgciownltezxvlu9n684t] \u001b[91mError:\u001b[0m No fine-tune job: ft-2zaA7qi0rxJduWQpdvOvmGn3 (HTTP status code: 404)\n"]}], "metadata": {"id": "QKl4dyT8fxi2", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1669964615071, "user_tz": -360, "elapsed": 1573, "user": {"displayName": "colab0 ineuron", "userId": "16851312232179065356"}}, "outputId": "1c59b436-c019-40fd-e3aa-7cd30c10c93b"}}, {"cell_type": "code", "execution_count": null, "source": ["results = pd.read_csv('result.csv')\n", "results[results['classification/accuracy'].notnull()].tail(1)"], "outputs": [{"output_type": "error", "ename": "EmptyDataError", "evalue": "ignored", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mEmptyDataError\u001b[0m                            Traceback (most recent call last)", "\u001b[0;32m<ipython-input-22-9b3ed3a6748e>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mresults\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpd\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mread_csv\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'result.csv'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      2\u001b[0m \u001b[0mresults\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mresults\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'classification/accuracy'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mnotnull\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mtail\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.8/dist-packages/pandas/util/_decorators.py\u001b[0m in \u001b[0;36mwrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    309\u001b[0m                     \u001b[0mstacklevel\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mstacklevel\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    310\u001b[0m                 )\n\u001b[0;32m--> 311\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mfunc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    312\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    313\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0mwrapper\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.8/dist-packages/pandas/io/parsers/readers.py\u001b[0m in \u001b[0;36mread_csv\u001b[0;34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, squeeze, prefix, mangle_dupe_cols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, error_bad_lines, warn_bad_lines, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options)\u001b[0m\n\u001b[1;32m    584\u001b[0m     \u001b[0mkwds\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mupdate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkwds_defaults\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    585\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 586\u001b[0;31m     \u001b[0;32mreturn\u001b[0m \u001b[0m_read\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfilepath_or_buffer\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mkwds\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    587\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    588\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.8/dist-packages/pandas/io/parsers/readers.py\u001b[0m in \u001b[0;36m_read\u001b[0;34m(filepath_or_buffer, kwds)\u001b[0m\n\u001b[1;32m    480\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    481\u001b[0m     \u001b[0;31m# Create the parser.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 482\u001b[0;31m     \u001b[0mparser\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mTextFileReader\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfilepath_or_buffer\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwds\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    483\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    484\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0mchunksize\u001b[0m \u001b[0;32mor\u001b[0m \u001b[0miterator\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.8/dist-packages/pandas/io/parsers/readers.py\u001b[0m in \u001b[0;36m__init__\u001b[0;34m(self, f, engine, **kwds)\u001b[0m\n\u001b[1;32m    809\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0moptions\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"has_index_names\"\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mkwds\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"has_index_names\"\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    810\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 811\u001b[0;31m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_engine\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_make_engine\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mengine\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    812\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    813\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mclose\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.8/dist-packages/pandas/io/parsers/readers.py\u001b[0m in \u001b[0;36m_make_engine\u001b[0;34m(self, engine)\u001b[0m\n\u001b[1;32m   1038\u001b[0m             )\n\u001b[1;32m   1039\u001b[0m         \u001b[0;31m# error: Too many arguments for \"ParserBase\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1040\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mmapping\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mengine\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mf\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0moptions\u001b[0m\u001b[0;34m)\u001b[0m  \u001b[0;31m# type: ignore[call-arg]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1041\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1042\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0m_failover_to_python\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.8/dist-packages/pandas/io/parsers/c_parser_wrapper.py\u001b[0m in \u001b[0;36m__init__\u001b[0;34m(self, src, **kwds)\u001b[0m\n\u001b[1;32m     67\u001b[0m         \u001b[0mkwds\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"dtype\"\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mensure_dtype_objs\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkwds\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"dtype\"\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     68\u001b[0m         \u001b[0;32mtry\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 69\u001b[0;31m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_reader\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mparsers\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mTextReader\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mhandles\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mhandle\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwds\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     70\u001b[0m         \u001b[0;32mexcept\u001b[0m \u001b[0mException\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     71\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mhandles\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mclose\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.8/dist-packages/pandas/_libs/parsers.pyx\u001b[0m in \u001b[0;36mpandas._libs.parsers.TextReader.__cinit__\u001b[0;34m()\u001b[0m\n", "\u001b[0;31mEmptyDataError\u001b[0m: No columns to parse from file"]}], "metadata": {"id": "arZ5tm_Bfxi2", "outputId": "59db2fec-79ae-427d-9d80-5964aeeb735e", "colab": {"base_uri": "https://localhost:8080/", "height": 241}, "executionInfo": {"status": "error", "timestamp": 1669964684505, "user_tz": -360, "elapsed": 727, "user": {"displayName": "colab0 ineuron", "userId": "16851312232179065356"}}}}, {"cell_type": "markdown", "source": ["The accuracy reaches 99.6%. On the plot below we can see how accuracy on the validation set increases during the training run. "], "metadata": {"id": "nhqckAq9fxi2"}}, {"cell_type": "code", "execution_count": null, "source": ["results[results['classification/accuracy'].notnull()]['classification/accuracy'].plot()"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<AxesSubplot:>"]}, "metadata": {}, "execution_count": 12}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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"}, "metadata": {"needs_background": "light"}}], "metadata": {"id": "-21CZ2eYfxi2", "outputId": "79d21285-2648-4277-8172-e8287e692a4f"}}, {"cell_type": "markdown", "source": ["## Using the model\n", "We can now call the model to get the predictions."], "metadata": {"id": "fag2Gf9yfxi2"}}, {"cell_type": "code", "execution_count": null, "source": ["test = pd.read_json('sport2_prepared_valid.jsonl', lines=True)\n", "test.head()"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                                              prompt completion\n", "0  From: <EMAIL> (<PERSON>)...     hockey\n", "1  From: <EMAIL> (<PERSON> ...     hockey\n", "2  From: <EMAIL> (Geral...     hockey\n", "3  From: <EMAIL> (<PERSON>...   baseball\n", "4  From: <EMAIL> (<PERSON>)\\nSub...   baseball"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>prompt</th>\n", "      <th>completion</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>From: <EMAIL> (<PERSON>)...</td>\n", "      <td>hockey</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>From: <EMAIL> (<PERSON> ...</td>\n", "      <td>hockey</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>From: <EMAIL> (Geral...</td>\n", "      <td>hockey</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>From: <EMAIL> (<PERSON>...</td>\n", "      <td>baseball</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>From: <EMAIL> (<PERSON>)\\nSub...</td>\n", "      <td>baseball</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "metadata": {}, "execution_count": 13}], "metadata": {"id": "19Rhv2wqfxi2", "outputId": "0b4624ea-c9fc-49e2-cf15-82d63b0cf4dd"}}, {"cell_type": "markdown", "source": ["We need to use the same separator following the prompt which we used during fine-tuning. In this case it is `\\n\\n###\\n\\n`. Since we're concerned with classification, we want the temperature to be as low as possible, and we only require one token completion to determine the prediction of the model."], "metadata": {"id": "5Kfm_Kaofxi3"}}, {"cell_type": "code", "execution_count": null, "source": ["ft_model = 'ada:ft-openai-2021-07-30-12-26-20'\n", "res = openai.Completion.create(model=ft_model, prompt=test['prompt'][0] + '\\n\\n###\\n\\n', max_tokens=1, temperature=0)\n", "res['choices'][0]['text']\n"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["' hockey'"]}, "metadata": {}, "execution_count": 14}], "metadata": {"id": "in5P3vuxfxi3", "outputId": "b844d433-3347-496c-ca3a-65f4d20dc369"}}, {"cell_type": "markdown", "source": ["To get the log probabilities, we can specify logprobs parameter on the completion request"], "metadata": {"id": "TfDPa8qTfxi3"}}, {"cell_type": "code", "execution_count": null, "source": ["res = openai.Completion.create(model=ft_model, prompt=test['prompt'][0] + '\\n\\n###\\n\\n', max_tokens=1, temperature=0, logprobs=2)\n", "res['choices'][0]['logprobs']['top_logprobs'][0]"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<OpenAIObject at 0x7fe114e435c8> JSON: {\n", "  \" baseball\": -7.6311407,\n", "  \" hockey\": -0.0006307676\n", "}"]}, "metadata": {}, "execution_count": 15}], "metadata": {"id": "YKu1-64Wfxi3", "outputId": "3dc6edbb-f8e5-4a90-f74d-4a5ac66b1004"}}, {"cell_type": "markdown", "source": ["We can see that the model predicts hockey as a lot more likely than baseball, which is the correct prediction. By requesting log_probs, we can see the prediction (log) probability for each class."], "metadata": {"id": "5qcJ8zYsfxi3"}}, {"cell_type": "markdown", "source": ["### Generalization\n", "Interestingly, our fine-tuned classifier is quite versatile. Despite being trained on emails to different mailing lists, it also successfully predicts tweets."], "metadata": {"id": "4ETmcRUwfxi3"}}, {"cell_type": "code", "execution_count": null, "source": ["sample_hockey_tweet = \"\"\"Thank you to the \n", "@Canes\n", " and all you amazing Caniacs that have been so supportive! You guys are some of the best fans in the NHL without a doubt! Really excited to start this new chapter in my career with the \n", "@DetroitRedWings\n", " !!\"\"\"\n", "res = openai.Completion.create(model=ft_model, prompt=sample_hockey_tweet + '\\n\\n###\\n\\n', max_tokens=1, temperature=0, logprobs=2)\n", "res['choices'][0]['text']"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["' hockey'"]}, "metadata": {}, "execution_count": 16}], "metadata": {"id": "vHI7_gQMfxi3", "outputId": "ce1a4a05-12a2-43a8-9e7b-2afe988e0d86"}}, {"cell_type": "code", "execution_count": null, "source": ["sample_baseball_tweet=\"\"\"BREAKING: The Tampa Bay Rays are finalizing a deal to acquire slugger <PERSON> from the Minnesota Twins, sources tell ESPN.\"\"\"\n", "res = openai.Completion.create(model=ft_model, prompt=sample_baseball_tweet + '\\n\\n###\\n\\n', max_tokens=1, temperature=0, logprobs=2)\n", "res['choices'][0]['text']"], "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["' baseball'"]}, "metadata": {}, "execution_count": 17}], "metadata": {"id": "tZy_wOI7fxi4", "outputId": "18a5e685-4ce7-47df-af2a-4808eb772cdd"}}], "metadata": {"orig_nbformat": 4, "language_info": {"name": "python", "version": "3.7.3", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}, "kernelspec": {"name": "python3", "display_name": "Python 3.7.3 64-bit ('base': conda)"}, "interpreter": {"hash": "3b138a8faad971cc852f62bcf00f59ea0e31721743ea2c5a866ca26adf572e75"}, "colab": {"provenance": [], "machine_shape": "hm"}, "accelerator": "GPU", "gpuClass": "standard"}, "nbformat": 4, "nbformat_minor": 0}